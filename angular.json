{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"sakai": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "dist/"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/assets/demo/flags/flags.css", "node_modules/primeicons/primeicons.css", "node_modules/prismjs/themes/prism-coy.css", "src/styles.scss", "node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/@fortawesome/fontawesome-free/css/all.min.css", "node_modules/gridstack/dist/gridstack.min.css"], "scripts": ["node_modules/prismjs/prism.js", "node_modules/prismjs/components/prism-typescript.js", "node_modules/gridstack/dist/gridstack-all.js"], "allowedCommonJsDependencies": ["chart.js"], "browser": "src/main.ts"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "200kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "stage": {"budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "sakai:build:production"}, "development": {"buildTarget": "sakai:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "sakai:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "79144b3a-5aad-4977-9cb5-757e69b64212"}}