version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 22
    commands:
      - echo Installing dependencies
  pre_build:
    commands:
      - echo Starting build phase
      - npm install --legacy-peer-deps
  build:
    commands:
      - echo Building the application
      - npm run build
  post_build:
    commands:
      - echo Deploying to S3 Bucket $DEPLOYMENT_BUCKET
      - aws s3 cp --recursive ./dist/browser s3://$DEPLOYMENT_BUCKET
      - echo Invalidating CloudFront distribution $CLOUDFRONT_ID
      - aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_ID --paths /\*
