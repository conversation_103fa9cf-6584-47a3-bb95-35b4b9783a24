{"name": "sakai-ng", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve --no-hmr", "build": "ng build", "build:stage": "NODE_ENV=stage ng build --configuration stage", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "~19.2.14", "@angular/cdk": "^19.2.18", "@angular/common": "~19.2.14", "@angular/compiler": "~19.2.14", "@angular/core": "~19.2.14", "@angular/forms": "~19.2.14", "@angular/material": "^19.2.18", "@angular/platform-browser": "~19.2.14", "@angular/platform-browser-dynamic": "~19.2.14", "@angular/router": "~19.2.14", "@fortawesome/angular-fontawesome": "^1.0.0", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@primeng/themes": "^19.1.3", "@stomp/stompjs": "^7.1.1", "@types/google.maps": "^3.58.1", "chart.js": "^4.4.9", "file-saver": "^2.0.5", "gridstack": "11.3", "jwt-decode": "^4.0.0", "luxon": "^3.6.1", "moment": "^2.30.1", "moment-duration-format": "^2.3.2", "ng2-charts": "^8.0.0", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primeng": "^19.1.3", "prismjs": "^1.30.0", "rxjs": "^7.8.2", "sockjs-client": "^1.6.1", "tslib": "^2.8.1", "web-animations-js": "^2.3.2", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "~19.2.15", "@angular/cli": "~19.2.15", "@angular/compiler-cli": "~19.2.14", "@types/googlemaps": "^3.43.3", "@types/jasmine": "^5.1.8", "@types/node": "^22.15.30", "@types/prismjs": "^1.26.5", "angular-eslint": "^19.8.0", "eslint": "^9.28.0", "jasmine-core": "^5.7.1", "jasmine-spec-reporter": "^7.0.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "ts-node": "^10.9.2", "typescript": "5.8.3"}}