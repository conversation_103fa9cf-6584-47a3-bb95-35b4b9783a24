
.header{
    background-color: #363636;height:50px;color:#fff;box-sizing:border-box;z-index: 3;position:fixed;top:0px;left:0px;width: 100%;display: flex;justify-content: space-between;align-items:center;padding-left:10px;padding-right:10px;
    font-size: 22px;
}
.sidebar{
    width:250px;
    position:fixed;
    left:0px;
    top:0px;
    height:100%;
    text-align: center;
    background-color:  #363636;
    border-right: 2px solid  #363636;
    box-sizing: border-box;
    padding: 5px;
    padding-left: 15px;
    padding-right: 15px;
    overflow: scroll;  
    font-family:'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    z-index:1;
}
.sidebar::-webkit-scrollbar {
    width: 0;  /* Remove scrollbar space */
    background: transparent;  /* Optional: just make scrollbar invisible */
}

#Test
{
    position: absolute;
    visibility: hidden;
    height: auto;
    width: auto;
    white-space: nowrap; /* Thanks to <PERSON> comment */
}

.sidebarLabel{
    font-size: 15px;
    font-family:'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 10px;
}
.textWhite{
    color:white;
}

.sidebarInput{
    height:25px;
    width:80px;
    text-align: center;
    background-color: #f5f5f5;
}

.canvasContainer{
    width:calc(100% - 380px);margin-left: 310px;
}

.canvas{
    box-shadow: 0px 0px 3px 3px #56ffb5;
    background-color: #f5f5f5;
    margin-right:10px;
    margin-top:60px;
}
.componentArea{
    min-height:30px;
    border:0px solid #fff;
    margin-right:10px;
}
.componentAreaBtn{
    border:0px;
    padding:5px;
    padding-left: 10px;
    padding-right: 10px;
    cursor:pointer;
}
.componentContainer{
    border-bottom:1px solid #eee;
    width: /*100%;*/330px;
    box-sizing: border-box;
    padding-left: 10px;
    padding-right: 10px;
    padding-top:5px;
    padding-bottom:5px;
    color:white;
    font-family: sans-serif;
}
.componentLable{
    position: relative;top: 2px;
}
.componentInput{
    text-align: center;
    width:40px;
    height:25px;
}


#overlay {
    position: fixed;
    display: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    z-index: 2;
    cursor: pointer;
}

.overlayBack{
    width:600px;height:520px;background:#f5f5f5;margin:auto;margin-top: 60px;position:relative;
}
.closeOverlay{
    width:40px;height:40px;border-bottom-left-radius: 25px;background-color: #eee;text-align: center;box-sizing: border-box;
    font-family: sans-serif;font-size: 25px;
    position:absolute;right:0px;
    z-index:3;
}


.addComponentBtn{
    padding:10px;background:#fff;border:1px solid #000;box-shadow: 0px 0px 6px 3px #eee;
    margin-left:10px;margin-right: 10px;
}

.componentMainNDiv{
    /*position:absolute;top:50px;*/
    border:0px solid red;height:calc(100% - 70px);overflow:scroll;
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */  
}
.componentMainNDiv::-webkit-scrollbar{
    display: none;
}

.componentContainerOverlay{
    color: black;margin:auto;margin-top: 70px;
    background-color: #eee;
    width: 90%;padding:25px;
}

.componentSelector{
    background-color: #eee; color: #000; display: flex; align-items: center;justify-content:center;padding-left:10px;
    border:1px solid #ddd;
    padding-right:10px;
}

.addComponentOverlay{
    background-color: #eee;
    height: 30px;
    border:1px solid #eee; 
}

.rectfArea{
    width:100%;text-align: center;
}
.rectsArea{
    width:100%;margin:10px;display: flex;justify-content: space-between;align-items: center;
}

.saveBtnOverlay{
    background-color: #363636; height: 40px; margin-top: 20px; display: flex; justify-content: center; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; align-items: center; font-size: 18px; font-weight: bold; color: #ddd;position:absolute;bottom:0px;width:100%;
}

.editArea{
    background-color: #ddd;height:30px;position: fixed;top:50px;margin-left:240px;width : calc(100% - 248px);display: none;justify-content: space-between;align-items: center;    font-family:'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}