:host ::ng-deep {
    .layout-topbar {
        // display: flex;
        // align-items: center;
        // justify-content: flex-start;
        // background-color: #363636 !important;
        // // padding: 10px;
        // margin-left: 250px;
        // width: -webkit-fill-available;
        // padding-left: 16px;
        // padding-right: 16px;
        // //background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
        // height: 59px;

        display: flex;
        align-items: center;
        justify-content: flex-start;
        background-color: #363636 !important;
        margin-left: 0px;
        width: -webkit-fill-available;
        padding-left: 0px;
        padding-right: 16px;
        height: 59px;
    }

    .layout-topbar-left {
        display: flex;
        align-items: center;
        gap: 0px;
        width: 280px;
    }

    .layout-topbar .layout-topbar-button i {
        //font-size: 1.5rem;
        font-size: 15px;
        color: #56ffb5;
    }

    .layout-topbar .layout-topbar-button:hover {
        background-color: #363636 !important;

    }

    .layout-menu-button {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        // width: 50px;
        // height: 40px;
        width: 30px;
        height: 25px;
        border-radius: 50%;
        transition: background 0.3s ease-in-out, color 0.3s;
    }

    /* Default menu button color */
    .layout-menu-button i {
        color: rgb(255, 255, 255);
    }

    /* Highlight effect when clicked */
    .layout-menu-button.active {
        background-color: rgba(255, 255, 255, 0.2);
    }

    .layout-menu-button.active i {
        color: #ffffff;
        /* Highlighted icon color */
    }

    .layout-topbar-right {
        display: flex;
        align-items: center;
        position: relative;
        margin-left: auto;
    }

    .user-profile {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: #4caf50;
        color: white;
        font-weight: bold;
        border-radius: 50%;
        cursor: pointer;
    }

    .dropdown-card {
        position: absolute;
        top: 50px;
        right: 0;
        background: white;
        border-radius: 10px;
        box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
        padding: 15px;
        width: 250px;
        z-index: 1000;
    }

    .user-info {
        display: flex;
        align-items: center;
        padding: 10px;
    }

    .avatar-large {
        width: 50px;
        height: 50px;
        background: #4caf50;
        color: white;
        font-weight: bold;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
    }

    .user-details {
        display: flex;
        flex-direction: column;
    }

    .name {
        font-size: 16px;
        font-weight: bold;
        margin: 0;
    }

    .email {
        font-size: 14px;
        color: #666;
        margin: 0;
    }

    .dropdown-options {
        display: flex;
        flex-direction: column;
        margin-top: 10px;
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        border: none;
        background: transparent;
        width: 100%;
        text-align: left;
        cursor: pointer;
        transition: background 0.3s;
        font-size: 14px;
    }

    .dropdown-item:hover {
        background: #f5f5f5;
    }

    .divider {
        border: 0;
        height: 1px;
        background: #ddd;
        margin: 10px 0;
    }

    .logout-button {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        background: transparent;
        border: none;
        width: 100%;
        text-align: left;
        color: #e53935;
        font-weight: bold;
        cursor: pointer;
        transition: background 0.3s;
    }

    .logout-button:hover {
        background: #ddd;
    }
}