import { Directive, ElementRef, HostListener, Renderer2, Output, EventEmitter, inject } from '@angular/core';

@Directive({
    selector: '[appResizable]',
    standalone: false
})
export class ResizableDirective {
  private isResizing = false;

  @Output() widthChanged = new EventEmitter<number>();
  private el = inject(ElementRef);
  private renderer = inject(Renderer2);

  constructor() {
    const resizer = this.renderer.createElement('div');
    this.renderer.addClass(resizer, 'resizer');
    this.renderer.appendChild(this.el.nativeElement, resizer);
  }

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent) {
    if ((event.target as HTMLElement).classList.contains('resizer')) {
      this.isResizing = true;
      document.addEventListener('mousemove', this.onMouseMove);
      document.addEventListener('mouseup', this.onMouseUp);
    }
  }

  onMouseMove = (event: MouseEvent) => {
    if (this.isResizing) {
      const newWidth = event.clientX - this.el.nativeElement.offsetLeft;
      this.renderer.setStyle(this.el.nativeElement, 'width', `${newWidth}px`);
      this.widthChanged.emit(newWidth);  // Emit new width value
    }
  };

  onMouseUp = () => {
    this.isResizing = false;
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
  };
}
