import {inject} from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn } from '@angular/router';
import {AuthenticationService} from './authentication.service';
import {Location} from '@angular/common';

/**
 * Guard to activate or deactivate anonymous routes
 */
export const anonymousGuard: CanActivateFn = (route: ActivatedRouteSnapshot) => {
  const authenticationService = inject(AuthenticationService);
  const location = inject(Location);
  if (authenticationService.isAuthenticated()) {
      location.back();
      return false;
    }
    return true;
}