import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { AppRoutingModule } from './app-routing.module';
import { AccordionModule } from 'primeng/accordion';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { BadgeModule } from 'primeng/badge';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ButtonModule } from 'primeng/button';
import { DatePicker } from 'primeng/datepicker';
import { CardModule } from 'primeng/card';
import { CarouselModule } from 'primeng/carousel';
import { CascadeSelectModule } from 'primeng/cascadeselect';
import { ChartModule } from 'primeng/chart';
import { CheckboxModule } from 'primeng/checkbox';
import { ChipModule } from 'primeng/chip';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { ColorPickerModule } from 'primeng/colorpicker';
import { ContextMenuModule } from 'primeng/contextmenu';
import { DataViewModule } from 'primeng/dataview';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { SelectModule } from 'primeng/select';
import { FieldsetModule } from 'primeng/fieldset';
import { FileUploadModule } from 'primeng/fileupload';
import { GalleriaModule } from 'primeng/galleria';
import { ImageModule } from 'primeng/image';
import { InplaceModule } from 'primeng/inplace';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputMaskModule } from 'primeng/inputmask';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { InputTextModule } from 'primeng/inputtext';
import { TextareaModule } from 'primeng/textarea';
import { KnobModule } from 'primeng/knob';
import { ListboxModule } from 'primeng/listbox';
import { MegaMenuModule } from 'primeng/megamenu';
import { MenuModule } from 'primeng/menu';
import { MenubarModule } from 'primeng/menubar';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';
import { MultiSelectModule } from 'primeng/multiselect';
import { OrderListModule } from 'primeng/orderlist';
import { OrganizationChartModule } from 'primeng/organizationchart';
import { PopoverModule } from 'primeng/popover';
import { PaginatorModule } from 'primeng/paginator';
import { PanelModule } from 'primeng/panel';
import { PanelMenuModule } from 'primeng/panelmenu';
import { PasswordModule } from 'primeng/password';
import { PickListModule } from 'primeng/picklist';
import { ProgressBarModule } from 'primeng/progressbar';
import { RadioButtonModule } from 'primeng/radiobutton';
import { RatingModule } from 'primeng/rating';
import { RippleModule } from 'primeng/ripple';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { ScrollTopModule } from 'primeng/scrolltop';
import { SelectButtonModule } from 'primeng/selectbutton';
import { DrawerModule } from 'primeng/drawer';
import { SkeletonModule } from 'primeng/skeleton';
import { SliderModule } from 'primeng/slider';
import { SplitButtonModule } from 'primeng/splitbutton';
import { SplitterModule } from 'primeng/splitter';
import { StepsModule } from 'primeng/steps';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview'; // TODO: Need to be replaced with TabsModule
import { TagModule } from 'primeng/tag';
import { TerminalModule } from 'primeng/terminal';
import { TieredMenuModule } from 'primeng/tieredmenu';
import { TimelineModule } from 'primeng/timeline';
import { ToastModule } from 'primeng/toast';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { ToolbarModule } from 'primeng/toolbar';
import { TooltipModule } from 'primeng/tooltip';
import { TreeModule } from 'primeng/tree';
import { TreeSelectModule } from 'primeng/treeselect';
import { TreeTableModule } from 'primeng/treetable';
import { ScrollerModule } from 'primeng/scroller';
import { CommonModule, LocationStrategy, HashLocationStrategy } from '@angular/common';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AppComponent } from './app.component';
import { AppMainComponent } from './app.main.component';
import { AppTopBarComponent } from './app.topbar.component';
import { AppFooterComponent } from './app.footer.component';
import { AppConfigComponent } from './app.config.component';
import { AppMenuComponent } from './app.menu.component';
import { AppMenuitemComponent } from './app.menuitem.component';
import { DashboardComponentOld } from './components/dashboard-old/dashboard.component.old';
import { EmptyComponent } from './components/empty/empty.component';
import { CountryService } from './service/countryservice';
import { CustomerService } from './service/customerservice';
import { EventService } from './service/eventservice';
import { IconService } from './service/iconservice';
import { NodeService } from './service/nodeservice';
import { PhotoService } from './service/photoservice';
import { ProductService } from './service/productservice';
import { MenuService } from './service/app.menu.service';
import { ConfigService } from './service/app.config.service';
import { ManageClientsComponent } from './components/manage-clients/manage-clients.component';
import { ManageLocationsComponent } from './components/manage-locations/manage-locations.component';
import { ManageFleetsComponent } from './components/manage-fleets/manage-fleets.component';
import { AddLocationComponent } from './components/add-location/add-location.component';
import { AddFleetComponent } from './components/add-fleet/add-fleet.component';
import { ManageRolesComponent } from './components/manage-roles/manage-roles.component';
import { ManageUsersComponent } from './components/manage-users/manage-users.component';
import { AddUserComponent } from './components/add-user/add-user.component';
import { AddClientComponent } from './components/add-client/add-client.component';
import { ManageSpotsComponent } from './components/manage-spots/manage-spots.component';
import { LoginComponent } from './components/login/login.component';
import { ClientDetailsComponent } from './components/client-details/client-details.component';
import { RouterModule } from '@angular/router';
import { ManageJobsComponent } from './components/manage-jobs/manage-jobs.component';
import { AddJobComponent } from './components/add-job/add-job.component';
import { ManageMessagesComponent } from './components/manage-messages/manage-messages.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { ManageEntryExitComponent } from './components/manage-entry-exit/manage-entry-exit.component';
import { AppAlertModule } from './app-alert/app-alert.module';
import { AppLoaderModule } from './app-loader/app-loader.module';
import { ErrorModule } from './error-handling/error.module';
import { ErrorRouterModule } from './error-handling/error-router.module';
import { AppErrorHandler } from './error-handling/app-error.handler';
import { SecurityModule } from './security/security.module';
import { AppHttpInterceptor } from './security/app-http.interceptor';
import { CanvasToolComponent } from './canvas-tool/canvas-tool.component';
import { PreviewCanvasMapComponent } from './preview-canvas-map/preview-canvas-map.component';
import { PhoneMaskDirective } from './model/phone-mask.directive';
import { ForgotPasswordComponent } from './components/forgot-password/forgot-password.component';
import { NewUserComponent } from './components/new-user/new-user.component';
import { WebSocketService } from './web-socket.service';
import { ManageTrailerAuditComponent } from './components/manage-trailer-audit/manage-trailer-audit.component';
import { AddTrailerAuditComponent } from './components/add-trailer-audit/add-trailer-audit.component';
import { TermsComponent } from './components/terms/terms.component';
import { UserTermsComponent } from './components/user-terms/user-terms.component';
import { PrivacyPolicyComponent } from './components/privacy-policy/privacy-policy.component';
import { UserprivacyPolicyComponent } from './components/userprivacy-policy/userprivacy-policy.component';
import { RedirectPageComponent } from './components/redirect-page/redirect-page.component';
import { OverviewComponent } from './components/overview/overview.component';
import { DialogService } from 'primeng/dynamicdialog';
import { AddSpotsPopupComponent } from './components/add-spots-popup/add-spots-popup.component';
import { ManageMobileAppVersionComponent } from './components/manage-mobile-app-version/manage-mobile-app-version.component';
import { ManageUserAvailabilityComponent } from './components/manage-user-availability/manage-user-availability.component';
import { ManageOvertimeUsersComponent } from './components/manage-overtime-users/manage-overtime-users.component';
import { ManageTrailerTrackingComponent } from './components/manage-trailer-tracking/manage-trailer-tracking.component';
import { ManageReportComponent } from './components/manage-report/manage-report.component';
import { ManageReportMovesByDriverComponent } from './components/manage-report-moves-by-driver/manage-report-moves-by-driver.component';
import { ManageReportMovesBySpotterComponent } from './components/manage-report-moves-by-spotter/manage-report-moves-by-spotter.component';
import { ManageReportTrailerReportLoadstatusComponent } from './components/manage-report-trailer-report-loadstatus/manage-report-trailer-report-loadstatus.component';
import { ManageReportTrailerReportLocationComponent } from './components/manage-report-trailer-report-location/manage-report-trailer-report-location.component';
import { ManageReportTrailerReportEntryExitComponent } from './components/manage-report-trailer-report-entry-exit/manage-report-trailer-report-entry-exit.component';
import { ManageReportAssetInventoryComponent } from './components/manage-report-asset-inventory/manage-report-asset-inventory.component';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { BaseChartDirective, provideCharts, withDefaultRegisterables } from 'ng2-charts';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { DashboardComponent } from './components/dashboard-new/dashboard.component';
import { MatOptionModule } from '@angular/material/core';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { AverageMoveTimeComponent } from './components/widgets/average-move-time/average-move-time.component';
import { AverageNumberOfMovesComponent } from './components/widgets/total-moves/average-number-of-moves.component';
import { KeyMetricsComponent } from './components/widgets/key-metrics/key-metrics.component';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { AverageMoveTimeClientComponent } from './components/widgets/total-move-time-client/average-move-time-client.component';
import { AverageNumberOfMovesClientComponent } from './components/widgets/average-move-time-client/average-number-of-moves-client.component';
import { EmptyFullTrailerCountComponent } from './components/widgets/empty-full-trailer-count/empty-full-trailer-count.component';
import { ResizableDirective } from './resizableDirective/resizable.directive';
import { AverageTurnAroundTimeComponent } from './components/widgets/turn-around-time/average-turn-around-time/average-turn-around-time.component';
import { AverageDwellTimeComponent } from './components/widgets/average-dwell-time/average-dwell-time/average-dwell-time.component';
import { ManageTrailerAuditNewComponent } from './components/manage-trailer-audit-new/manage-trailer-audit-new.component';
import { AverageDockDwellTimeComponent } from './components/widgets/average-dock-dwell-time/average-dock-dwell-time.component';
import { AverageDockTurnaroundTimeComponent } from './components/widgets/average-dock-turnaround-time/average-dock-turnaround-time.component';
import { AverageOfMovesComponent } from './components/widgets/average-of-moves/average-of-moves.component';
import { DailyHourlyAverageMovesComponent } from './components/widgets/daily-hourly-average-moves/daily-hourly-average-moves.component';
import { providePrimeNG } from 'primeng/config';
import Aura from '@primeng/themes/aura';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { FluidModule } from 'primeng/fluid';
import { ManageReportTrailerAuditComponent } from './components/manage-report-trailer-audit/manage-report-trailer-audit.component';
import { ManageReportTrailerHistoryComponent } from './components/manage-report-trailer-history/manage-report-trailer-history.component';
import { ManageContactUsComponent } from './components/manage-contact-us/manage-contact-us.component';
import { DockUsageComponent } from './components/widgets/dock-usage/dock-usage.component';
import { InTransitMovesComponent } from './components/widgets/in-transit-moves/in-transit-moves.component';
import { WeeklyTotalSpotsComponent } from './components/widgets/weekly-total-spots/weekly-total-spots.component';
import { ResponsiveService } from './service/responsive.service';

@NgModule({
    declarations: [
        DashboardComponent,
        AverageMoveTimeComponent,
        AverageNumberOfMovesComponent,
        KeyMetricsComponent,
        AppComponent,
        AppMainComponent,
        AppTopBarComponent,
        AppFooterComponent,
        AppConfigComponent,
        AppMenuComponent,
        AppMenuitemComponent,
        DashboardComponentOld,
        EmptyComponent,
        ManageClientsComponent,
        ManageLocationsComponent,
        ManageFleetsComponent,
        AddLocationComponent,
        AddFleetComponent,
        ManageRolesComponent,
        ManageUsersComponent,
        AddUserComponent,
        AddClientComponent,
        ManageSpotsComponent,
        LoginComponent,
        ClientDetailsComponent,
        ManageJobsComponent,
        AddJobComponent,
        ManageMessagesComponent,
        ManageEntryExitComponent,
        CanvasToolComponent,
        PreviewCanvasMapComponent,
        PhoneMaskDirective,
        ForgotPasswordComponent,
        NewUserComponent,
        ManageTrailerAuditComponent,
        AddTrailerAuditComponent,
        TermsComponent,
        UserTermsComponent,
        PrivacyPolicyComponent,
        UserprivacyPolicyComponent,
        RedirectPageComponent,
        OverviewComponent,
        AddSpotsPopupComponent,
        ManageMobileAppVersionComponent,
        ManageUserAvailabilityComponent,
        ManageOvertimeUsersComponent,
        ManageTrailerTrackingComponent,
        ManageReportComponent,
        ManageReportMovesByDriverComponent,
        ManageReportMovesBySpotterComponent,
        ManageReportTrailerReportLoadstatusComponent,
        ManageReportTrailerReportLocationComponent,
        ManageReportTrailerReportEntryExitComponent,
        ManageReportAssetInventoryComponent,
        AverageMoveTimeClientComponent,
        AverageNumberOfMovesClientComponent,
        EmptyFullTrailerCountComponent,
        ResizableDirective,
        AverageTurnAroundTimeComponent,
        AverageDwellTimeComponent,
        ManageTrailerAuditNewComponent,
        AverageDockDwellTimeComponent,
        AverageDockTurnaroundTimeComponent,
        AverageOfMovesComponent,
        DailyHourlyAverageMovesComponent,
        ManageReportTrailerAuditComponent,
        ManageContactUsComponent,
        DockUsageComponent,
        InTransitMovesComponent,
        WeeklyTotalSpotsComponent,
        ManageReportTrailerHistoryComponent
    ],
    bootstrap: [AppComponent],
    imports: [
        DragDropModule,
        MatProgressSpinnerModule,
        MatOptionModule,
        MatFormFieldModule,
        MatToolbarModule,
        MatCardModule,
        MatSelectModule,
        BrowserModule,
        FontAwesomeModule,
        FormsModule,
        MatCheckboxModule,
        MatSlideToggleModule,
        MatButtonModule,
        MatInputModule,
        MatIconModule,
        RouterModule,
        CommonModule,
        BaseChartDirective,
        ReactiveFormsModule,
        AppRoutingModule,
        MatDialogModule,
        BrowserAnimationsModule,
        AccordionModule,
        AutoCompleteModule,
        AvatarModule,
        AvatarGroupModule,
        BadgeModule,
        BreadcrumbModule,
        ButtonModule,
        DatePicker,
        CardModule,
        CarouselModule,
        CascadeSelectModule,
        ChartModule,
        CheckboxModule,
        ChipModule,
        ConfirmPopupModule,
        ColorPickerModule,
        ContextMenuModule,
        DataViewModule,
        DialogModule,
        DividerModule,
        SelectModule,
        FieldsetModule,
        FileUploadModule,
        GalleriaModule,
        ImageModule,
        InplaceModule,
        InputNumberModule,
        InputMaskModule,
        ToggleSwitchModule,
        InputTextModule,
        TextareaModule,
        KnobModule,
        ListboxModule,
        MegaMenuModule,
        MenuModule,
        MatTooltipModule,
        MenubarModule,
        MessageModule,
        MessagesModule,
        MultiSelectModule,
        OrderListModule,
        OrganizationChartModule,
        PopoverModule,
        PaginatorModule,
        PanelModule,
        PanelMenuModule,
        PasswordModule,
        PickListModule,
        ProgressBarModule,
        RadioButtonModule,
        RatingModule,
        RippleModule,
        ScrollPanelModule,
        ScrollTopModule,
        SelectButtonModule,
        DrawerModule,
        SkeletonModule,
        SliderModule,
        SplitButtonModule,
        SplitterModule,
        StepsModule,
        TagModule,
        TableModule,
        TabViewModule, // TODO: Need to be replaced with TabsModule
        TerminalModule,
        TieredMenuModule,
        TimelineModule,
        ToastModule,
        ToggleButtonModule,
        ToolbarModule,
        TooltipModule,
        TreeModule,
        TreeSelectModule,
        TreeTableModule,
        ScrollerModule,
        AppAlertModule,
        AppLoaderModule,
        ErrorModule,
        ErrorRouterModule,
        SecurityModule,
        IconFieldModule,
        InputIconModule,
        FluidModule,
    ],
    providers: [
        {
            provide: HTTP_INTERCEPTORS,
            useClass: AppHttpInterceptor,
            multi: true
        },
        {
            provide: ErrorHandler,
            useClass: AppErrorHandler
        },
        {
             provide: LocationStrategy,
             useClass: HashLocationStrategy
         },
        CountryService,
        CustomerService,
        EventService,
        IconService,
        NodeService,
        PhotoService,
        ProductService,
        MenuService,
        ConfigService,
        WebSocketService,
        DialogService,
        ResponsiveService,
        provideHttpClient(withInterceptorsFromDi()),
        provideCharts(withDefaultRegisterables()),
        providePrimeNG({
            theme: {
                preset: Aura,
				options: {
                    darkModeSelector: false
                }
            }
        })
    ]
})
export class AppModule { }
