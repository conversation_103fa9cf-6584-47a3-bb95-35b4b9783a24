.loader {
  z-index: 10000;
  margin: -75px 0 0 -75px;
  animation: spin 2s linear infinite;
  border: 6px solid #f3f3f3;
  border-top: 6px solid #3498db;
  border-radius: 73%;
  margin: 0 auto;
  width: 75px;
  height: 75px;
}

.loader-overlay {
  z-index: 9999;
  position: fixed;
  /* Sit on top of the page content */
  display: block;
  /* Hidden by default */
  width: 100%;
  /* Full width (cover the whole page) */
  height: 100%;
  /* Full height (cover the whole page) */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  /* Black background with opacity */
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}