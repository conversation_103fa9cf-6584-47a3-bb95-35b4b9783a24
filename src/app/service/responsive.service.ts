import { computed, inject, Injectable } from "@angular/core";
import { BreakpointObserver } from "@angular/cdk/layout"
import { toSignal } from "@angular/core/rxjs-interop";

@Injectable()
export class ResponsiveService {
    readonly #BREAKPOINTS = {
        xSmall: '(max-width: 584px)',
        small: '(min-width: 585px) and (max-width: 1024px)',
        medium: '(min-width: 1025px) and (max-width: 1397px)',
        large: '(min-width: 1398px)'
    }
    #breakpointObserverService = inject(BreakpointObserver)
    #screenWidth = toSignal(this.#breakpointObserverService.observe([this.#BREAKPOINTS.xSmall, this.#BREAKPOINTS.small, this.#BREAKPOINTS.medium, this.#BREAKPOINTS.large]));

    public isExtraSmall = computed(() => this.#screenWidth()?.breakpoints[this.#BREAKPOINTS.xSmall]);
    public isSmall = computed(() => this.#screenWidth()?.breakpoints[this.#BREAKPOINTS.small]);
    public isMedium = computed(() => this.#screenWidth()?.breakpoints[this.#BREAKPOINTS.medium]);
    public isLarge = computed(() => this.#screenWidth()?.breakpoints[this.#BREAKPOINTS.large]);
}