:host ::ng-deep {
   
    
      .border-round {
        border-radius: 50% !important;
    }

    .text-xl {
        font-size: 1rem !important;
    }

    .bg-grey{
        background-color: #898989;
    }

    .text-grey{
        color: #898989;
    }

    .bg-orange{
        background-color: #FFAA00;
    }

    .text-orange{
        color: #FFAA00;
    }

    .bg-orange{
        background-color: #FFAA00;
    }

    .text-green{
        color: #0C7B00;
    }

    .bg-green{
        background-color: #0C7B00;
    }

    .text-blue{
        color: #2F468C;
    }

    .bg-blue{
        background-color: #2F468C;
    }

    .text-purple{
        color: #810BC5;
    }

    .bg-purple{
        background-color: #810BC5;
    }

    .text-brown{
        color: #65340D;
    }

    .bg-brown{
        background-color: #65340D;
    }

    .text-orange-dark{
        color: #FF6F00;
    }

    .bg-orange-dark{
        background-color: #FF6F00;
    }

    .text-cyan{
        color: #04A595;
    }

    .bg-cyan{
        background-color: #04A595;
    }

    .pointer{
        cursor: pointer;
    }

    /* Default flex direction is row for screens larger than lg and xl */
.flex-container {
    display: flex;
    flex-direction: row;
    
  }
  
  /* Change flex direction to column for screens smaller than lg and xl */
  @media (max-width: 991.98px) {
    .flex-container {
      flex-direction: column;
    }
  }

  @media (min-width: 1200px) {
    .card {
        min-height: 120px; /* Set your desired minimum height for xl screens here */
    }
}
    
@media (min-width: 1200px) {
    .custom-card {
        height: 560px; /* Set your desired minimum height for xl screens here */
    }
}
}