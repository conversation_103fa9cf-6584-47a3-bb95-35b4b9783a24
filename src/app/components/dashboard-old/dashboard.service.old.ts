import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { IpagedQuery } from 'src/app/model/IpagedQuery';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  messageCountSource = new Subject<number>();
  $messageCount = this.messageCountSource.asObservable();

  constructor(private http: HttpClient) { }

  sendMessageCount(count:number){
    this.messageCountSource.next(count);
  }

  getJobsCount(timePeriod?:string,clientId?:any):Observable<any>{
    let params = new HttpParams();
    if(timePeriod){
      params = params.append("timePeriod",timePeriod)
      // params = params.append("toDateTime",query.toDateTime.toString())
    }
    if(clientId){
      params = params.append("clientId",clientId.toString())
    }
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/statistics/jobs`,{params});
  }


  getMessagesCount(query?:IpagedQuery):Observable<any>{
    let params = new HttpParams();
    if(query){
      params = params.append("fromDateTime",query.fromDateTime.toString())
      params = params.append("toDateTime",query.toDateTime.toString())
    }
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/statistics/messages`);
  }

  getGeneralStatistics():Observable<any>{
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/statistics/totals`);
  }
}
