import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { IpagedQuery } from 'src/app/model/IpagedQuery';

@Injectable({
  providedIn: 'root'
})
export class DashboardServiceNew {

  messageCountSource = new Subject<number>();
  $messageCount = this.messageCountSource.asObservable();

  constructor(private http: HttpClient) { }

  sendMessageCount(count: number) {
    this.messageCountSource.next(count);
  }

  getJobsCount(timePeriod?: string, clientId?: any): Observable<any> {
    let params = new HttpParams();
    if (timePeriod) {
      params = params.append("timePeriod", timePeriod)
      // params = params.append("toDateTime",query.toDateTime.toString())
    }
    if (clientId) {
      params = params.append("clientId", clientId.toString())
    }
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/statistics/jobs`, { params });
  }


  getMessagesCount(query?: IpagedQuery): Observable<any> {
    let params = new HttpParams();
    if (query) {
      params = params.append("fromDateTime", query.fromDateTime.toString())
      params = params.append("toDateTime", query.toDateTime.toString())
    }
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/statistics/messages`);
  }

  getGeneralStatistics(): Observable<any> {
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/statistics/totals`);
  }

  getUserPreference(userId: string): Observable<Object> {
    let params = new HttpParams();
    params = params.append("userId", userId.toString())
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/preferences/widget`, { params });
  }
  saveUserPreference(userId: string, widgets: string[]): Observable<Object> {
    return this.http.post(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/preferences/widget?userId=${userId}`, widgets);
  }

  savelayout(userId: string, widgets: string): Observable<Object> {
    return this.http.post(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/preferences/layout?userId=${userId}`, widgets);
  }

  getLayout(userId: string): Observable<Object> {
    let params = new HttpParams();
    params = params.append("userId", userId.toString())
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/preferences/layout`, { params });
  }


  getTotalNumMoves(query: IpagedQuery): Observable<any> {
    var params = new HttpParams({
      fromObject: {
        isActive: query.isActive.valueOf(),
        page: query.page.toString(),
        size: query.size.toString(),
      }
    })
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs/total-moves`, { params });
  }

  getTotalNumMovesForClient(query: IpagedQuery, clientId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
        isActive: query.isActive.valueOf(),
        page: query.page.toString(),
        size: query.size.toString(),
      }
    })
    if (clientId) {
      params = params.append("clientId", clientId.toString())
    }
    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs/client/total-moves`, { params });
  }

  // getTotalAvgDwellTime(query: IpagedQuery, clientId: any): Observable<any> {
  //   var params = new HttpParams({
  //     fromObject: {
  //       isActive: query.isActive.valueOf(),
  //       page: query.page.toString(),
  //       size: query.size.toString(),
  //     }
  //   })
  //   if (clientId) {
  //     params = params.append("clientId", clientId.toString())
  //   }
  //   return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/api/dwell-time/client/`, { clientId.toString() });
  // }

  getTotalAvgDwellTime(clientId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }
    let url = `${APP_CONSTANTS.BASE_API_URL}/api/dwell-time/client/${clientId}`;
    return this.http.get(url, { params });
  }

  getTotalAvgDockDwellTime(clientId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }

    let url = `${APP_CONSTANTS.BASE_API_URL}/api/dwell-time/client/${clientId}/avgEmptiedTime`;
    return this.http.get(url, { params });
  }

  getTotalAvgDockTurnaroundTime(clientId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }

    let url = `${APP_CONSTANTS.BASE_API_URL}/api/turn-around-time/client/${clientId}`;
    return this.http.get(url, { params });
  }

  averageMoves(query: IpagedQuery, status?: any, clientId?: any, fromDate?: any, toDate?: any, fleetId?: any, sort?: any, assignedTo?: string, notes?: any, bucket?: string): Observable<any> {
    var params = new HttpParams({
      fromObject: {
        isActive: query.isActive.valueOf(),
        page: query.page.toString(),
        size: query.size.toString(),
      }
    })
    if (assignedTo) {
      params = params.append("assignedTo.uuid", assignedTo)
    }
    if (sort) {
      var sortBy = 'createdDate,' + sort;
      params = params.append("sort", sortBy)
    }
    else {
      if (bucket) {
        params = params.append("sort", 'queuePosition,asc')
      }
      else {
        params = params.append("sort", 'createdDate,desc')
      }
    }
    if (status) {
      params = params.append("status", status.toString())
    }
    if (clientId) {
      params = params.append("pickupLocation.client.uuid", clientId.toString())
    }
    if (fromDate && toDate) {
      params = params.append("fromDate", fromDate.toString())
      params = params.append("toDate", toDate.toString())
    }
    if (fleetId) {
      params = params.append("fleet.uuid", fleetId.toString())
    }
    if (notes) {
      // console.log("notes", notes);
      params = params.append("description", notes)
    }
    if (bucket) {
      params = params.append("bucket", bucket)
    }
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs/average-move-time`, { params });
  }


  deleteUserPreference(userId): Observable<any> {
    let params = new HttpParams();
    params = params.append("userId", userId.toString())

    return this.http.delete(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/preferences/widget`, { params });
  }

  viewAverageOfMoves(clientId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }
    let url = `${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/dashboard/avg-moves?clientId=${clientId}`;
    return this.http.get(url, { params });
  }

  dailyHourlyAverageMoves(clientId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }
    let url = `${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/dashboard/daily-hourly-move-average?clientId=${clientId}`;
    return this.http.get(url, { params });
  }

  viewInTransitSpots(clientId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }
    let url = `${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/dashboard/in-transit-spots?clientId=${clientId}`;
    return this.http.get(url, { params });
  }

  viewWeeklyTotalSpots(clientId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }
    let url = `${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/dashboard/weekly-total-spots?clientId=${clientId}`;
    return this.http.get(url, { params });
  }

  viewDockUsageSpots(locationId: any, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }
    let url = `${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/dashboard/spots-of-location?locationId=${locationId}`;
    return this.http.get(url, { params });
  }

  viewSpotsCount(clientId: any, spotId: boolean, isRefresh: boolean): Observable<any> {
    var params = new HttpParams({
      fromObject: {
      }
    });

    if (isRefresh) {
      params = params.append("isRefresh", isRefresh)
    }
    let url = `${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/dashboard/spot-counts?clientId=${clientId}&spotId=${spotId}`;
    return this.http.get(url, { params });
    // return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/dashboard/spot-counts?clientId=${clientId}&spotId=${spotId}`);
  }

}
