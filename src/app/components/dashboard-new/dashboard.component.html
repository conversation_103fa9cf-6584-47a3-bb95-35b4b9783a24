<mat-toolbar class="toolbar" [class.scrolled]="isScrolled">
    <span class="toolbar-title">My Dashboard</span>
    <div class="toolbar-buttons">
        <button class="refresh-button mr-9" [class.disabled]="isRefreshDisabled" [class.can-hover]="!isRefreshDisabled"
            (click)="!isRefreshDisabled && refreshData()"
            [matTooltip]="isRefreshDisabled ? 'Please wait ' + refreshCounter + 's before refreshing' : ''" mat-button
            matTooltipPosition="below">
            <mat-icon class="refreshIcon">refresh</mat-icon>
            <span class="cooldown-timer last-refresh-time normal-text">
                {{ lastRefreshTime }}
            </span>
            <span *ngIf="!isRefreshDisabled" class="cooldown-timer last-refresh-time hover-text">
                Refresh Data
            </span>
        </button>
        <p-select [options]="clients" [(ngModel)]="selectedClient" (onChange)="onClientChange()"
            optionLabel="clientName" optionValue="clientName" size="small"/>

        <p-button (click)="toggleWidgetManager($event)" [outlined]="true" severity="contrast">
            <span class="material-symbols-outlined mr-1">dashboard</span>
            Manage Widgets
            <span class="material-symbols-outlined ml-1">keyboard_arrow_down</span>
        </p-button>
    </div>
</mat-toolbar>
<div (click)="closeWidgetManager($event)" class="dashboard-container">

    <div #gridStack class="grid-stack">
        <div *ngFor="let widget of layout; trackBy: trackByFn" [attr.data-gs-id]="widget.id" [attr.gs-h]="widget.h"
            [attr.gs-w]="widget.w" [attr.gs-x]="widget.x" [attr.gs-y]="widget.y" [attr.gs-min-w]="2" [attr.gs-min-h]="2"
            class="grid-stack-item draggable-card">
            <div class="grid-stack-item-content">
                <ng-container *ngIf="widget.type === 'average-move-time'">
                    <app-average-moves [lineChartData]="lineChartData" [loading]="loadingAvgMoveTime"
                        [clientId]="selectedClientUuid" (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-average-moves>
                </ng-container>

                <ng-container *ngIf="widget.type === 'averageOfMoves'">
                    <app-average-of-moves [loading]="loadingAverageOfMoves" [averageMoves]="averageMoves"
                        (toggleWidgetEvent)="removeFromWidget($event)"></app-average-of-moves>
                </ng-container>

                <ng-container *ngIf="widget.type === 'weekly-total-spots'">
                    <app-weekly-total-spots [loading]="loadingWeeklyTotalSpots"
                        (toggleWidgetEvent)="removeFromWidget($event)"
                        [weeklyTotalSpots]="getWeeklyTotalSpotsData"></app-weekly-total-spots>
                </ng-container>
                <!-- <ng-container *ngIf="widget.type === 'average-number-of-moves' && showAverageNumberOfMoves && isLoggedInClient">
                    <app-average-move-time-client *ngIf="showAverageNumberOfMoves" [barChartData]="barChartDataForClientRole"
                        [chartOptionsBar]="chartOptionsBar" [loading]="loadingTotalMoveTimeForClientRole" (toggleWidgetEvent)="toggleWidget($event)">
                    </app-average-move-time-client>
                </ng-container> -->
                <!-- Average Number of Moves Widget -->
                <ng-container *ngIf="widget.type === 'average-number-of-moves'">
                    <app-average-number-of-moves [barChartData]="barChartData" [loading]="loadingTotalNumMoves"
                        (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-average-number-of-moves>
                </ng-container>

                <ng-container *ngIf="widget.type === 'dock-usage'">
                    <app-dock-usage #dockUsage *ngIf="showDockUsage" [clientId]="selectedClientUuid"
                        (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-dock-usage>
                </ng-container>

                <!-- 
                <ng-container *ngIf="widget.type === 'average-move-time' && showAverageMoveTime && isLoggedInClient">
                    <app-average-number-of-moves-client *ngIf="showAverageMoveTime"
                        [barChartData]="barChartDataOfAvgMovesForClientRole"
                        [chartOptionsBar]="chartOptionsBarForClient" [loading]="loadingAvgMovesForClientRole"
                        (toggleWidgetEvent)="toggleWidget($event)">
                    </app-average-number-of-moves-client>
                </ng-container> -->
                <ng-container *ngIf="widget.type === 'empty-full-count'">
                    <app-empty-full-trailer-count *ngIf="showEmptyFullTrailerCount" [clientId]="selectedClientUuid"
                        (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-empty-full-trailer-count>
                </ng-container>
                <!-- Key Metrics Widget -->
                <ng-container *ngIf="widget.type === 'key-metrics'">
                    <app-key-metrics [generalStatistics]="generalStatistics" [messageStatistics]="messageStatistics"
                        [jobStatistics]="jobStatistics" [isLoggedUserClient]="isLoggedInClient"
                        (toggleWidgetEvent)="removeFromWidget($event)" (routeToJobsEvent)="routeToManageJobs($event)">
                    </app-key-metrics>
                </ng-container>
                <ng-container *ngIf="widget.type === 'average-turn-around-time'">
                    <app-average-turn-around-time [lineChartData]="lineChartDataForAverTurnTime"
                        [loading]="loadingAvgMoveTurnTime" [clientId]="selectedClientUuid"
                        (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-average-turn-around-time>
                </ng-container>

                <ng-container *ngIf="widget.type === 'average-dwell-time'">
                    <app-average-dwell-time [barChartData]="barChartAvgDwellTimeData" [loading]="loadingAvgDwellTime"
                        (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-average-dwell-time>
                </ng-container>

                <ng-container *ngIf="widget.type === 'average-dock-dwell-time'">
                    <app-average-dock-dwell-time [barChartData]="barChartDockDwellTime"
                        [loading]="loadingAvgDockDwellTime" (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-average-dock-dwell-time>
                </ng-container>

                <ng-container *ngIf="widget.type === 'average-dock-turnaround-time'">
                    <app-average-dock-turnaround-time [barChartData]="barChartDockTurnaroundTime"
                        [loading]="loadingAvgDockTurnaroundTime" (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-average-dock-turnaround-time>
                </ng-container>

                <ng-container *ngIf="widget.type === 'daily-hourly-move-average'">
                    <app-daily-hourly-average-moves [statsByDay]="getDailyHourlyAverageMovesData"
                        [loading]="loadingDailyHourlyAverageMoves" (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-daily-hourly-average-moves>
                </ng-container>

                <ng-container *ngIf="widget.type === 'in-transit-moves'">
                    <app-in-transit-moves [inTransitData]="getinTransitSpotsData" [loading]="loadingInTransitMoves"
                        (toggleWidgetEvent)="removeFromWidget($event)">
                    </app-in-transit-moves>
                </ng-container>
            </div>
        </div>
    </div>


    <mat-card (click)="$event.stopPropagation()" *ngIf="widgetManagerOpen" class="widget-manager">
        <h2>Widgets
            <button (click)="restoreDefaults()">Restore Defaults</button>
        </h2>
        <div class="reorganise-swith-container">
            <div>Reorganize Automatically</div>
            <p-toggleswitch [(ngModel)]="reorganizeAutomatically" />
        </div>
        
        <!-- <mat-slide-toggle [(ngModel)]="reorganizeAutomatically">
            Reorganize Automatically
        </mat-slide-toggle> -->
        
        <div class="checkbox-group gap-1">
            <h4>Spots / Moves</h4>
            <ng-container *ngFor="let widget of widgets; let i = index">
                @if(checkWidgetAvailability(widget)) {
                    <div class="flex justify-content-start align-items-center">
                        <p-checkbox [inputId]="widget.id" [(ngModel)]="widget.enable" variant="outlined"
                            (onChange)="toggleWidget($event, widget.id)" [binary]="true"/>
                        <label [for]="widget.id" class="ml-2">{{ widget.label }}</label>
                    </div>
                }
            
                <!-- <mat-checkbox *ngIf="checkWidgetAvailability(widget)" [ngModel]="widget.enable"
                    (ngModelChange)="toggleWidget($event, widget)"
                    [disabled]="widget.isLoggedInClient !== undefined && widget.isLoggedInClient !== isLoggedInClient">
                    {{ widget.label }}
                </mat-checkbox> -->
            </ng-container>
        </div>
    </mat-card>
</div>