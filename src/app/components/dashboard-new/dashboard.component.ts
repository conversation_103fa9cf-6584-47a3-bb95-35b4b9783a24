import { DatePipe } from "@angular/common";
import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, HostListener, inject, OnInit, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { GridStack, GridStackOptions, GridStackWidget } from "gridstack";
import moment from "moment";
import { CheckboxChangeEvent } from "primeng/checkbox";
import { combineLatest, interval, of, Subscription } from "rxjs";
import { catchError } from "rxjs/operators";
import { APP_CONSTANTS } from "src/app/constants/app.constants";
import { ErrorService } from "src/app/error-handling/error.service";
import { IpagedQuery } from "src/app/model/IpagedQuery";
import { TokenService } from "src/app/security/token.service";
import { DashboardService } from "../dashboard-old/dashboard.service.old";
import { ManageClientsService } from "../manage-clients/manage-clients.service";
import { ManageFleetsService } from "../manage-fleets/manage-fleets.service";
import { ManageJobsService } from "../manage-jobs/manage-jobs.service";
import { DashboardServiceNew } from "./dashboard.service";
import { ResponsiveService } from "src/app/service/responsive.service";


const widgetStack = [
  // Row 1
  { id: 'key-metrics', type: 'key-metrics', label: 'Key Metrics', x: 0, y: 0, w: 12, h: 2, enable: false },

  // Row 2 split into 3 equal parts
  { id: 'averageOfMoves', type: 'averageOfMoves', label: 'Average # of Moves', x: 0, y: 2, w: 4, h: 4, enable: false },
  { id: 'weekly-total-spots', type: 'weekly-total-spots', label: 'Weekly Total Spots', x: 4, y: 2, w: 4, h: 4, enable: false },
  { id: 'dock-usage', type: 'dock-usage', label: 'Dock Usage', x: 8, y: 2, w: 4, h: 4, enable: false },

  // Remaining layout (from row 3 onward)
  { id: 'average-move-time', type: 'average-move-time', label: 'Average Move Time - Driver/Spotter', x: 0, y: 6, w: 6, h: 4, enable: false },
  { id: 'average-number-of-moves', type: 'average-number-of-moves', label: 'Total Number of Moves', x: 6, y: 6, w: 6, h: 4, enable: false },

  { id: 'empty-full-count', type: 'empty-full-count', label: 'Trailer Status', x: 0, y: 10, w: 6, h: 4, enable: false },
  { id: 'average-turn-around-time', type: 'average-turn-around-time', label: 'Average Turnaround Time', x: 6, y: 10, w: 6, h: 4, enable: false },

  { id: 'average-dwell-time', type: 'average-dwell-time', label: 'Average Dwell Time', x: 0, y: 14, w: 6, h: 4, enable: false },
  { id: 'average-dock-dwell-time', type: 'average-dock-dwell-time', label: 'Average Dock Dwell Time', x: 6, y: 14, w: 6, h: 4, enable: false },

  { id: 'average-dock-turnaround-time', type: 'average-dock-turnaround-time', label: 'Average Dock Turnaround Time', x: 0, y: 18, w: 6, h: 4, enable: false },
  { id: 'daily-hourly-move-average', type: 'daily-hourly-move-average', label: 'Daily Hourly Average Moves', x: 0, y: 22, w: 12, h: 4, enable: false },
  { id: 'in-transit-moves', type: 'in-transit-moves', label: 'In Transit Moves', x: 0, y: 26, w: 12, h: 4, enable: false }
]

export interface UserStats {
  userDto: string;
  type: string;
  clientName: string;
  clientUuid: string;
  dayMoves: number;
  weekMoves: number;
  monthMoves: number;
}

interface CustomGridStackOptions extends GridStackOptions {
  oneColumnSize?: number;
}

@Component({
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.scss"],
  providers: [DatePipe],
  standalone: false
})

export class DashboardComponent implements OnInit, AfterViewInit {

  responsiveService = inject(ResponsiveService);
  isScrolled = false;
  getAccessToken: any = [];
  @ViewChild('gridStack', { static: true }) gridContainer: ElementRef;
  @ViewChild('dockUsage') dockUsageComp: any;
  grid: GridStack;
  layout: GridStackWidget[] = [];
  clients: any[] = [];
  userId: any;
  selectedClient: string = "";
  selectedClientUuid: string = "";
  defaultCliennt: any;
  widgetManagerOpen = false;
  reorganizeAutomatically = false;
  showAverageMoveTime: boolean = true;
  showAverageTurnaroundTime: boolean = true;
  showAverageNumberOfMoves: boolean = true;
  showDockUsage: boolean = true;
  showAverageDwellTime: boolean = true;
  showAverageDockDwellTime: boolean = true;
  showAverageDockTurnaroundTime: boolean = true;
  showDailyHourlyAverageMoves: boolean = false;
  isDailyHourlyAverageMovesAllowed: boolean = false;
  showKeyMetrics = true;
  showAverageOfMoves: boolean = true;
  showWeeklyTotalSpots: boolean = true;
  showEmptyFullTrailerCount = true;
  showInTransitMoves: boolean = true;
  getinTransitSpotsData: any;
  getWeeklyTotalSpotsData: any;
  keyMetricsData: any = {};
  subscription = new Subscription();
  fleetList = [];
  totalMoves: any;
  emptyCount = 0;
  fullCount = 0;
  isLoggedInClient: boolean = false;
  userPrefernce: any;
  totalRecordsOpen: any;
  totalRecordsIntransit: any;
  totalRecordsCompleted: any;
  totalRecordsDriverQueue: any;
  totalRecordsSpotterQueue: any;
  totalRecordsException: any;
  todayQuery: IpagedQuery;
  userRoles: any;
  unreadMessage: boolean = false;
  generalStatistics = {
    totalClients: 0,
    totalTrailers: 0,
    totalTrucks: 0,
    totalUsers: 0,
  };
  messageStatistics = {
    newCount: 0,
    readCount: 0,
  };
  isRefreshDisabled = false;
  refreshCounter = 60; // Cooldown time in seconds
  refreshInterval?: Subscription;
  lastRefreshTime: string = "0 minutes ago";
  lastRefreshTimestamp?: number;
  private timeUpdateInterval?: any;
  jobStatistics = {
    activeJobs: 0,
    pendingJobs: 0,
    completedJobsOneMonth: 0,
    completedJobsToday: 0,
  };
  clientQuery: IpagedQuery;
  lineChartData: any = {};
  lineChartDataForAverTurnTime: any = {};
  barChartData: any = {};
  barChartAvgDwellTimeData: any = {};
  barChartDataForClientRole: any = {};
  barChartDockDwellTime: any = {};
  barChartDockTurnaroundTime: any = {};
  barChartDataOfAvgMovesForClientRole: any = {};
  webSocket: WebSocket;
  averageMoves: any;
  getDailyHourlyAverageMovesData: any;
  chartOptionsBarForClient = {
    responsive: true,
    plugins: {
      tooltip: {
        enabled: true,
        callbacks: {
          label: function (tooltipItem: any) {
            return `Value: ${tooltipItem.raw}`;
          },
        },
      },
      legend: {
        display: true,
      },
    },
    scales: {
      y: {
        grid: {
          display: true,
          drawBorder: false,
        },
        title: {
          display: true,
          text: "Average Move Time (Minutes)",
        },
      },
    },
    elements: {
      bar: {
        borderWidth: 1,
        barThickness: 10,
      },
    },
  };
  emptyFullChartData: any = {};
  showEmptyFullChart = true;
  showTaskDistribution = true;
  loadingAvgMoveTime: boolean = true;
  loadingAvgMoveTurnTime: boolean = true;
  loadingTotalMoveTimeForClientRole: boolean = true;
  loadingAvgMovesForClientRole: boolean = true;
  backupLayout: any[] = [];
  loadingTotalNumMoves: boolean = true;
  loadingAvgDwellTime: boolean = true;
  loadingAvgDockDwellTime: boolean = true;
  loadingAvgDockTurnaroundTime: boolean = true;
  loadingAverageOfMoves: boolean = true;
  loadingDailyHourlyAverageMoves: boolean = true;
  loadingInTransitMoves: boolean = true;
  loadingWeeklyTotalSpots: boolean = true;
  widgets: any[] = [];
  isRefresh: boolean = false;
  cdRef = inject(ChangeDetectorRef);
  widgetAvailability = {};

  constructor(
    private manageJobsService: ManageJobsService,
    private dashboardService: DashboardService,
    private dashboardServiceNew: DashboardServiceNew,
    private manageClientsService: ManageClientsService,
    private manageFleetsService: ManageFleetsService,
    private tokenService: TokenService,
    private router: Router,
    private errorService: ErrorService,
    private datePipe: DatePipe
  ) {
    this.webSocket = new WebSocket(APP_CONSTANTS.SOCKET_URL);
  }

  ngOnInit(): void {
    this.userId = this.tokenService.getUserId();
    this.userRoles = this.tokenService.getUserRoles();
    this.getAccessToken = JSON.parse(localStorage.getItem('access_token'));
    const allowedRoles = ['ROLE_ADMIN', 'ROLE_SUPERVISOR', 'ROLE_IT'];
    if (allowedRoles.includes(this.getAccessToken.roles[0])) {
      this.showDailyHourlyAverageMoves = true;
      this.isDailyHourlyAverageMovesAllowed = true;
    }
    this.updateWidgetAvailability();
    this.layout = this.getDefaultLayout();
    this.loadLastRefreshTime();
    this.startTimeUpdateInterval();
    this.clientQuery = { isActive: true, size: 1000, page: 0 };
    const storeSelectedClientIdLists = JSON.parse(localStorage.getItem("storeSelectedClientIdLists"));
    const storedClient = JSON.parse(localStorage.getItem("setDefaultClientId"));
    if (!storeSelectedClientIdLists) {
      this.isRefresh = true;
    }
    else {
      const isRefreshEnable = JSON.parse(localStorage.getItem("storeSelectedClientIdLists")).find(item => item.clientName === storedClient.clientName);
      this.isRefresh = isRefreshEnable.isRefresh;
    }
    this.getClientList(this.clientQuery);
    let twentyFourHours = moment().add(-24, "hours").format("yyyy-MM-DD HH:mm");
    let nowTime = moment().format("yyyy-MM-DD HH:mm");
    this.todayQuery = {
      fromDateTime: twentyFourHours,
      toDateTime: nowTime,
    };
    this.getAllStatistics();
    this.startRefreshCooldown();
    //localStorage.setItem("setWidgetLocalStorage", JSON.stringify([]));
  }

  // @HostListener('window:scroll', [])
  // onWindowScroll() {
  //   const scrollTop = window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0;
  //   this.isScrolled = scrollTop > 0; // Set to true if scrolled down even 1px
  // }

  @HostListener('window:resize', [])
  onResize(event: Event): void {
    this.layout = this.getDefaultLayout();
  }

  ngAfterViewInit(): void {
    const options: CustomGridStackOptions = {
      column: 12,
      cellHeight: 'auto',
      float: false,
      oneColumnSize: this.getColumnsBasedOnWidth(),
      margin: 10,

      draggable: {
        handle: ".grid-stack-item"
      },
      resizable: {
        handles: "e, w, s, se, sw",
        autoHide: false
      }
    };

    this.grid = GridStack.init(options);
    this.grid.enableMove(true);
    this.grid.enableResize(true);

    this.grid.on("dragstop", () => {
      this.grid.compact();
      this.rearrangeGrid();
    });

    this.grid.on("resizestop", () => {
      this.grid.compact();
      this.rearrangeGrid();
      this.adjustGridHeight();
    });

    window.addEventListener("resize", () => {
      this.grid.column(this.getColumnsBasedOnWidth());
      this.adjustGridHeight();
      this.rearrangeGrid();
    });

    setTimeout(() => {
      this.restoreLayout();
      this.restoreDefaults();
      this.adjustGridHeight();
    }, 500);
    this.manageWidgetDropDown();
  }

  updateWidgetAvailability() {
    this.widgetAvailability = {
      'key-metrics': this.showKeyMetrics,

      // Row 2 split into 3 equal parts
      'averageOfMoves': this.showAverageOfMoves,
      'weekly-total-spots': this.showWeeklyTotalSpots,
      'dock-usage': this.showDockUsage,

      // Remaining layout (from row 3 onward)
      'average-move-time': this.showAverageMoveTime,
      'average-number-of-moves': this.showAverageNumberOfMoves,

      'empty-full-count': this.showEmptyFullTrailerCount,
      'average-turn-around-time': this.showAverageTurnaroundTime,

      'average-dwell-time': this.showAverageDwellTime,
      'average-dock-dwell-time': this.showAverageDockDwellTime,

      'average-dock-turnaround-time': this.showAverageDockTurnaroundTime,
      'daily-hourly-move-average': this.showDailyHourlyAverageMoves,
      'in-transit-moves': this.showInTransitMoves
    }
  }

  manageWidgetDropDown() {
    this.widgets = this.getAvailableWidgets();
  }

  getAvailableWidgets() {
    return widgetStack.map(widget => {
      const updatedWidget = { ...widget };
      return { ...updatedWidget, enable: this.widgetAvailability[widget.type] };
    })
  }

  getDefaultLayout(): any[] {
    let defaultLayout = [];
    // let defaultLayout = [
    //   // Row 1
    //   { id: 'key-metrics', type: 'key-metrics', label: 'Key Metrics', x: 0, y: 0, w: 12, h: 2, enable: this.showKeyMetrics },

    //   // Row 2 split into 3 equal parts
    //   { id: 'averageOfMoves', type: 'averageOfMoves', label: 'Average # of Moves', x: 0, y: 2, w: 4, h: 4, enable: this.showAverageOfMoves },
    //   { id: 'weekly-total-spots', type: 'weekly-total-spots', label: 'Weekly Total Spots', x: 4, y: 2, w: 4, h: 4, enable: this.showWeeklyTotalSpots },
    //   { id: 'dock-usage', type: 'dock-usage', label: 'Dock Usage', x: 8, y: 2, w: 4, h: 4, enable: this.showDockUsage },

    //   // Remaining layout (from row 3 onward)
    //   { id: 'average-move-time', type: 'average-move-time', label: 'Average Move Time - Driver/Spotter', x: 0, y: 6, w: 6, h: 4, enable: this.showAverageMoveTime },
    //   { id: 'average-number-of-moves', type: 'average-number-of-moves', label: 'Total Number of Moves', x: 6, y: 6, w: 6, h: 4, enable: this.showAverageNumberOfMoves },

    //   { id: 'empty-full-count', type: 'empty-full-count', label: 'Trailer Status', x: 0, y: 10, w: 6, h: 4, enable: this.showEmptyFullTrailerCount },
    //   { id: 'average-turn-around-time', type: 'average-turn-around-time', label: 'Average Turnaround Time', x: 6, y: 10, w: 6, h: 4, enable: this.showAverageTurnaroundTime },

    //   { id: 'average-dwell-time', type: 'average-dwell-time', label: 'Average Dwell Time', x: 0, y: 14, w: 6, h: 4, enable: this.showAverageDwellTime },
    //   { id: 'average-dock-dwell-time', type: 'average-dock-dwell-time', label: 'Average Dock Dwell Time', x: 6, y: 14, w: 6, h: 4, enable: this.showAverageDockDwellTime },

    //   { id: 'average-dock-turnaround-time', type: 'average-dock-turnaround-time', label: 'Average Dock Turnaround Time', x: 0, y: 18, w: 6, h: 4, enable: this.showAverageDockTurnaroundTime },
    //   { id: 'daily-hourly-move-average', type: 'daily-hourly-move-average', label: 'Daily Hourly Average Moves', x: 0, y: 22, w: 12, h: 4, enable: this.showDailyHourlyAverageMoves },
    //   { id: 'in-transit-moves', type: 'in-transit-moves', label: 'In Transit Moves', x: 0, y: 26, w: 12, h: 4, enable: this.showInTransitMoves }
    // ];

    if (this.responsiveService.isLarge()) {
      defaultLayout = this.getAvailableWidgets().map(widget => {
        if (widget.id === 'key-metrics' || widget.id === 'dock-usage' || widget.id === 'weekly-total-spots' || widget.id === 'averageOfMoves') {
          return ({ ...widget, h: 2 });
        }

        return ({ ...widget , y: (widget.y -2)});
      })
    } else {
      defaultLayout = this.getAvailableWidgets();
    }

    return defaultLayout.filter(widget => widget.enable);
  }


  rearrangeGrid() {
    this.layout = this.grid.save() as GridStackWidget[];
    this.cdRef.detectChanges();
  }

  adjustGridHeight() {
    const content = document.querySelector('.grid-stack') as HTMLElement;
    if (content) content.style.height = 'auto';
  }

  restoreLayout() {
    if (this.layout.length && this.grid) {
      this.grid.removeAll(false);
      this.cdRef.detectChanges();
      this.layout.forEach(widget => {
        this.grid.addWidget({
          x: widget.x, y: widget.y, w: widget.w, h: widget.h,
          id: widget.id,
        });
      });
      this.cdRef.detectChanges();
    }
  }

  getColumnsBasedOnWidth(): number {
    const width = window.innerWidth;
    if (width < 576) return 3;
    if (width < 768) return 6;
    if (width < 992) return 9;
    return 12;
  }

  trackByFn(index: number, item: any): any {
    return item.id;
  }

  removeFromWidget(event: any) {
    if (event === 'showKeyMetrics') this.showKeyMetrics = false;
    else if (event === 'showAverageMoveTime') this.showAverageMoveTime = false;
    else if (event === 'showAverageNumberOfMoves') this.showAverageNumberOfMoves = false;
    else if (event === 'dock-usage') this.showDockUsage = false;
    else if (event === 'showEmptyFullTrailerCount') this.showEmptyFullTrailerCount = false;
    else if (event === 'showAverageTurnaroundTime') this.showAverageTurnaroundTime = false;
    else if (event === 'average-dwell-time') this.showAverageDwellTime = false;
    else if (event === 'average-dock-dwell-time') this.showAverageDockDwellTime = false;
    else if (event === 'average-dock-turnaround-time') this.showAverageDockTurnaroundTime = false;
    else if (event === 'averageOfMoves') this.showAverageOfMoves = false;
    else if (event === 'weekly-total-spots') this.showWeeklyTotalSpots = false;
    else if (event === 'daily-hourly-move-average') this.showDailyHourlyAverageMoves = false;
    else if (event === 'in-transit-moves') this.showInTransitMoves = false;
    this.updateWidgetAvailability();
    this.layout = this.getDefaultLayout();
    this.grid.load(this.layout);
    this.grid.compact();
    this.rearrangeGrid();
    this.cdRef.detectChanges();
  }

  toggleWidget(event: CheckboxChangeEvent, widgetId: string): void {
    if (widgetId === 'key-metrics') this.showKeyMetrics = event.checked;
    else if (widgetId === 'average-move-time') this.showAverageMoveTime = event.checked;
    else if (widgetId === 'average-number-of-moves') this.showAverageNumberOfMoves = event.checked;
    else if (widgetId === 'dock-usage') this.showDockUsage = event.checked;
    else if (widgetId === 'empty-full-count') this.showEmptyFullTrailerCount = event.checked;
    else if (widgetId === 'average-turn-around-time') this.showAverageTurnaroundTime = event.checked;
    else if (widgetId === 'average-dwell-time') this.showAverageDwellTime = event.checked;
    else if (widgetId === 'average-dock-dwell-time') this.showAverageDockDwellTime = event.checked;
    else if (widgetId === 'average-dock-turnaround-time') this.showAverageDockTurnaroundTime = event.checked;
    else if (widgetId === 'averageOfMoves') this.showAverageOfMoves = event.checked;
    else if (widgetId === 'weekly-total-spots') this.showWeeklyTotalSpots = event.checked;
    else if (widgetId === 'daily-hourly-move-average') this.showDailyHourlyAverageMoves = event.checked;
    else if (widgetId === 'in-transit-moves') this.showInTransitMoves = event.checked;
    this.updateWidgetAvailability();
    this.layout = this.getDefaultLayout();
    this.grid.load(this.layout);
    this.grid.compact();
    this.rearrangeGrid();
    this.cdRef.detectChanges();
  }

  restoreDefaults(): void {
    const userId = this.tokenService.getUserId();
    this.dashboardServiceNew.deleteUserPreference(userId).subscribe(() => {
      this.showAverageMoveTime = true;
      this.showEmptyFullTrailerCount = true;
      this.showAverageNumberOfMoves = true;
      this.showDockUsage = true;
      this.showKeyMetrics = true;
      this.showAverageTurnaroundTime = true;
      this.showAverageDwellTime = true;
      this.showAverageDockDwellTime = true;
      this.showAverageDockTurnaroundTime = true;
      this.showAverageOfMoves = true;
      this.showWeeklyTotalSpots = true;
      const allowedRoles = ['ROLE_ADMIN', 'ROLE_SUPERVISOR', 'ROLE_IT'];
      if (allowedRoles.includes(this.getAccessToken.roles[0])) {
        this.showDailyHourlyAverageMoves = true;
        this.isDailyHourlyAverageMovesAllowed = true;
      }
      this.showInTransitMoves = true;
      this.updateWidgetAvailability();
      if (this.grid) {
        this.layout = this.getDefaultLayout();
        this.grid.load(this.layout);
        this.grid.compact();
      }
      this.manageWidgetDropDown();
      this.rearrangeGrid();
      this.cdRef.detectChanges();
    },
      (error) => {
        console.error("Error deleting user preferences:", error);
      });
  }

  loadLastRefreshTime() {
    this.lastRefreshTimestamp = Date.now();
    localStorage.setItem("lastRefreshTime", this.lastRefreshTimestamp.toString());
    this.updateLastRefreshTime();
    this.isRefreshDisabled = false;
    this.refreshCounter = 60;
  }

  updateLastRefreshTime() {
    const now = Date.now();
    const diff = now - this.lastRefreshTimestamp;
    const minutes = Math.floor(diff / 60000);
    this.lastRefreshTime = minutes < 1 ? "0 minutes ago" : `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
  }

  startTimeUpdateInterval() {
    this.timeUpdateInterval = setInterval(() => { this.updateLastRefreshTime(); }, 60000);
  }

  ngOnDestroy() {
    this.refreshInterval?.unsubscribe();
    clearInterval(this.timeUpdateInterval);
  }

  refreshData() {
    if (this.isRefreshDisabled) return;
    // this.onClientChange();
    this.isRefresh = true;
    this.reRenderApiCalls();
    this.lastRefreshTimestamp = Date.now();
    localStorage.setItem("lastRefreshTime", this.lastRefreshTimestamp.toString());
    this.updateLastRefreshTime();
    this.startRefreshCooldown();
  }

  startRefreshCooldown() {
    if (this.refreshInterval) {
      this.refreshInterval.unsubscribe();
    }
    this.isRefreshDisabled = true;
    this.refreshCounter = 60;
    this.refreshInterval = interval(1000).subscribe(() => {
      if (this.refreshCounter > 0) {
        this.refreshCounter--;
      } else {
        this.isRefreshDisabled = false;
        this.refreshInterval?.unsubscribe();
      }
    });
  }

  toggleWidgetManager(event: Event): void {
    if (this.widgetManagerOpen) {
      this.widgetManagerOpen = false;
    } else {
      this.widgetManagerOpen = true;
      event.stopPropagation();
      this.manageWidgetDropDown();
    }
  }

  closeWidgetManager(event: Event): void {
    if (this.widgetManagerOpen) {
      this.widgetManagerOpen = false;
    }
  }

  getThreeMonthsDateRange(): { fromDate: string; toDate: string } {
    const today = new Date();
    const fromDate = this.datePipe.transform(today, "yyyy-MM-dd");
    const toDate = this.datePipe.transform(today, "yyyy-MM-dd");
    return { fromDate: fromDate || "", toDate: toDate || "" };
  }

  getClientList(query: any) {
    const { fromDate, toDate } = this.getThreeMonthsDateRange();
    this.manageClientsService.viewClients(query).subscribe((response) => {
      this.clients = response.list;
      if (this.clients.length > 0) {
        const storedClient = JSON.parse(localStorage.getItem("setDefaultClientId"));
        if (storedClient) {
          this.selectedClient = storedClient.clientName;
          this.selectedClientUuid = storedClient.clientId;
        }
        else {
          this.defaultCliennt = this.clients[0];
          localStorage.setItem("setDefaultClientId", JSON.stringify(this.defaultCliennt));
          this.selectedClient = this.defaultCliennt.clientName;
          this.selectedClientUuid = this.defaultCliennt.clientId;
        }
        const getstoredClient = JSON.parse(localStorage.getItem("setDefaultClientId"));
        let storeClientName: any = [];
        getstoredClient.isRefresh = false;
        storeClientName.push(getstoredClient)
        localStorage.setItem("storeSelectedClientIdLists", JSON.stringify(storeClientName));
        this.fetchUserStats(this.clientQuery, this.selectedClientUuid, this.isRefresh);
        this.getJobsV1(this.clientQuery, "COMPLETED", this.selectedClientUuid, fromDate, toDate, this.isRefresh);
        this.getJobsOfAveraTurnTime(this.clientQuery, "COMPLETED", this.selectedClientUuid, fromDate, toDate, this.isRefresh)
        this.getAvgDwellTimeStatus(this.selectedClientUuid, this.isRefresh);
        this.getAvgDockDwellTimeStatus(this.selectedClientUuid, this.isRefresh);
        this.getAvgDockTurnaroundTimeStatus(this.selectedClientUuid, this.isRefresh);
        this.averageOfMoves(this.selectedClientUuid, this.isRefresh);
        this.getInTransitSpots(this.selectedClientUuid, this.isRefresh);
        this.getWeeklyTotalSpots(this.selectedClientUuid, this.isRefresh);
        if (this.showDailyHourlyAverageMoves) this.dailyHourlyAverageMoves(this.selectedClientUuid, this.isRefresh);
        if (this.isLoggedInClient) {
          this.getAverageMovesForClientRole(this.clientQuery, "COMPLETED", this.selectedClientUuid);
        }
      } else {
        console.warn("No clients found.");
      }
    },
      (error) => {
        console.error("Error fetching clients:", error);
        this.errorService.handleError(error, true);
      }
    );
  }

  getJobsV1(query: any, status?: any, clientId?: any, fromDate?: any, toDate?: any, isRefresh?: boolean, fleetId?: any, sort?: any, assignedTo?: string, notes?: any, bucket?: string) {
    this.loadingAvgMoveTime = true;
    this.manageJobsService.viewJobsV1(query, status, clientId, fromDate, toDate, isRefresh, fleetId, sort, assignedTo, notes, bucket).subscribe((response) => {
      this.updateUserAverageTimeChart(response);
      this.loadingAvgMoveTime = false;
    },
      (error) => {
        this.errorService.handleError(error, true);
        this.loadingAvgMoveTime = false;
      }
    );
  }

  getJobsOfAveraTurnTime(query: any, status?: any, clientId?: any, fromDate?: any, toDate?: any, isRefresh?: boolean, fleetId?: any, sort?: any, assignedTo?: string, notes?: any, bucket?: string) {
    this.loadingAvgMoveTurnTime = true;
    this.manageJobsService.viewJobsOfAverTurnaroundTime(query, status, clientId, fromDate, toDate, isRefresh, fleetId, sort, assignedTo, notes, bucket).subscribe((response) => {
      this.updateUserAverageTurnTimeChart(response);
      this.loadingAvgMoveTurnTime = false;
    },
      (error) => {
        this.errorService.handleError(error, true);
        this.loadingAvgMoveTurnTime = false;
      }
    );
  }

  fetchUserStats(query: any, clientUuid?: string, isRefresh?: boolean) {
    this.loadingTotalNumMoves = true;
    this.dashboardServiceNew.getTotalNumMovesForClient(query, clientUuid, isRefresh).subscribe((apiResponse) => {
      this.updateUserMoveTimeChart(apiResponse);
      this.loadingTotalNumMoves = false;
    }),
      catchError((error) => {
        this.loadingTotalNumMoves = false;
        this.errorService.handleError(error, true);
        return of([]);
      });
  }

  getAvgDwellTimeStatus(clientUuid: any, isRefresh?: boolean) {
    this.loadingAvgDwellTime = true;
    this.dashboardServiceNew.getTotalAvgDwellTime(clientUuid, isRefresh).subscribe((apiResponse) => {
      this.updateUserAvgDwellTimeChart(apiResponse);
      this.loadingAvgDwellTime = false;
    }),
      catchError((error) => {
        this.loadingAvgDwellTime = false;
        this.errorService.handleError(error, true);
        return of([]);
      });
  }

  getAvgDockDwellTimeStatus(clientUuid: any, isRefresh?: boolean) {
    this.loadingAvgDockDwellTime = true;
    this.dashboardServiceNew.getTotalAvgDockDwellTime(clientUuid, isRefresh).subscribe((apiResponse) => {
      const dockDwellTimeResponse = apiResponse.reduce((acc, item) => {
        acc[item.locationName] = {
          occupied: item.averageLastOccupiedTimeHours,
          empty: item.averageLastEmptiedTimeHours
        };
        return acc;
      }, {} as Record<string, { occupied: number; empty: number }>);
      this.updateDockDwellTimeChart(dockDwellTimeResponse);
      this.loadingAvgDockDwellTime = false;
    }),
      catchError((error) => {
        this.loadingAvgDockDwellTime = false;
        this.errorService.handleError(error, true);
        return of([]);
      });
  }

  getAvgDockTurnaroundTimeStatus(clientUuid: any, isRefresh?: boolean) {
    this.loadingAvgDockTurnaroundTime = true;
    this.dashboardServiceNew.getTotalAvgDockTurnaroundTime(clientUuid, isRefresh).subscribe((apiResponse) => {
      this.updateDockTurnaroundTimeChart(apiResponse);
      this.loadingAvgDockTurnaroundTime = false;
    }),
      catchError((error) => {
        this.loadingAvgDockTurnaroundTime = false;
        this.errorService.handleError(error, true);
        return of([]);
      });
  }

  averageOfMoves(clientUuid: any, isRefresh?: boolean) {
    this.loadingAverageOfMoves = true;
    this.dashboardServiceNew.viewAverageOfMoves(clientUuid, isRefresh).subscribe((apiResponse) => {
      const labels: string[] = Object.keys(apiResponse);
      this.averageMoves = {
        key: labels[0],
        value: apiResponse[labels[0]]
      }
      setTimeout(() => {
        this.loadingAverageOfMoves = false;
      }, 100);
    }),
      catchError((error) => {
        this.loadingAverageOfMoves = false;
        this.errorService.handleError(error, true);
        return of([]);
      });
  }

  getWeeklyTotalSpots(clientUuid: any, isRefresh?: boolean) {
    this.loadingWeeklyTotalSpots = true;
    this.dashboardServiceNew.viewWeeklyTotalSpots(clientUuid, isRefresh).subscribe(res => {
      this.getWeeklyTotalSpotsData = res;
      setTimeout(() => {
        this.loadingWeeklyTotalSpots = false;
      }, 100);
    }, (error) => {
      this.loadingWeeklyTotalSpots = false;
      this.errorService.handleError(error, true);
    });
  }

  dailyHourlyAverageMoves(clientUuid: any, isRefresh?: boolean) {
    this.loadingDailyHourlyAverageMoves = true;
    this.dashboardServiceNew.dailyHourlyAverageMoves(clientUuid, isRefresh).subscribe((apiResponse) => {
      this.getDailyHourlyAverageMovesData = apiResponse.statsByDay;
      setTimeout(() => {
        this.loadingDailyHourlyAverageMoves = false;
      }, 100);
    }),
      catchError((error) => {
        this.loadingDailyHourlyAverageMoves = false;
        this.errorService.handleError(error, true);
        return of([]);
      });
  }

  getInTransitSpots(clientUuid: any, isRefresh?: boolean) {
    this.loadingInTransitMoves = true;
    this.dashboardServiceNew.viewInTransitSpots(clientUuid, isRefresh).subscribe(res => {
      this.getinTransitSpotsData = res.inTransitSpots;
      setTimeout(() => {
        this.loadingInTransitMoves = false;
      }, 100);
    }, (error) => {
      this.loadingInTransitMoves = false;
      this.errorService.handleError(error, true);
    });
  }


  updateUserAverageTurnTimeChart(response: any): void {
    const labels: string[] = Object.keys(response.DRIVER);
    const driverData: number[] = labels.map((day) => response.DRIVER[day] || 0);
    const spotterData: number[] = labels.map(
      (day) => response.SPOTTER[day] || 0
    );
    this.lineChartDataForAverTurnTime = {
      labels: labels,
      datasets: [
        {
          label: "Average Driver Turnaround Time",
          data: driverData,
          borderColor: '#99ffcc',
          backgroundColor: '#99ffcc',
          fill: false,
          tension: 0.4,
          pointBackgroundColor: '#6BC094'
        },
        {
          label: "Average Spotter Turnaround Time",
          data: spotterData,
          borderColor: '#99ccff',
          backgroundColor: '#99ccff',
          fill: false,
          tension: 0.4,
          pointBackgroundColor: '#6788AF'
        },
      ],
    };
  }

  updateUserAverageTimeChart(response: any): void {
    const labels: string[] = Object.keys(response.DRIVER);
    const driverData: number[] = labels.map((day) => response.DRIVER[day] || 0);
    const spotterData: number[] = labels.map(
      (day) => response.SPOTTER[day] || 0
    );
    this.lineChartData = {
      labels: labels,
      datasets: [
        {
          label: 'Driver Average Time (Minutes)',
          data: driverData,
          borderColor: '#99ffcc',
          backgroundColor: '#99ffcc',
          fill: false,
          tension: 0.4,
          pointBackgroundColor: '#6BC094'
        },
        {
          label: 'Spotter Average Time (Minutes)',
          data: spotterData,
          borderColor: '#99ccff',
          backgroundColor: '#99ccff',
          fill: false,
          tension: 0.4,
          pointBackgroundColor: '#6788AF'
        }
      ]
    };
  }

  updateUserMoveTimeChart(response: any): void {
    const labels: string[] = Object.keys(response.DRIVER); // Extract days of the week
    const driverData: number[] = labels.map((day) => response.DRIVER[day] || 0);
    const spotterData: number[] = labels.map(
      (day) => response.SPOTTER[day] || 0
    );
    this.barChartData = {
      labels: labels,
      datasets: [
        {
          label: "Driver",
          data: driverData,
          borderColor: '#99FFCC',
          backgroundColor: "#99FFCC", //#22a374
          fill: false,
          tension: 0.4
        },
        {
          label: "Spotter",
          data: spotterData,
          backgroundColor: "#99ccff",
          fill: false,
          tension: 0.4
        },
      ],
    };
  }

  updateUserAvgDwellTimeChart(response: any): void {
    const filtered = response.filter((location: any) => location.averageDwellTimeHours !== 0);
    const labels: string[] = filtered.map((location: any) => location.locationName);
    const averageDwellTimeHours: number[] = filtered.map((location: any) => location.averageDwellTimeHours);
    this.barChartAvgDwellTimeData = {
      labels: labels,
      datasets: [
        {
          label: 'Dwell Time',
          data: averageDwellTimeHours,
          borderColor: '#99ccff',
          backgroundColor: '#99ccff',
          barThickness: 50
        }
      ]
    };
  }

  updateDockDwellTimeChart(response: any): void {
    const filteredLabels = Object.keys(response).filter(loc => {
      const entry = response[loc];
      return entry.occupied !== 0 || entry.empty !== 0;
    });

    const occupiedData: number[] = filteredLabels.map(loc => response[loc].occupied || 0);
    const emptyData: number[] = filteredLabels.map(loc => response[loc].empty || 0);
    this.barChartDockDwellTime = {
      labels: filteredLabels,
      datasets: [
        {
          label: "Average Time Occupied",
          data: occupiedData,
          borderColor: '#99ffcc',
          backgroundColor: '#99ffcc',
          fill: false,
          tension: 0.4,
          //hoverBackgroundColor: '#2a75bf',
          //hoverBorderColor: '#2a75bf',
        },
        {
          label: "Average Time Empty",
          data: emptyData,
          borderColor: '#99ccff',
          backgroundColor: '#99ccff',
          fill: false,
          tension: 0.4
        },
      ],
    };
  }

  updateDockTurnaroundTimeChart(response: any): void {
    const filtered = response.filter((location: any) => location.averageTurnAroundTimeHours !== 0);
    const labels: string[] = filtered.map((location: any) => location.locationName);
    const averageTurnAroundTimeHours: number[] = filtered.map((location: any) => location.averageTurnAroundTimeHours);
    this.barChartDockTurnaroundTime = {
      labels: labels,
      datasets: [
        {
          label: 'Average Dock Turnaround Time',
          data: averageTurnAroundTimeHours,
          backgroundColor: '#99ccff',
          borderColor: '#99ccff',
          barThickness: 50

        }
      ]
    };
  }


  getAllStatistics() {
    combineLatest([
      this.dashboardService.getGeneralStatistics(),
      this.dashboardService.getMessagesCount(this.todayQuery),
    ]).subscribe(
      (res) => {
        this.generalStatistics = res[0];
        this.messageStatistics = res[1];
        this.dashboardService.sendMessageCount(this.messageStatistics.newCount);
        if (this.messageStatistics.newCount != 0) {
          this.unreadMessage = true;
        }
      },
      (error) => {
        this.errorService.handleError(error, true);
      }
    );
  }

  routeToManageJobs(filter) {
    this.router.navigate(["/main/manage-jobs"], { queryParams: { filter } });
  }

  onClientChange(): void {
    const { fromDate, toDate } = this.getThreeMonthsDateRange();
    const selectedClientObj = this.clients.find(
      (client) => client.clientName === this.selectedClient
    );
    const storeSelectedClientIdLists = JSON.parse(localStorage.getItem("storeSelectedClientIdLists"));
    if (selectedClientObj) {
      localStorage.removeItem("setDefaultClientId");
      localStorage.setItem("setDefaultClientId", JSON.stringify(selectedClientObj));
      const storedClient = JSON.parse(localStorage.getItem("setDefaultClientId"));
      this.selectedClient = storedClient.clientName;
      this.selectedClientUuid = storedClient.clientId;
      this.selectedClientUuid = storedClient.clientId;
      this.showEmptyFullTrailerCount = false;
      setTimeout(() => {
        this.showEmptyFullTrailerCount = true;
      }, 100);
      let filterExistingClientLists = storeSelectedClientIdLists.find(item => item.clientName === storedClient.clientName);
      if (!filterExistingClientLists) {
        localStorage.removeItem("storeSelectedClientIdLists");
        selectedClientObj.isRefresh = false;
        this.isRefresh = true;
        storeSelectedClientIdLists.push(selectedClientObj)
        localStorage.setItem("storeSelectedClientIdLists", JSON.stringify(storeSelectedClientIdLists));
      }
      else {
        const isRefreshEnable = JSON.parse(localStorage.getItem("storeSelectedClientIdLists")).find(item => item.clientName === storedClient.clientName);
        this.isRefresh = isRefreshEnable.isRefresh;
      }
      this.reRenderApiCalls();
    } else {
      console.warn("Selected client not found.");
    }
  }

  reRenderApiCalls() {
    const { fromDate, toDate } = this.getThreeMonthsDateRange();
    this.getJobsV1(this.clientQuery, "COMPLETED", this.selectedClientUuid, fromDate, toDate, this.isRefresh);
    this.getJobsOfAveraTurnTime(this.clientQuery, "COMPLETED", this.selectedClientUuid, fromDate, toDate, this.isRefresh)
    this.fetchUserStats(this.clientQuery, this.selectedClientUuid, this.isRefresh);
    this.getAvgDwellTimeStatus(this.selectedClientUuid, this.isRefresh);
    this.getAvgDockDwellTimeStatus(this.selectedClientUuid, this.isRefresh);
    this.getAvgDockTurnaroundTimeStatus(this.selectedClientUuid, this.isRefresh)
    this.averageOfMoves(this.selectedClientUuid, this.isRefresh);
    this.getWeeklyTotalSpots(this.selectedClientUuid, this.isRefresh);
    this.getInTransitSpots(this.selectedClientUuid, this.isRefresh);
    this.dockUsageComp.refresh();
    if (this.showDailyHourlyAverageMoves) this.dailyHourlyAverageMoves(this.selectedClientUuid, this.isRefresh);
    this.startRefreshCooldown();
  }

  getAverageMovesForClientRole(query: any, status?: any, clientId?: any, fromDate?: any, toDate?: any, fleetId?: any, sort?: any, assignedTo?: string, notes?: any, bucket?: string
  ) {
    this.loadingAvgMovesForClientRole = true;
    this.manageJobsService.viewJobsForClientRole(query, status, clientId, fromDate, toDate, fleetId, sort, assignedTo, notes, bucket).subscribe(
      (response) => {
        this.loadingAvgMovesForClientRole = false;
        this.updateUserAverageTimeChartForClientRole(response);
      },
      (error) => {
        this.errorService.handleError(error, true);
        this.loadingAvgMovesForClientRole = false;
      }
    );
  }

  updateUserAverageTimeChartForClientRole(response: any[]): void {
    const labels: string[] = [];
    const data: number[] = [];
    response.forEach((entry) => {
      labels.push(entry.role);
      data.push(entry.averageTime);
    });

    this.barChartDataOfAvgMovesForClientRole = {
      labels: labels,
      datasets: [
        {
          label: "Average Move Time",
          data: data,
          backgroundColor: ["#2b97c2", "#2b97c2"],
          borderColor: ["#FB8C00", "#FB8C00"],
          borderWidth: 1,
          barPercentage: 0.4,
          categoryPercentage: 0.4,
        },
      ],
    };
  }

  //new changes by madhavan

  reenableDragging() {
    setTimeout(() => {
      this.grid.enableMove(true);
      this.grid.enableResize(true);
    }, 100);
  }

  enableDraggableCards() {
    this.grid.enable();
    this.grid.movable(".draggable-card", true);
  }

  enableResizable() {
    document.querySelectorAll(".grid-stack-item").forEach((item) => {
      (item as HTMLElement).classList.add("ui-resizable");
    });
  }

  adjustWidgetPositions() {
    if (!this.grid || !this.layout) return;
    const updatedNodes = this.grid.save() as GridStackWidget[];
    if (!Array.isArray(updatedNodes)) {
      console.error("GridStack.save() did not return an array:", updatedNodes);
      return;
    }
    updatedNodes.forEach((node) => {
      let widget = this.layout.find((w) => w.id === node.id);
      if (widget) {
        widget.x = node.x;
        widget.y = node.y;
        widget.w = node.w;
        widget.h = node.h;
      }
    });
    this.layout.sort((a, b) => a.y - b.y);
    for (let i = 1; i < this.layout.length; i++) {
      const prevWidget = this.layout[i - 1];
      const currentWidget = this.layout[i];
      currentWidget.y = prevWidget.y + prevWidget.h;
      let nodeToUpdate = this.grid.engine.nodes.find(
        (n) => n.id === currentWidget.id
      );
      if (nodeToUpdate) {
        this.grid.update(nodeToUpdate.el, {
          x: nodeToUpdate.x,
          y: currentWidget.y,
          w: nodeToUpdate.w,
          h: nodeToUpdate.h,
        });
      }
    }
  }

  checkWidgetAvailability(widget: any): boolean {
    return widget.id === 'daily-hourly-move-average' ? this.isDailyHourlyAverageMovesAllowed : true
  }
}
