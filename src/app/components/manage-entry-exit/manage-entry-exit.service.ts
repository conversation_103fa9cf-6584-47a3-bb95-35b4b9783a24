import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { IpagedQuery } from 'src/app/model/IpagedQuery';

@Injectable({
  providedIn: 'root'
})
export class ManageEntryExitService {

  constructor(private http: HttpClient) { }

  getEntryExitList(query:IpagedQuery, clientId?:any, locationId?:string, type?:string, fromDate?:any, toDate?:any): Observable<any> {
    
    let params = new HttpParams({
      fromObject:{
        isActive:query.isActive.valueOf(),
        page:query.page.toString(),
        size:query.size.toString(),
        sort:'createdDate,desc'
      }
    })

    if(fromDate && toDate){
      params = params.append("fromDate",fromDate.toString())
      params = params.append("toDate",toDate.toString())
    }

    if(type) {
      params = params.append("type",type)
    }

    if(locationId) {
      params = params.append("location.uuid", locationId)
    }

    if(clientId){
      params = params.append("location.client.uuid",clientId.toString())
    }
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/fleets/entryExits`,{params});
  }

  deleteEntryExit(clientId,entryExitId): Observable<any> {
    return this.http.delete(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/clients/${clientId}/locations/entryExits/${entryExitId}`);
  }
}
