:host ::ng-deep {
    .p-toolbar{
    background-color: #f5f5f5 !important;
    border: none !important;
    }

    // .ng-dirty{
    //      color: tomato;
    // }

    .text-danger{
        color: red;
    }

    .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
        text-align: center !important;
    }

    .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
        text-align: center !important;
    }
}
.text-danger{
    color: red;
}
.file-upload {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #ccc;
    padding: 5px;
    border-radius: 4px;
    width: fit-content;
  }
  
  .file-input {
    display: none; /* Hide the default file input */
  }
  
  .file-display {
    display: flex;
    align-items: center;
    margin-right: 10px;
    width: 50%;
  }
  
  .file-icon {
    width: 20px; /* Adjust icon size */
    height: 20px;
    margin-right: 5px; /* Space between icon and text */
  }
  
  .file-text {
    color: #555;
    font-size: 14px;
    overflow: hidden;
  }
  
  .file-label {
    padding: 5px 15px;
    background-color: #6c7a89;
    color: white;
    font-size: 13px;
    border-radius: 3px;
    cursor: pointer;
    text-align: right;
  }
  
  @media only screen and (max-width: 991px) {
    .entry_exit_grid .field{
        width: 100% !important;
    }
  }
  