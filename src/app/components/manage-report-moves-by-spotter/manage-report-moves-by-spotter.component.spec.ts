import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageReportMovesBySpotterComponent } from './manage-report-moves-by-spotter.component';

describe('ManageReportMovesBySpotterComponent', () => {
  let component: ManageReportMovesBySpotterComponent;
  let fixture: ComponentFixture<ManageReportMovesBySpotterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ManageReportMovesBySpotterComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageReportMovesBySpotterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
