// :host ::ng-deep {}

.p-tag {
    font-size: 1rem !important;
    padding: 0.25rem 1.4rem !important;
    margin-left: 10px !important;
}

.text-danger {
    color: red;
}

.radio-tag-blue .p-radiobutton-checked .p-radiobutton-box {
    border-color: #1976d2 !important;
    background: #1976d2 !important;
}

.radio-tag-blue .p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: #1976d2 !important;
    background: #1976d2 !important;
}

.radio-tag-red .p-radiobutton-checked .p-radiobutton-box {
    border-color: #EF4444 !important;
    background: #EF4444 !important;
}

.radio-tag-red .p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: #EF4444 !important;
    background: #EF4444 !important;
}

.radio-tag-green .p-radiobutton-checked .p-radiobutton-box {
    border-color: #047857 !important;
    background: #047857 !important;
}

.radio-tag-green .p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: #047857 !important;
    background: #047857 !important;
}

// .p-radiobutton-checked .p-radiobutton-box {
//     border-color: unset !important;
//     background: none !important;
// }

.alignSelf {
    align-self: end;
}

.checkBoxAlign {
    display: inline-flex;
    align-items: center;
}

.labelMb {
    margin-bottom: 0.5rem;
}

.p-checkbox .p-checkbox-box.p-highlight {
    background-color: #1976d2 !important;
    /* Set to same blue as the tag */
    border-color: #1976d2 !important;
}

.p-checkbox .p-checkbox-box.p-highlight:hover {
    background-color: #1565c0 !important;
    /* Optional hover variation */
    border-color: #1565c0 !important;
}

.noPointer {
    pointer-events: none;
    opacity: 0.8;
}