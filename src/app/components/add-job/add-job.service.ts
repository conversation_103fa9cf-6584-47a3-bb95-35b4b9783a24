import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';

@Injectable({
  providedIn: 'root'
})
export class AddJobService {

  constructor(private http: HttpClient) { }

  saveJob(jobObj: any, scheduledDateTime?: string): Observable<any> {
    let url = `${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs`;

    if (scheduledDateTime) {
      url += `?scheduleDateTime=${scheduledDateTime}`;
    }

    return this.http.post(url, jobObj);
  }

  // updateJob(jobId, jobObj): Observable<any> {
  //   return this.http.put(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs/${jobId}`, jobObj);
  // }

  updateJob(jobId: any, jobObj: any, scheduledDateTime?: string): Observable<any> {
    let url = `${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs/${jobId}`;

    if (scheduledDateTime) {
      url += `?scheduleDateTime=${scheduledDateTime}`;
    }

    return this.http.put(url, jobObj);
  }

  getJobById(jobId): Observable<any> {
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs/${jobId}`);
  }

  deleteJob(jobId): Observable<any> {
    return this.http.delete(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs/${jobId}`);
  }
}
