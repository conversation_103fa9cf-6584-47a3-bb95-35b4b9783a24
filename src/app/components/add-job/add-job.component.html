<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>
    <div class="col-12">
        <div class="card">
            <h5>{{ status == 'SCHEDULED' ? (isSpotEdit ? 'Reschedule Spot' : 'Schedule Spot') : (isSpotEdit ? 'Edit Spot' : 'Add Spot') }}</h5>
            <form [formGroup]="jobForm">
                <p-fluid class="grid">
                    <div class="field col-12 md:col-4">
                        <label htmlFor="client">Select Client<span class="text-danger">*</span></label>
                        <p-select class="full-width" placeholder="Select Client" formControlName="clientId"
                            [options]="clientList" (onChange)="onClientSelect($event)" optionLabel="clientName"
                            optionValue="clientId"></p-select>
                        <span class="text-danger" *ngIf="
                            jobForm.controls.clientId.touched &&
                            jobForm.controls.clientId.invalid
                            ">Client is required</span>
                    </div>
                    <div class="field col-12 md:col-8" *ngIf="!isSupervisorOrClient"></div>
                    <div class="field col-12 md:col-8 alignSelf" *ngIf="isSupervisorOrClient">
                        <div class="col-12 md:col-4">
                            <div class="checkBoxAlign flex align-items-center">
                                <p-checkbox [ngModelOptions]="{standalone: true}" [(ngModel)]="scheduledOrAdd"
                                    binary="true" inputId="scheduled"
                                    [ngClass]="job && job.status !== 'SCHEDULED' ? 'noPointer':''"
                                    (onChange)="onChangeSheduledAndAdd($event, 'scheduled')"></p-checkbox>
                                <p-tag rounded="true"
                                    style="background-color: #00c853; color: white; margin-left: 0.5rem;"
                                    value="Set up a schedule"></p-tag>
                            </div>
                        </div>
                    </div>
                    <div class="field col-12 md:col-4" *ngIf="!isNewTrailer">
                        <label htmlFor="trailerTrucks">Trailer/Truck/Container<span class="text-danger">*</span>
                            <p-tag class="tag-hover cursor-pointer" severity="success" (click)="showNewTrailerFields()"
                                value="New Asset" icon="pi pi-plus"></p-tag>
                        </label>
                        <p-autocomplete formControlName="fleetAndHotTrailer" [dropdown]="true"
                            [suggestions]="filteredFleets" (completeMethod)="filterFleets($event)"
                            (onSelect)="onFleetSelect($event)" dataKey="fleetAndHotTrailer"
                            (onClear)="clearUniqueId($event)" (onFocus)="isInputTouched=true"
                            (onBlur)="isInputTouched=true" (input)="onUnitNumberChange($event)">
                            <ng-template let-fleet pTemplate="item">
                                <div>{{fleet.fleetAndHotTrailer}}</div>
                            </ng-template>
                        </p-autocomplete>
                        <input pInputText type="hidden" formControlName="fleetId" />
                        <span class="text-danger" *ngIf="
                            (jobForm.controls.fleetAndHotTrailer.touched &&
                            jobForm.controls.fleetAndHotTrailer.invalid && 
                            uniqueClientId!=null && !isTrailerFoundInList) || (submitted && jobForm.value.fleetId == '' && !isTrailerFoundInList)
                            ">Please select a Trailer/Truck/Container</span>
                        <span *ngIf="isTrailerFoundInList" class="text-danger">Trailer/Unit # not found. Please add
                            using "+New Asset "</span>
                        <span class="text-danger" *ngIf="
                            isInputTouched && uniqueClientId==null
                            ">Client required</span>
                    </div>
                    <div class="field col-12 md:col-4" *ngIf="isNewTrailer"></div>
                    <div class="field col-12 md:col-4" *ngIf="jobForm.value.scheduledOrAdd === 'add'"></div>
                    <div class="field col-12 md:col-4" *ngIf="jobForm.value.scheduledOrAdd === 'scheduled'">
                        <label for="scheduledDateTime" class="mb-3">Scheduled Date and Time<span
                                class="text-danger">*</span></label>
                        <span class="block mt-2 md:mt-0 p-input-icon-left">
                            <p-datepicker [ngClass]="job && job.status !== 'SCHEDULED' ? 'noPointer':''"
                                inputId="scheduledDateTime" name="scheduledDateTime" placeholder="Select Date and Time"
                                showTime="true" hourFormat="12" dateFormat="mm/dd/yy" showIcon = "true"
                                formControlName="scheduledDateTime" [minDate]="minDate"
                                (onFocus)="isInputTouched=true" (onBlur)="isInputTouched=true" (ngModelChange)="onDateTimeSelect($event)">
                            </p-datepicker>
                            <span class="text-danger" *ngIf="
                                    jobForm.controls.scheduledDateTime.touched &&
                                    jobForm.controls.scheduledDateTime.invalid 
                                ">Please enter a valid date & time</span>
                        </span>
                    </div>
                    <div class="field col-12" *ngIf="isNewTrailer">
                        <p-tag class="tag-hover cursor-pointer" severity="warning" (click)="showNewTrailerFields()"
                            value="Back" icon="pi pi-directions-alt"></p-tag>
                        <div class="flex">
                            <div class="field md:col-4">
                                <label>Carrier<span class="text-danger">*</span></label>
                                <input pInputText id="spot" type="text" formControlName="carrier" />
                                <!-- <p-select class="full-width" *ngIf="!newCarrier" [options]="uniqueCarrierList" placeholder="Select Carrier Name" formControlName="carrier" optionLabel="carrier" optionValue="carrierId"></p-select> -->
                            </div>
                            <div class="field md:col-4">
                                <label>Type<span class="text-danger">*</span></label>
                                <p-select class="full-width" placeholder="Select Type" formControlName="trailerType"
                                    [options]="dropdownItems" optionLabel="name" optionValue="code"></p-select>
                            </div>
                            <div class="field md:col-4">
                                <label>Trailer#<span class="text-danger">*</span></label>
                                <input pInputText id="spot" type="text" formControlName="fleetAndHotTrailer" />
                                <span class="text-danger" *ngIf="
                                    jobForm.controls.fleetAndHotTrailer.touched &&
                                    jobForm.controls.fleetAndHotTrailer.invalid 
                                ">Please enter the Trailer /Unit #</span>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="field col-12 md:col-8" *ngIf="!isNewTrailer"></div> -->
                    <div class="field col-12 md:col-6">
                        <label htmlFor="pickupLocation">Pickup Location<span class="text-danger">*</span></label>
                        <p-select class="full-width" [options]="clientsLocationsList" formControlName="pickupLocationId"
                            placeholder="Select Pickup Location" optionLabel="locationName"
                            (onChange)="onChange($event,'pickup')" optionValue="locationId"></p-select>
                        <span class="text-danger" *ngIf="
                            jobForm.controls.pickupLocationId.touched &&
                            jobForm.controls.pickupLocationId.invalid
                            ">Pickup Location is required</span>
                    </div>
                    <div class="field col-12 md:col-6">
                        <label htmlFor="pickupSpot">Pickup Dock/Parking Spot<span class="text-danger">*</span></label>
                        <p-select class="full-width" [options]="pickupSpots" formControlName="pickupSpotId"
                            placeholder="Select Pickup Spot" optionLabel="spotAndStatus"
                            optionValue="spotId"></p-select>
                        <span class="text-danger" *ngIf="
                            jobForm.controls.pickupSpotId.touched &&
                            jobForm.controls.pickupSpotId.invalid
                            ">Pickup Dock/Parking Spot is required</span>
                    </div>
                    <div class="field col-12 md:col-6">
                        <label htmlFor="dropLocation">Drop Location<span class="text-danger">*</span></label>
                        <p-select class="full-width" [options]="clientsLocationsList" formControlName="dropLocationId"
                            placeholder="Select Drop Location" optionLabel="locationName"
                            (onChange)="onChange($event,'drop')" optionValue="locationId"></p-select>
                        <span class="text-danger" *ngIf="
                            jobForm.controls.dropLocationId.touched &&
                            jobForm.controls.dropLocationId.invalid
                            ">Drop Location is required</span>
                    </div>
                    <div class="field col-12 md:col-6">
                        <label htmlFor="dropSpot">Drop off Dock/Parking Spot<span class="text-danger">*</span><</label>
                        <p-select class="full-width" [options]="dropSpots" formControlName="dropSpotId" placeholder="Select Drop Spot"
                            optionLabel="spotAndStatus" optionValue="spotId"></p-select>
                        <span class="text-danger" *ngIf="
                                                jobForm.controls.dropSpotId.touched &&
                                                jobForm.controls.dropSpotId.invalid
                                                ">Drop Spot is required</span>
                    </div>
                    <div class="field col-12 md:col-6" *ngIf="isSupervisorOrClient">
                        <label htmlFor="sequenceAsn">Sequence ASN</label>
                        <input pInputText id="sequenceAsn" formControlName="sequenceAsn" type="text" />
                    </div>
                    <div class="field col-12">
                        <label htmlFor="jobDescription">Notes</label>
                        <textarea pTextarea rows="3" cols="30" formControlName="description"></textarea>
                    </div>
                    <div class="field col-12 md:col-4">
                        <label htmlFor="trailerTrucks">Trailer/Container Status</label>
                        <p-select class="full-width" [options]="trailerStatus" formControlName="fleetStatus"
                            placeholder="Select Trailer/Container Status" optionLabel="name"
                            optionValue="code"></p-select>
                    </div>
                    <div class="field col-12 md:col-4">
                        <label htmlFor="drivers">Drivers/Yard Spotters<span class="text-danger">*</span></label>
                        <p-select class="full-width" [options]="driversYardSpotters" formControlName="assignedToUserId"
                            optionLabel="fullName" placeholder="Select Drivers/Yard Spotters" optionValue="userId"
                            (onChange)="onUserSelect($event)"></p-select>
                        <span class="text-danger" *ngIf="
                            jobForm.controls.assignedToUserId.touched &&
                            jobForm.controls.assignedToUserId.invalid && 
                            isBucket == false
                            ">Driver/Yard Spotter is required</span>
                    </div>
                    <div class="field col-12">
                        <div class="grid">
                            <div class="col-12">
                                <label htmlFor="jobPriorities">Spot Priorities<span class="text-danger">*</span></label>
                            </div>
                            <div class="col-12">
                                <div class="flex align-items-center gap-3">
                                    <div class="field-radiobutton radio-tag-red">
                                        <p-radioButton name="priority" value="HIGH" formControlName="priority"
                                            id="priority"></p-radioButton>
                                        <p-tag rounded="true" severity="danger" value="High"></p-tag>
                                    </div>
                                    <div class="field-radiobutton radio-tag-green">
                                        <p-radioButton name="priority" value="MEDIUM" formControlName="priority"
                                            id="priority2"></p-radioButton>
                                        <p-tag rounded="true" severity="primary" value="Medium"></p-tag>
                                    </div>
                                    <div class="field-radiobutton">
                                        <p-radioButton name="priority" value="LOW" formControlName="priority"
                                            id="priority3"></p-radioButton>
                                        <p-tag rounded="true" severity="success" value="Low"></p-tag>
                                    </div>
                                </div>
                            </div>
                            <span class="text-danger" *ngIf="
                            jobForm.controls.priority.touched &&
                            jobForm.controls.priority.invalid
                            ">Priority is required</span>
                        </div>
                    </div>
                    <div class="field col-12">
                        <button style="width: fit-content !important;" pButton class="p-button-primary mx-2"
                            type="button" (click)="onSubmit()" label="Submit"></button>
                        <button style="width: fit-content !important;" *ngIf="jobId && hideButtonsIfSpotter === false"
                            pButton class="p-button-danger mx-2" type="button" (click)="deleteJob()"
                            label="Delete"></button>
                    </div>
                </p-fluid>
            </form>
        </div>
    </div>
</div>

<p-dialog [(visible)]="deleteJobDialog" header="Confirm" [modal]="true" [style]="{width:'450px'}">
    <div class="flex align-items-center justify-content-start">
        <i class="pi pi-exclamation-triangle mr-3 my-4" style="font-size: 2rem"></i>
        <span *ngIf="jobId">Are you sure you want to delete this spot ?</span>
    </div>
    <ng-template pTemplate="footer">
        <button pButton pRipple icon="pi pi-times" class="p-button-text" label="No"
            (click)="deleteJobDialog = false"></button>
        <button pButton pRipple icon="pi pi-check" class="p-button-text" label="Yes" (click)="confirmDelete()"></button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="addFleetDialog" header="{{addTrailerHeader}}" [modal]="true" [style]="{width:'450px'}"
    (onHide)="cancelAddFleet()">
    <div *ngIf="!addFleetDialogConfirm" class="flex align-items-center justify-content-start">
        <i class="pi pi-exclamation-triangle mr-3 my-4" style="font-size: 2rem"></i>
        <span>The trailer/truck/container number you entered is not found in our system. Do you want to add?</span>
    </div>
    <form [formGroup]="fleetForm" *ngIf="addFleetDialogConfirm">
        <p-fluid class="grid">
            <div class="field col-12 md:col-6">
                <label htmlFor="type">Type<span class="text-danger">*</span></label>
                <p-select class="full-width" [options]="dropdownItems" formControlName="type" placeholder="Select Type"
                    optionLabel="name" optionValue="code"></p-select>
                <span class="text-danger" *ngIf="
                        fleetForm.controls.type.touched &&
                        fleetForm.controls.type.invalid && trailerSubmitted
                    ">Type is required</span>
            </div>
            <div class="field col-12 md:col-6">
                <label htmlFor="">Unit Number<span class="text-danger">*</span></label>
                <input pInputText id="unitNumber" formControlName="unitNumber" type="text" />
                <span class="text-danger" *ngIf="
                        fleetForm.controls.unitNumber.touched &&
                        fleetForm.controls.unitNumber.invalid && trailerSubmitted
                    ">Unit Number is required</span>
            </div>
            <div class="field col-12">

                <label htmlFor="carrier">Carrier<span class="text-danger">*</span></label>
                <p-autocomplete formControlName="carrier" (completeMethod)="getCarrier($event)"
                    [suggestions]="uniqueCarrierList" dataKey="carrier" [dropdown]="true" (onClear)="clearCarrier()">
                    <ng-template>
                        <div>{{uniqueCarrierList}}</div>
                    </ng-template>
                </p-autocomplete>
                <span class="text-danger" *ngIf="
                    fleetForm.controls.carrier.touched &&
                    fleetForm.controls.carrier.invalid && trailerSubmitted
                    ">Carrier is required
                </span>

            </div>

            <div class="field col-12">
                <label htmlFor="remarks">Notes</label>
                <textarea pTextarea formControlName="remarks" rows="3" cols="30"></textarea>
            </div>

        </p-fluid>
    </form>
    <ng-template pTemplate="footer">
        <button *ngIf="!addFleetDialogConfirm" pButton pRipple icon="pi pi-times" class="p-button-text" label="No"
            (click)="addFleetDialog = false"></button>
        <button *ngIf="!addFleetDialogConfirm" pButton pRipple icon="pi pi-check" class="p-button-text" label="Yes"
            (click)="addFleetConfirm()"></button>
        <button *ngIf="addFleetDialogConfirm" pButton pRipple icon="pi pi-times" class="p-button-text" label="Cancel"
            (click)="cancelAddFleet()"></button>
        <button *ngIf="addFleetDialogConfirm" pButton type="button" (click)="addFleet()" label="Submit"></button>
    </ng-template>
</p-dialog>