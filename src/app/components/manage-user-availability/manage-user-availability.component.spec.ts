import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageUserAvailabilityComponent } from './manage-user-availability.component';

describe('ManageUserAvailabilityComponent', () => {
  let component: ManageUserAvailabilityComponent;
  let fixture: ComponentFixture<ManageUserAvailabilityComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ManageUserAvailabilityComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageUserAvailabilityComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
