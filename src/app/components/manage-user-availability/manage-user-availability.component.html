<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>
    <div class="col-12">
        <div class="card">
            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <ng-template pTemplate="left">
                    <div class="my-2">
                        <h5 class="m-0">User Availability</h5>
                    </div>
                </ng-template>
                <ng-template pTemplate="right">
                    <div class="">
                        <!-- <h5>User Details</h5> -->
                        <div class="flex" *ngIf="name != undefined && name != ''">
                            <h6 class="mr-2">Name: </h6>
                            {{ name }}
                        </div>
                        <div class="flex" *ngIf="email != undefined && email != ''">
                            <h6 class="mr-2">Email: </h6>
                            {{ email }}
                        </div>
                    </div>
                </ng-template>
            </p-toolbar>
           

            <div class="user-avail-grid grid">
                <div class="col-12 card" style="background-color: #ffffff;">
                    <div class="card1">
                        <!-- <h5>Availability</h5>
                        <hr /> -->
                        <div class="field d-flex">
                            <div class="col-2">
                                <h6>Day</h6>
                            </div>
                            <div class="col-2">
                                <h6>Start Time</h6>
                            </div>
                            <div class="col-2">
                                <h6>End Time</h6>
                            </div>
                            <div class="col-2">
                                <h6>Break Start Time</h6>
                            </div>
                            <div class="col-2">
                                <h6>Break End Time</h6>
                            </div>
                            <div class="col-2 text-center">
                                <h6>Available</h6>
                            </div>
                        </div>
                        <hr />
                        <div class="field d-flex" *ngFor="let item of availabilityList">
                            <div class="col-2">
                                <label>{{item.dayOfWeek}}</label>
                            </div>
                            <div class="col-2">
                                <p-datepicker inputId="calendar-timeonly" [(ngModel)]="item.startingTime"
                                    [timeOnly]="true" hourFormat="12" (ngModelChange)="availabilityChanged(item)"></p-datepicker>
                            </div>
                            <div class="col-2">
                                <p-datepicker inputId="calendar-timeonly" [(ngModel)]="item.endingTime"
                                    [timeOnly]="true" hourFormat="12" (ngModelChange)="availabilityChanged(item)"></p-datepicker>
                                <span class="text-danger" *ngIf="item.isRequiredError">Please provide both starting and ending time.</span>
                            </div>
                            <div class="col-2">
                                <p-datepicker inputId="calendar-timeonly" [(ngModel)]="item.breakStartingTime"
                                [timeOnly]="true" hourFormat="12" (ngModelChange)="availabilityChanged(item)"></p-datepicker>
                            </div>
                            <div class="col-2">
                                <p-datepicker inputId="calendar-timeonly" [(ngModel)]="item.breakEndingTime"
                                [timeOnly]="true" hourFormat="12" (ngModelChange)="availabilityChanged(item)"></p-datepicker>
                            </div>
                            <div class="col-2 text-center">
                                <p-checkbox [(ngModel)]="item.isActive" [binary]="true" inputId="binary"
                                    (click)="availabilityChanged(item)"
                                    [disabled]="item.endingTime == null || item.startingTime == null"></p-checkbox>
                            </div>
                        </div>
                        <div class="field flex justify-content-center">
                            <button pButton pRipple label="Save" icon="pi pi-check" class="p-button-primary"
                        (click)="saveUserAvailability()"></button>
                        </div>
                    </div>
                </div>
                <div class="col-12 card">
                    <div class="card1">
                        <!-- <h5>Time Off / Working Day</h5> -->
                        <!-- <hr/> -->
                        <p-table showGridlines #dt [value]="userAvailabilityException"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [rows]="50" [rowHover]="true" dataKey="id">
                            <ng-template #caption>
                                <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                                  <h5 class="m-0">Time Off / Working Day</h5>
                                  <div class="my-2 flex align-items-baseline">
                                    <button pButton pRipple label="New" icon="pi pi-plus" class="p-button-success"
                                        (click)="newOffDayDialog = true"></button>
                                  </div>
                                </div>
                              </ng-template>
                           
                            <ng-template #header>
                                <tr>

                                    <th pSortableColumn="date">Date</th>
                                    <th pSortableColumn="type">Type of day</th>
                                    <th pSortableColumn="startingTime">Start Time</th>
                                    <th pSortableColumn="endingTime">End Time</th>
                                    <th pSortableColumn="endingTime">Break Start Time</th>
                                    <th pSortableColumn="endingTime">Break End Time</th>
                                    <th></th>

                                </tr>


                            </ng-template>

                            <ng-template #body let-item>
                                <tr>
                                    <td>
                                        {{item.date}}
                                    </td>

                                    <td>
                                        {{item.type}}
                                    </td>

                                    <td>
                                        {{item.startingTime}}
                                    </td>

                                    <td>
                                        {{item.endingTime}}
                                    </td>

                                    <td>
                                        {{item.breakStartingTime}}
                                    </td>
                                    <td>
                                        {{item.breakEndingTime}}
                                    </td>
                                    <td>
                                        <button pButton type="button" class="p-button-help"
                                            icon="pi pi-ellipsis-h" (click)="menu.toggle($event);toggleMenu(item)"></button>
                                            <p-menu #menu [popup]="true" [model]="menuItems" appendTo="body"></p-menu>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="7">No Data found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </div>
            </div>
            <p-dialog [(visible)]="newOffDayDialog" header="Details" [modal]="true" (onHide)="hideDialog()"
                [style]="{width:'550px'}">
                <form [formGroup]="newExceptionForm" [style]="{minHeight: '440px'}">
                    <p-fluid class="grid">
                        <div class="col-12 md:col-12 field">
                            <label>Date</label>
                            <p-datepicker formControlName="date" appendTo="body"></p-datepicker>
                            <span class="text-danger" *ngIf="
                                                newExceptionForm.controls.date.touched &&
                                                newExceptionForm.controls.date.invalid && isSubmitted
                                                ">Date is required</span>
                        </div>
                        <!-- <div class="field col-12 md:col-12">
                                            <label htmlFor="date">Date<span class="text-danger">*</span></label>
                                            <p-datepicker ></p-datepicker>
                                            <p-select class="full-width" placeholder="Select Date" formControlName="clientId" [options]="clientList" (onChange)="onClientSelect($event)" optionLabel="clientName" optionValue="clientId"></p-select>
                                            <span
                                                class="text-danger"
                                                *ngIf="
                                                jobForm.controls.clientId.touched &&
                                                jobForm.controls.clientId.invalid
                                                "
                                                >Client is required</span
                                                >
                                        </div> -->
                        <div class="col-12 md:col-6 field">
                            <label>Start Time</label>
                            <p-datepicker inputId="calendar-timeonly" formControlName="startingTime" [timeOnly]="true"
                                hourFormat="12"></p-datepicker>
                            <span class="text-danger" *ngIf="
                                            newExceptionForm.controls.startingTime.touched &&
                                            newExceptionForm.controls.startingTime.invalid && isSubmitted
                                            ">Start Time is required</span>
                        </div>
                        <div class="col-12 md:col-6 field">
                            <label>End Time</label>
                            <p-datepicker inputId="calendar-timeonly" formControlName="endingTime" [timeOnly]="true"
                                hourFormat="12"></p-datepicker>
                            <span class="text-danger" *ngIf="
                                            newExceptionForm.controls.endingTime.touched &&
                                            newExceptionForm.controls.endingTime.invalid && isSubmitted
                                            ">End Time is required</span>
                        </div>
                        <div class="col-12 md:col-6 field">
                            <label>Break Start Time</label>
                            <p-datepicker inputId="calendar-timeonly" formControlName="breakStartingTime" [timeOnly]="true"
                                hourFormat="12"></p-datepicker>
                        </div>
                        <div class="col-12 md:col-6 field">
                            <label>Break End Time</label>
                            <p-datepicker inputId="calendar-timeonly" formControlName="breakEndingTime" [timeOnly]="true"
                                hourFormat="12"></p-datepicker>
                        </div>
            
            
                        <div class="col-12 md:col-12 field">
                            <label>Type of day</label>
                            <p-select class="full-width" placeholder="Select Type" formControlName="type" [options]="type"
                                optionLabel="name" optionValue="code"></p-select>
                            <span class="text-danger" *ngIf="
                                            newExceptionForm.controls.type.touched &&
                                            newExceptionForm.controls.type.invalid && isSubmitted
                                            ">Type is required</span>
                        </div>
                        <span class="text-danger" *ngIf="addExceptionFormValidity == false">Select both break starting time and
                            break ending time</span>
                    </p-fluid>
                </form>
                <ng-template pTemplate="footer">
                    <div style="margin-bottom: 5px;">
                        {{errorMessage}}
                    </div>
                    <div>
                        <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-secondary"
                            (click)="hideDialog()"></button>
                        <button pButton pRipple label="Save" icon="pi pi-check" class="p-button-success"
                            (click)="saveNewException()"></button>
                    </div>
            
                </ng-template>
            </p-dialog>
            <p-dialog [(visible)]="errorMessageDialog" [modal]="true" (onHide)="hideDialog1()">
                <ng-template #header>
                    <h2>Alert</h2>
                </ng-template>
                <ng-template pTemplate="content">
                    {{errorMessage}}
                </ng-template>
                <ng-template pTemplate="footer">
                    <button pButton pRipple label="Close" icon="pi pi-check" class="p-button-secondary"
                    (click)="hideDialog1()"></button>
                </ng-template>
            </p-dialog>

        </div>
    </div>
</div>