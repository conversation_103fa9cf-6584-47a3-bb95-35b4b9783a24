import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UserprivacyPolicyComponent } from './userprivacy-policy.component';

describe('UserprivacyPolicyComponent', () => {
  let component: UserprivacyPolicyComponent;
  let fixture: ComponentFixture<UserprivacyPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UserprivacyPolicyComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UserprivacyPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
