import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MenuItem } from 'primeng/api';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { ManageContactUsService } from './manage-contact-us.service';
import { AppAlertService } from 'src/app/app-alert/service/app-alert.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { Router } from '@angular/router';
import { TokenService } from 'src/app/security/token.service';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { ContactUsDataService } from 'src/app/service/contact-us-data.service';

@Component({
  selector: 'app-manage-contact-us',
  templateUrl: './manage-contact-us.component.html',
  styleUrls: ['./manage-contact-us.component.scss'],
  standalone: false
})
export class ManageContactUsComponent implements OnInit {

  breadcrumbItems: MenuItem[] = [];
  contactForm: FormGroup;
  userRoles: any;
  dropdownType = [
    { label: 'Dashboard', value: 'Dashboard' },
    { label: 'Clients', value: 'Clients' },
    { label: 'Locations', value: 'Locations'},
    { label: 'Asset Inventory', value: 'Asset Inventory'},
    { label: 'Trailer Audit', value: 'Trailer Audit' },
    { label: 'Entry / Exit Detail', value: 'Entry / Exit Detail' },
    { label: 'Users', value: 'Users' },
    { label: 'Spots', value: 'Spots' },
    { label: 'Reports', value: 'Reports' },
    { label: 'Messages', value: 'Messages' },
    { label: 'Others', value: 'Others' },
    // { label: 'Mobile App Updates', value: 'Mobile App Updates' },
    // { label: 'Canvas Tool', value: 'Canvas Tool' },
    // { label: 'Contact Us', value: 'Contact Us' },
  ];
  clientId: any;
  userQuery: IpagedQuery;
  canceled: boolean = false;
  submitted: boolean = false;
  
  constructor(
    private fb: FormBuilder,
    private loader: AppLoaderService,
    private manageContactUsService: ManageContactUsService,
    private alertService: AppAlertService,
    private errorService: ErrorService,
    private router: Router,
    private tokenService: TokenService,
    private manageClientService: ManageClientsService,
    private contactUsDataService: ContactUsDataService,
  ) { 

    this.contactForm = this.fb.group({
          subject: ['', Validators.required],
          messageBody: ['', Validators.required],
          type: ['', Validators.required]
        });

  }

  ngOnInit(): void {
    this.breadcrumbItems.push({ label: 'Contact Us' });
    this.userQuery = { isActive: true, size: 1000, page: 0 };
    
    this.userRoles = this.tokenService.getUserRoles();
    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_CLIENT)) {
      this.dropdownType = this.dropdownType.filter(role => role.value !== 'Clients');
    }
    if (this.userRoles.some(role => role !== APP_CONSTANTS.USER_ROLES.ROLE_CLIENT)) {
      this.dropdownType = this.dropdownType.filter(role => role.value !== 'Locations');
    }
    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_DRIVER)) {
      this.dropdownType = this.dropdownType.filter(role => role.value !== 'Dashboard' && role.value !== 'Trailer Audit' && role.value !== 'Entry / Exit Detail' && role.value !== 'Reports' );
    }
    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_SPOTTER)) {
      this.dropdownType = this.dropdownType.filter(role => role.value !== 'Dashboard' && role.value !== 'Trailer Audit' && role.value !== 'Entry / Exit Detail' && role.value !== 'Reports' );
    }
    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_GUARD)) {
      this.dropdownType = this.dropdownType.filter(role => role.value !== 'Dashboard' && role.value !== 'Trailer Audit' && role.value !== 'Reports' );
    }

    this.clientId = this.tokenService.getUserClientId();

    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_SUPERVISOR || role === APP_CONSTANTS.USER_ROLES.ROLE_CLIENT)){
        if(this.clientId){
          this.getLoggedInClientList(this.userQuery, this.clientId);
        }
    }
    
    if(this.contactUsDataService.getData()){
      this.contactForm.patchValue(this.contactUsDataService.getData());
    }

  }

  onSubmit() {
     if (this.contactForm.invalid) {
      this.contactForm.markAllAsTouched();
    } else {
        this.loader.show();
        this.manageContactUsService.sendContactUsEmail(this.contactForm.value).subscribe(res => {
          this.loader.hide();
          this.alertService.alertSuccess([`Thank you for contacting us.`]);
          this.contactForm.reset();
          this.contactUsDataService.clearData();
          this.submitted = true;
        }, (error) => {
          this.loader.hide();
          this.errorService.handleError(error, true);
        })
    }
  }

  getLoggedInClientList(query, currentUser) {
    this.loader.show()
    this.manageClientService.viewClients(query, null, currentUser).subscribe(response => {
      // this.clientList = response.list;
      if (!response.list[0].trailerAudit) {
        this.dropdownType = this.dropdownType.filter(role => role.value !== 'Trailer Audit');
      }

      this.loader.hide();
    }, (error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }

  ngOnDestroy() {
    if (!this.canceled || !this.submitted) {
      this.contactUsDataService.setData(this.contactForm.value);


    }
  }

  onCancel() {
    this.canceled = true;
    this.contactUsDataService.clearData();
    this.contactForm.reset();
  }

  // onTypeChange() {
  //   if (this.submitted) {
  //     this.submitted = false;
  //   }
  // }

}
