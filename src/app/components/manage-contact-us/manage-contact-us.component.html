<!-- <div class="grid">
	<div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>
	<div class="col-12">
		<div class="card">
			<h5>Contact Us</h5>
			<form [formGroup]="contactForm">
				<div class="p-fluid p-formgrid grid">
					<div class="field col-6 md:col-6">
                        <label htmlFor="client">Select Type<span class="text-danger">*</span></label>
						<p-dropdown [options]="dropdownType" formControlName="type" placeholder="Select Type" optionLabel="label" optionValue="value"></p-dropdown>
                        <span
                            class="text-danger"
                            *ngIf="
								contactForm.controls.type.touched &&
								contactForm.controls.type.invalid
                            "
                            >Type is required</span
                        >
                    </div>
					
					<div class="field col-6 md:col-6">
						<label htmlFor="">Subject<span class="text-danger">*</span></label>
						<input pInputText id="subject" formControlName="subject" type="text" />
						<span
                            class="text-danger"
                            *ngIf="
								contactForm.controls.subject.touched &&
								contactForm.controls.subject.invalid
                            "
                            >Subject is required</span
                        >
					</div>
					
					<div class="field col-12">
						<label htmlFor="messageBody">Message<span class="text-danger">*</span></label>
						<textarea pInputTextarea  formControlName="messageBody" rows="3" cols="30"></textarea>
                        <span
                            class="text-danger"
                            *ngIf="
								contactForm.controls.messageBody.touched &&
								contactForm.controls.messageBody.invalid
                            "
                            >Message is required</span
                        >
					</div>
					<button pButton type="button" (click)="onSubmit()" label="Submit"></button>
					
				</div>
			</form>
		</div>
	</div>
</div>

 -->

<div class="grid">
	<div class="col-12">
		<div class="card card-w-title">
			<p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
		</div>
	</div>
</div>
<div class="w-full contact flex justify-content-center align-items-center">
	<div class="card">
		<h5>Contact Us</h5>
		<p-fluid>
			<form [formGroup]="contactForm">
				<div class="p-formgrid grid">
					<div class="field col-6 md:col-6">
						<label htmlFor="client">Issue Location<span class="text-danger">*</span></label>
						<p-select [options]="dropdownType" formControlName="type" placeholder="Select Issue Location"
							optionLabel="label" optionValue="value"></p-select>
						<span class="text-danger" *ngIf="
								contactForm.controls.type.touched &&
								contactForm.controls.type.invalid
                            ">Issue Location is required</span>
					</div>

					<div class="field col-6 md:col-6">
						<label htmlFor="">Subject<span class="text-danger">*</span></label>
						<input pInputText id="subject" formControlName="subject" type="text" />
						<span class="text-danger" *ngIf="
								contactForm.controls.subject.touched &&
								contactForm.controls.subject.invalid
                            ">Subject is required</span>
					</div>

					<div class="field col-12">
						<label htmlFor="messageBody">Message<span class="text-danger">*</span></label>
						<textarea pInputTextarea  formControlName="messageBody" rows="3" cols="30"></textarea>
						<span class="text-danger" *ngIf="
								contactForm.controls.messageBody.touched &&
								contactForm.controls.messageBody.invalid
                            ">Message is required</span>
					</div>
					<div class="w-full flex justify-content-center">
						<button pButton class="p-button-secondary mx-2" style="width: 100px;" type="button"
							(click)="onCancel()" label="Cancel"></button>
						<button pButton type="button" (click)="onSubmit()" style="width: 100px;"
							label="Submit"></button>
					</div>
				</div>
			</form>
		</p-fluid>
	</div>
</div>