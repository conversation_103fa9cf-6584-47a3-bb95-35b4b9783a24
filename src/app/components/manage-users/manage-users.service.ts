import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { IpagedQuery } from 'src/app/model/IpagedQuery';

@Injectable({
  providedIn: 'root'
})
export class ManageUsersService {

  constructor(private http: HttpClient) { }



  viewUsers(query: IpagedQuery, clientId?: any, firstName?: string, roleName?: string, allUsers?: boolean, locationIds?: string[], userId?: string): Observable<any> {
    var params = new HttpParams({
      fromObject: {
        isActive: query.isActive.valueOf(),
        page: query.page.toString(),
        size: query.size.toString(),
        sort: 'createdDate,desc',
      }
    })

    if (roleName) {
      params = params.append("roleName", roleName);
    }

    if (firstName) {
      params = params.append("firstName", firstName);
    }

    if (allUsers) {
      params = params.append("allUsers", allUsers);
    }

    if (locationIds) {
      params = params.append("locationIds", locationIds.join(","));
    }



    if (userId) {
      params = params.append("uuid", userId);
    }

    //  if(roleNames)
    //   {
    //     params = params.append("roleNames", roleNames.join(","));
    //   }

    return clientId ? this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/clients/${clientId}/users`, { params }) : this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users`, { params });
  }

  resetpassword(userId: any): Observable<any> {
    return this.http.patch(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/${userId}/resetPassword`, null);
  }

  activateInactiveUser(userId: string): Observable<any> {
    return this.http.post(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/inactive`, { userId });
  }

  getSheduledDriversAndSpotters(): Observable<any> {
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/scheduledusers/buckets`);
  }
}
