<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>
    <div class="col-12">
        <div class="card">
            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <ng-template pTemplate="left">
                    <div class="my-2">
                        <h5 class="m-0">Users</h5>
                    </div>
                </ng-template>

                <ng-template pTemplate="right">
                    <button *ngIf="userType !== 'ROLE_CLIENT' && hideButtonsIfDriver !== true && hideButtonsIfGuard === false && hideButtonsIfSpotter === false" pButton pRipple label="New" icon="pi pi-plus" class="p-button-success mr-2" (click)="routeToAddUser()"></button>
                    <!-- <button pButton pRipple label="Export" icon="pi pi-upload" class="p-button-help" (click)="exportExcel()"></button> -->

                    <p-splitButton *ngIf="hideButtonsIfDriver !== true && hideButtonsIfSpotter === false" label="Export" [model]="items" (onClick)="exportExcel()" raised severity="help"></p-splitButton>
                    <!-- <button *ngIf="userType == 'ROLE_ADMIN' || userType == 'ROLE_SUPERVISOR' || userType == 'ROLE_IT'" pButton pRipple label="Overtime Users" icon="pi pi-plus" class="p-button-secondary" (click)="routeToOvertimeUsers()"></button> -->
                </ng-template>
            </p-toolbar>

            <p-table showGridlines #dt [value]="userList" [loading]="loading" styleClass="p-datatable-gridlines p-datatable-striped" [responsive]="true" [columns]="cols" [rows]="10"  [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">
                <ng-template #caption>
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <p-iconfield iconPosition="left" class="ml-auto">
                            <p-inputicon>
                                <i class="pi pi-search"></i>
                            </p-inputicon>
                            <input pInputText type="text" (input)="search()" [(ngModel)]="searchboxValue" placeholder="Search Name" />
                        </p-iconfield>
                    </div>
                </ng-template>
                <ng-template #header>
                    <tr>
                        <th style="width: 90px"></th>
                        <th pSortableColumn="firstName">First Name</th>
                        <th pSortableColumn="lastName">LastName</th>
                        <th pSortableColumn="status">Status</th>
                        <th pSortableColumn="client">Client</th>
                        <th pSortableColumn="role">Role</th>
                        <th pSortableColumn="email">Email</th>
                        <th pSortableColumn="idleTime"><div>Idle Time</div>(dd:hh:mm:ss)</th>
                        <th pSortableColumn="breakTime"><div>Break Time</div>(dd:hh:mm:ss)</th>
                        <th pSortableColumn="overTime"><div>OverTime</div>(dd:hh:mm:ss)</th>
                    </tr>
                    <tr>
                        <th></th>
                        <th>
                            <!-- <p-columnFilter type="text" field="name"></p-columnFilter> -->
                        </th>
                        <th>
                            <!-- <p-columnFilter type="text" field="country.name"></p-columnFilter> -->
                        </th>
                        <th>
                            <p-select [options]="dropdownStatus" placeholder="Select Status" optionLabel="name" optionValue="code" (onChange)="filterUserByStatus($event)"></p-select>
                        </th>
                        <th>
                            <p-select *ngIf="userType !== 'ROLE_CLIENT'" [showClear]="true" placeholder="Select Client" [options]="clientList" (onChange)="filterUsersByClient($event)" optionLabel="clientName" optionValue="clientId"></p-select>
                        </th>
                        <th>
                            <p-select [options]="roleList" placeholder="Select Role" optionLabel="name" optionValue="code" [showClear]="true" (onChange)="filterUserByRole($event)"></p-select>
                            <!-- <p-columnFilter type="boolean" field="verified"></p-columnFilter> -->
                        </th>
                        <th>

                        </th>
                        <th>

                        </th>
                        <th>

                        </th>
                        <th>

                        </th>
                    </tr>
                </ng-template>
                <ng-template #body let-user>
                    <tr>
                        <td>
                            <button *ngIf="(userType !== 'ROLE_CLIENT' && hideButtonsIfDriver !== true 
                                            && hideButtonsIfGuard === false && hideButtonsIfSpotter === false 
                                            && !hasSupervisorRole(user)) || loggedInUserId == user.userId" 
                                    pButton pRipple icon="pi pi-pencil" pTooltip="Edit" 
                                    class="p-button-rounded p-button-warning mr-2" 
                                    (click)="routeToEditUser(user.userId)">
                            </button>
                            <button *ngIf="user.isActive === false && hideButtonsIfDriver !== true "  pButton pRipple icon="pi pi-refresh" pTooltip="Activate" class="p-button-rounded p-button-success mr-2" (click)="activateUser(user.userId)"></button>
                            <!-- <button pButton pRipple icon="pi pi-key" pTooltip="Reset Password" class="p-button-rounded p-button-primary" (click)="resetPassword(user.userId)"></button> -->
                            <button *ngIf="(userType == 'ROLE_ADMIN' || userType == 'ROLE_SUPERVISOR' || userType == 'ROLE_IT') && (user.roles[0].roleName == 'DRIVER' || user.roles[0].roleName == 'SPOTTER') && (!user.isOnOverTime)"  pButton pRipple icon="pi pi-clock" pTooltip="Add to Overtime" class="p-button-rounded p-button-success mr-2" (click)="showAddOverTimeModal(user.userId)"></button>
                            <button *ngIf="(userType == 'ROLE_ADMIN' || userType == 'ROLE_SUPERVISOR' || userType == 'ROLE_IT') && (user.roles[0].roleName == 'DRIVER' || user.roles[0].roleName == 'SPOTTER') && (user.isOnOverTime)"  pButton pRipple icon="pi pi-stop-circle" pTooltip="Stop Overtime" class="p-button-rounded p-button-danger mr-2" (click)="showDeleteOverTimeModal(user.userId)"></button>                           
                            <button *ngIf="(userType == 'ROLE_ADMIN' || userType == 'ROLE_SUPERVISOR' || userType == 'ROLE_IT') && (user.roles[0].roleName == 'DRIVER' || user.roles[0].roleName == 'SPOTTER')"  pButton pRipple icon="pi pi-check-circle" pTooltip="User Availability" class="p-button-rounded p-button-help mr-2" (click)="routeToUserAvailability(user)"></button>
                        </td>
                        <td>
                            {{user.firstName}}
                        </td>
                        <td>
                            {{user.lastName}}
                        </td>
                        <td *ngIf="user.isActive" style="color: green;">
                            <i class="pi pi-check"></i> {{user.isActive ? 'Active' : 'Inactive'}}
                        </td>
                        <td *ngIf="!user.isActive" style="color: red;">
                            <i class="pi pi-minus-circle"></i> {{user.isActive ? 'Active' : 'Inactive'}}
                        </td>
                        <td>
                            <span *ngFor="let client of user.clients;let i=index">
                                {{client ? client?.clientName : 'None'}} {{i < user.clients.length - 1 ? ',' : ''}}
                            </span>
                        </td>
                        <td>
                            <span *ngFor="let role of user.roles;let i=index">
                                {{role ? role?.roleName : 'None'}} {{i < user.roles.length - 1 ? ',' : ''}}
                            </span>
                            {{user.role}}
                        </td>
                        <td>
                            {{user.email}}
                        </td>
                        <td>
                            <span *ngIf="(user.roles[0].roleName == 'DRIVER' || user.roles[0].roleName == 'SPOTTER')">{{user.idleTimeSinceSeconds ? user.idleTimeSinceSeconds: ' ' }}</span>
                        </td>
                        <td>
                            <span *ngIf="(user.roles[0].roleName == 'DRIVER' || user.roles[0].roleName == 'SPOTTER')">{{user.pendingBreakTimeSinceSeconds? user.pendingBreakTimeSinceSeconds: ' ' }}</span>
                        </td>
                        <td>
                            <span *ngIf="(user.roles[0].roleName == 'DRIVER' || user.roles[0].roleName == 'SPOTTER')">{{user.pendingOverTimeSinceSeconds ? user.pendingOverTimeSinceSeconds : ' ' }}</span>
                     
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="7">No User found.</td>
                    </tr>
                </ng-template>
            </p-table>
            



            <p-paginator [rows]="100" [showCurrentPageReport] = "true" currentPageReportTemplate="Showing {first} to {last} of {totalRecords}" [totalRecords]="totalRecords"  (onPageChange)="paginate($event)"></p-paginator>
            
        </div>
    </div>
    
    <p-dialog [(visible)]="resetPasswordModal" [style]="{width: '450px'}" header="New Password" [modal]="true">
        <ng-template pTemplate="content">
            <!-- <form [formGroup]="resetPasswordForm">
                <div class="field">
                    <label for="name">New Password</label>
                    <input type="text" pInputText id="name" formControlName="resetPassword" required autofocus [ngClass]="{'ng-invalid ng-dirty' : resetPasswordForm.controls.resetPassword.touched &&
                    resetPasswordForm.controls.resetPassword.invalid}"/>
                    <small class="ng-dirty ng-invalid" *ngIf="resetPasswordForm.controls.resetPassword.touched &&
                    resetPasswordForm.controls.resetPassword.invalid">New Password is required.</small>
                </div>
            </form> -->
            <div class="flex align-items-center justify-content-center">
                <h4 class="mb-0">{{password}}</h4>
            </div>
        </ng-template>

        <ng-template pTemplate="footer">
            <!-- <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text" (click)="resetPasswordModal=false"></button> -->
            <button pButton pRipple label="Ok" icon="pi pi-check" class="p-button-text" type="button" (click)="resetPasswordModal=false"></button>
        </ng-template>
    </p-dialog>
    <p-dialog [(visible)]="addOverTimeModal" [style]="{width: '450px'}" header="Add OverTime" [modal]="true">
        <div class="flex align-items-center justify-content-start">
            <i class="pi pi-exclamation-triangle mr-3 my-4" style="font-size: 2rem"></i>
            <span>Are you sure this driver/spotter is doing Overtime ?</span>
        </div>
        <ng-template pTemplate="footer">
            <button pButton pRipple icon="pi pi-times" class="p-button-text" label="No"
                (click)="hideAddOverTimeModal()"></button>
            <button pButton pRipple icon="pi pi-check" class="p-button-text" label="Yes" (click)="addOverTimeForUser()"></button>
        </ng-template>

    </p-dialog>
    <p-dialog [(visible)]="deleteOverTimeModal" [style]="{width: '450px'}" header="Delete OverTime" [modal]="true" class="p-fluid">
        <div class="flex align-items-center justify-content-start">
            <i class="pi pi-exclamation-triangle mr-3 my-4" style="font-size: 2rem"></i>
            <span> Are you sure this driver/spotter is not doing Overtime ?</span>
        </div>
        <ng-template pTemplate="footer">
            <button pButton pRipple icon="pi pi-times" class="p-button-text" label="No"
                (click)="hideDeleteOverTimeModal()"></button>
            <button pButton pRipple icon="pi pi-check" class="p-button-text" label="Yes" (click)="deleteOverTimeForUser()"></button>
        </ng-template>

    </p-dialog>

</div>
