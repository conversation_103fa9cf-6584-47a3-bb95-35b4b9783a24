<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>

    <div class="col-12">
        <div class="card">
            
            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <div class="w-full">
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <div class="my-2">
                            <h5 class="m-0">Trailer Report By Load Status</h5>
                        </div>

                        <div>
                        </div>
                    </div>    
            
                <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center mrgt-30">
                    
                        <span>
                            <p-select  [showClear]="true"
                                placeholder="Select Client" [options]="clientList"
                                (onChange)="filterJobsByClient($event)" optionLabel="clientName" optionValue="clientId"
                                class="mrgr-10"></p-select>

                            <p-select  [showClear]="true"
                                placeholder="Select Location" [options]="locationList"
                                (onChange)="filterJobsByLocation($event)" optionLabel="locationName" optionValue="locationId"
                                class="mrgr-10"></p-select>  
                            
                            <p-select class="ml-2" [showClear]="true" [options]="trailerStatus" 
                                placeholder="Select Load Status" optionLabel="name" optionValue="code"
                                (onChange)="selectJobTrailerStatus($event)"></p-select> 
                        </span>
                    
                  
                        <span class="block mt-2 md:mt-0 p-input-icon-left">
                            <span class="mr-2 font-medium">From Date : <input pInputText type="date"
                                    [(ngModel)]="fromDate" (change)="filterJobsByDate()" name="fromDate"
                                    placeholder="Select Date" /></span>
                            <span class="mx-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toDate"
                                    (change)="filterJobsByDate()" name="toDate" placeholder="Select Date" /></span>
                        </span>
                    
                    
                </div>
              </div>  
            </p-toolbar>

            <p-table showGridlines #dt5 [value]="jobs" [loading]="loading"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"                         
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template #header>
                                <tr>
                                     
                                    <th pSortableColumn="trailerTruck">Trailer Number</th>
                                    <th pSortableColumn="jobNumber">Current Location</th>
                                    <th pSortableColumn="pickupLocation">Carrier</th>
                                    <th pSortableColumn="dropLocation">Load Status</th>
                                    <th pSortableColumn="jobPickupTime">Sequence Number</th>
                                    <th pSortableColumn="jobDropoffTime">Pickup Date and Time</th>
                                    <th pSortableColumn="carrier">Dropoff Date and Time</th>
                            
                                </tr>
                            </ng-template>
                            <ng-template #body let-job let-columns="columns" let-index="rowIndex">
                                <tr [pReorderableRow]="index">
                                   
                                    <td>
                                        {{job.fleet?.unitNumber}}
                                    </td>
                                    <td>
                                        {{job.fleet.spot? job.fleet.spot?.locationName : '' }}
                                        <br>
                                        {{job.fleet.spot? job.fleet.spot?.spotName : '' }}
                                    </td>
                                    <td>
                                        {{job.fleet?.carrier}}
                                    </td>
                                    <td>
                                        {{job.fleetStatus}}
                                    </td>
                                    <td>
                                        {{job.sequenceAsn}}
                                    </td>
                                    <td>
                                        {{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                    </td>
                                    <td>
                                        {{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                    </td> 
                                   
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="16">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecords"
                            (onPageChange)="paginate($event)" #paginator></p-paginator>

        </div>
    </div>
</div>

