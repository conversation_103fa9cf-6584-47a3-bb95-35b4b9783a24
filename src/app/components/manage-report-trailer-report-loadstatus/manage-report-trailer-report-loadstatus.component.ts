import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { Paginator } from 'primeng/paginator';
import { Subscription, map } from 'rxjs';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { ManageReportService } from '../manage-report/manage-report.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { TokenService } from 'src/app/security/token.service';
import { ManageUsersService } from '../manage-users/manage-users.service';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { ManageLocationsService } from '../manage-locations/manage-locations.service';

@Component({
    selector: 'app-manage-report-trailer-report-loadstatus',
    templateUrl: './manage-report-trailer-report-loadstatus.component.html',
    styleUrls: ['./manage-report-trailer-report-loadstatus.component.scss'],
    standalone: false
})
export class ManageReportTrailerReportLoadstatusComponent implements OnInit {

 breadcrumbItems: MenuItem[];
     jobs: [];
     loading = false;
     totalRecords: any;
     query: IpagedQuery;
     userQuery: IpagedQuery;
     userRoles: any;
     clientId: string;
     fromDate: any;
     toDate: any;
     subscription = new Subscription();
     clientList = [];
     isSupervisor: boolean = false;
     trailerStatus = [
      {name:'Empty', code: 'EMPTY'},
      {name:'Loaded', code:'FULL'}
    ];
     selectedJobTrailerStatus: string;
     locationList = [];
     locationId: string;

     @ViewChild('paginator', { static: false }) paginator: Paginator;
   
     constructor(private manageReportService : ManageReportService,
                 private errorService: ErrorService,
                 private loader: AppLoaderService,
                 private tokenService: TokenService,
                 private manageUserService: ManageUsersService,
                 private manageClientService: ManageClientsService,
                 private manageLocationService: ManageLocationsService) { }
   
     ngOnInit(): void {
   
       this.breadcrumbItems = [];
       this.breadcrumbItems.push({ label: 'Moves By Spotter' });
       this.query = { isActive: true, size: 50, page: 0 };
       this.getJobs();
       this.getClientList(this.query);
      
     }
   
     private updateCurrentPage(currentPage: number): void {
       setTimeout(() => this.paginator.changePage(currentPage));
     }
   
     getJobs() {
        
       this.loading = true;  
       this.manageReportService.viewMoves(this.query, "COMPLETED", null, this.clientId, this.fromDate, this.toDate, null, this.selectedJobTrailerStatus, this.locationId)
           .subscribe(response => {
           
               this.jobs = response.list;
               this.totalRecords = response.totalElements;
               this.loading = false;
   
             }, (error) => {
             this.loader.hide();
             this.loading = false;
             this.errorService.handleError(error, true);
           })
       }
   
       paginate(event) {
         this.query.page = event.page;
         this.getJobs();
       }
   
     
   
         getClientList(query) {
           this.loader.show();
           this.manageClientService.viewClients(query).subscribe(response => {
             this.clientList = response.list;
             this.loader.hide();
             if(this.isSupervisor)
             {
               this.clientId = this.clientList[0].clientId;
             }
           }, (error) => {
             this.loader.hide();
             this.errorService.handleError(error, true);
           })
         }
   
   
         filterJobsByClient(event) {
         
           this.clientId = event.value;
           if (this.jobs.length == 0) {
             this.getJobs();
           }
           else {
             this.updateCurrentPage(0);
           }
           this.locationList = [];
          
           if(this.clientId != null) {
           
            this.getClientLocations();
           }
           
         } 

         filterJobsByLocation(event) {

            this.locationId = event.value;
            if (this.jobs.length == 0) {
              this.getJobs();
            }
            else {
              this.updateCurrentPage(0);
            }
         }
   
         filterJobsByDate() {
           if (this.toDate !== undefined) {
             if (this.jobs.length == 0) {
               this.getJobs();
             }
             else {
               this.updateCurrentPage(0);
             }
           }
         }
   
         selectJobTrailerStatus(event) {

          this.selectedJobTrailerStatus = event.value;
          if (this.jobs.length == 0) {
            this.getJobs();
          }
          else {
            this.updateCurrentPage(0);
          }
         }


         getClientLocations() {
          
          this.subscription.add(
              this.manageLocationService.viewLocations(this.query,this.clientId).subscribe(response=>{
                  this.locationList = response.list;
                  this.totalRecords = response.totalElements;
                  this.loading = false;
              },(error) => {
                  this.loader.hide();
                  this.errorService.handleError(error, true);
              })
          )
      }

}
