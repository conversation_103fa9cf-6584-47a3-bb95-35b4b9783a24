import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { AppAlertService } from 'src/app/app-alert/service/app-alert.service';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { ManageUsersService } from '../manage-users/manage-users.service';
import { AddUserService } from './add-user.service';
import { TokenService } from 'src/app/security/token.service';
import { APP_CONSTANTS, USER_CONSTANTS } from 'src/app/constants/app.constants';
import { ManageLocationsService } from '../manage-locations/manage-locations.service';

@Component({
    selector: 'app-add-user',
    templateUrl: './add-user.component.html',
    styleUrls: ['./add-user.component.scss'],
    standalone: false
})
export class AddUserComponent implements OnInit {

  userForm: FormGroup;
  breadcrumbItems: MenuItem[];
  query:IpagedQuery;
  clientList;
  locationList;
  roleList = [
    { name: USER_CONSTANTS.USER_ROLE_NAMES.admin, code: USER_CONSTANTS.USER_ROLE_UUIDS.admin },
    { name: USER_CONSTANTS.USER_ROLE_NAMES.client, code: USER_CONSTANTS.USER_ROLE_UUIDS.client },
    { name: USER_CONSTANTS.USER_ROLE_NAMES.supervisor, code: USER_CONSTANTS.USER_ROLE_UUIDS.supervisor },
    { name: USER_CONSTANTS.USER_ROLE_NAMES.driver, code: USER_CONSTANTS.USER_ROLE_UUIDS.driver },
    { name: USER_CONSTANTS.USER_ROLE_NAMES.guard, code: USER_CONSTANTS.USER_ROLE_UUIDS.guard },
    { name: USER_CONSTANTS.USER_ROLE_NAMES.yardSpotter, code: USER_CONSTANTS.USER_ROLE_UUIDS.yardSpotter },
    { name: USER_CONSTANTS.USER_ROLE_NAMES.it, code: USER_CONSTANTS.USER_ROLE_UUIDS.it }
  ];
  timeZoneList = USER_CONSTANTS.TIME_ZONES
  userId: any;
  deleteUserDialog: boolean = false;
  passwordDialog: boolean = false;
  subscription = new Subscription();
  password = "Haroon";
  email:String;
  confirmResetPasswordDialog = false;
  resetPasswordModal = false;
  hideClient: boolean;
  userRoles: any;
  loggedInUserId: { field: string; header: string; }[];
  isSpotterRoleToBeCreated: boolean = false;  
  userLocations: any;

  constructor(private fb:FormBuilder,
    private router:Router,
    private manageClientsService:ManageClientsService,
    private loader:AppLoaderService,
    private alertService:AppAlertService,
    private addUserService:AddUserService,
    private activatedRoute:ActivatedRoute,
    private manageUsersService : ManageUsersService,
    private tokenService:TokenService,
    private errorService:ErrorService,
    private manageLocationService:ManageLocationsService) {
    this.userForm = this.fb.group({
      clientIds: [''],
      email: ['',[Validators.required,Validators.email]],
      firstName: ['',Validators.required],
      lastName: ['',Validators.required],
      phone: [''],
      roleIds: ['',Validators.required],
      timeZone: [''],
      roleId:['',Validators.required],
      locationIds: ['']
    });

    this.userForm.get('roleId').valueChanges.subscribe(val => {
      if (val === USER_CONSTANTS.USER_ROLE_UUIDS.admin || val === USER_CONSTANTS.USER_ROLE_UUIDS.it) {
        this.userForm.controls['clientIds'].clearValidators(); 
      } else {
        this.userForm.controls['clientIds'].setValidators([Validators.required]);
      }
      this.userForm.controls['clientIds'].updateValueAndValidity();
      if (val !== USER_CONSTANTS.USER_ROLE_UUIDS.admin && val !== USER_CONSTANTS.USER_ROLE_UUIDS.it) {
        this.userForm.controls['timeZone'].clearValidators(); 
      } else {
        this.userForm.controls['timeZone'].setValidators([Validators.required]);
      }
      this.userForm.controls['timeZone'].updateValueAndValidity();
      if(val === USER_CONSTANTS.USER_ROLE_UUIDS.yardSpotter) {
        this.userForm.controls['locationIds'].setValidators([Validators.required]);
      }
      else
      {
        this.userForm.controls['locationIds'].clearValidators();
      }
    });

    this.activatedRoute.queryParams.subscribe(qparams=>{
      if(qparams["userId"]){
        this.userId = qparams["userId"];
      }
    })
  }

  ngOnInit(): void {
    this.userRoles = this.tokenService.getUserRoles();
    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_SUPERVISOR)){
      this.roleList = this.roleList.filter(role =>
        ['Supervisor', 'Client', 'Driver', 'Guard', 'Yard Spotter'].includes(role.name)
      );
    }
   
    this.loggedInUserId = this.tokenService.getUserId();
    
    console.log(this.roleList);
    this.breadcrumbItems = [];
      this.breadcrumbItems.push({ label: 'Users',routerLink:'../manage-users'});
      this.breadcrumbItems.push({ label: 'Create User'});

    // this.userForm.patchValue({
    //   timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone ? Intl.DateTimeFormat().resolvedOptions().timeZone : 'US/Eastern'
    //     });
    
    this.query = {isActive:true,size:100,page:0}
    this.viewClients(this.query);

    if(this.userId){
      this.getUserById(this.userId);
    }

    if(this.userId == null) {
      if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_DRIVER || role === APP_CONSTANTS.USER_ROLES.ROLE_GUARD || role === APP_CONSTANTS.USER_ROLES.ROLE_SPOTTER || role === APP_CONSTANTS.USER_ROLES.ROLE_CLIENT)){
        this.router.navigate(['main/manage-users'])
      }

    }
  }

  getUserById(userId){
    this.loader.show();
    this.addUserService.getUserById(userId).subscribe(res=>{
      this.userLocations = res;
      this.userForm.patchValue(res);
      this.patchValuesForClients(res);
      this.patchValuesForRoles(res);
      // this.patchValuesForLocations(res);
      this.loader.hide();
    })
  }

  patchValuesForRoles(res: any) {
    let roleIds = []
    if(res.roles){
      res.roles.map(el=>{
        roleIds.push(el.roleId);
      });
      this.userForm.patchValue({
        roleIds,
        roleId:roleIds[0]
      });
      this.toggleClient();
    }else{
      this.userForm.patchValue({
        roleIds : [],
        roleId:''
      })
    }
    
  }

  patchValuesForClients(res: any) {
    let clientIds = []
    if(res.clients){
      res.clients.map(el=>{
        clientIds.push(el.clientId);
      });
      this.userForm.patchValue({
        clientIds
      })
        this.locationList = [];
         for(var i=0;i<res.clients.length;i++) {
           this.getLocations(res.clients[i].clientId);
         }
      //   this.patchValuesForLocations(res);
      
    }else{
      this.userForm.patchValue({
        clientIds:[]
      })
    }
    
  }

  patchValuesForLocations(res:any) {
    let locationIds = []
     if(res.locations) {
        res.locations.map(e1=>{
          locationIds.push(e1.locationId)
        });
        this.userForm.patchValue({
          locationIds: locationIds
        })
     }else{
      this.userForm.patchValue({
        locationIds:[]
      })
     }  
  }

  viewClients(query:IpagedQuery){
    this.loader.show();
    this.manageClientsService.viewClients(query).subscribe(response=>{
      this.clientList = response.list;
      this.loader.hide();
    },(error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }

  onSubmit(){
    this.userForm.patchValue({
      roleIds:[this.userForm.value.roleId]
    })
    if(this.userForm.invalid){
      this.userForm.markAllAsTouched();
    }
    else{
      this.loader.show();
      if(this.userId){
        console.log(this.userForm.value);
        this.addUserService.updateUser(this.userId,this.userForm.value).subscribe(res=>{
          this.loader.hide();
          this.alertService.alertSuccess(['User Updated Successfully']);
          this.router.navigate(['main/manage-users']) 
        },(error) => {
          this.loader.hide();
          this.errorService.handleError(error, true);
      })
      }else{
        console.log(this.userForm.value);
        this.addUserService.saveUser(this.userForm.value).subscribe(res=>{
          this.loader.hide();
          this.alertService.alertSuccess(['User Added Successfully']);
          this.password = res.password;
          this.email = res.email; 
          this.passwordDialog = true;
        },error=>{
          this.errorService.handleError(error,true);
        })
      }
    }
  }


  

  deleteUser() {
    this.deleteUserDialog = true;
  }
 
  confirmresetPassword(userId : any){
    this.loader.show();
    this.manageUsersService.resetpassword(userId).subscribe(res=>{
    this.loader.hide();
    this.alertService.alertSuccess(['password reset successfully']);
    this.password = res.password;
    this.resetPasswordModal = true;
  },(error) => {
    this.loader.hide();
    this.errorService.handleError(error, true);
  })
}


  resetPassword(){
  this.loader.show();
  this.manageUsersService.resetpassword(this.userId).subscribe(res=>{
  this.loader.hide();
  this.alertService.alertSuccess(['password reset successfully']);
  this.password = res.password;
  this.resetPasswordModal = true;
  },(error) => {
    this.loader.hide();
    this.errorService.handleError(error, true);
})
  }

  confirmDelete(){
    this.loader.show();
    this.subscription.add(
    this.addUserService.deleteUser(this.userId).subscribe(res=>{
        this.deleteUserDialog = false;
        this.alertService.alertSuccess(['User Deactivated Successfully']);
        this.router.navigate(['main/manage-users'])
        this.loader.hide();
    },(error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
  })
    )
  }

  routeToManageUsers(){
    this.passwordDialog = false;
    this.router.navigate(['main/manage-users'])
  }

  toggleClient(){

    if(this.userForm.value.roleId === USER_CONSTANTS.USER_ROLE_UUIDS.yardSpotter)
    {
      this.isSpotterRoleToBeCreated = true;
    }   
    else
    {
      this.isSpotterRoleToBeCreated = false;
      this.userForm.patchValue({
        locationIds:null
      })
    }

    if(this.userForm.value.roleId === USER_CONSTANTS.USER_ROLE_UUIDS.admin || this.userForm.value.roleId === USER_CONSTANTS.USER_ROLE_UUIDS.it){
      this.hideClient = true;
      this.userForm.patchValue({
        clientIds:null
      })
    }else{
      this.hideClient = false;
    }
  }

  onClientSelect(event:any) {
    this.locationList = [];
    for(var i=0;i<event.value.length;i++)
    {
      this.getLocations(event.value[i]);
    }
   
  }

  getLocations(clientId:any)
  {
    this.loader.show();
    this.manageLocationService.viewLocations(this.query,clientId).subscribe(response=>{
    this.locationList.splice(this.locationList.length, 0, ...response.list);
    if(this.userLocations != null){
      this.patchValuesForLocations(this.userLocations);
    }
    this.loader.hide();

    },(error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }


  

}
