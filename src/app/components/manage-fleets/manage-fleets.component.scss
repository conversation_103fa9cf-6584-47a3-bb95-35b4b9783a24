:host ::ng-deep {
    .p-toolbar{
    background-color: #f5f5f5 !important;
    border: none !important;
    }

    .p-dialog .p-dialog-content {
        padding: 0rem 1.5rem !important
    }

    .text-danger{
        color: tomato;
    }

    .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
        text-align: center !important;
    }

    .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
        text-align: center !important;
    }

   
}

// :host ::ng-deep .p-dialog .p-dialog-header {
//     background: #EF4444 !important;
//     color: #ffffff;
// }
.unit-number-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1rem;
  }

.multiselect_display_block .p-multiselect-label{
    display: flex !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}
  
  