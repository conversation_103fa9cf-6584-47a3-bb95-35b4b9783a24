<div class="metrics-header">
    <span class="metrics-title">Daily Hourly Average Moves</span>
    <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
        severity="secondary" />
</div>

<!-- Loader -->
<div *ngIf="loading" class="loader-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading data...</p>
</div>

<div class="heatmap-table" *ngIf="!loading">
    <table>
        <thead>
            <tr>
                <th></th>
                <th *ngFor="let hour of hours">{{ hour }}</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let day of days">
                <td>
                    <div class="day-label cell">
                        {{ day.substring(0, 3) }}
                    </div>
                </td>
                <td *ngFor="let hour of hours">
                    <div class="cell" [ngStyle]="{
            'background-color': getCellColor(statsByDay?.[day]?.statsByHour?.[hour]?.avgMoves, day, hour)
         }">
                        {{ getCellValue(statsByDay?.[day]?.statsByHour?.[hour]?.avgMoves, day, hour) }}
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>