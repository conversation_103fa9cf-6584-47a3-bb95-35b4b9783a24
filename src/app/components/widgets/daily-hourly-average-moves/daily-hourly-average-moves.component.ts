import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { DateTime } from 'luxon';

@Component({
    selector: 'app-daily-hourly-average-moves',
    templateUrl: './daily-hourly-average-moves.component.html',
    styleUrls: ['./daily-hourly-average-moves.component.scss'],
    standalone: false
})
export class DailyHourlyAverageMovesComponent implements OnInit {
  @Output() toggleWidgetEvent = new EventEmitter<string>();
  @Input() loading: boolean;
  @Input() statsByDay: any;
  getAccessToken: any = [];
  // today: string = this.getCurrentDay();
  // currentHourIndex: number = new Date().getHours();

  today: string;
  currentHourIndex: number;

  days: string[]; //= ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  hours: string[] = [
    '12 AM', '1 AM', '2 AM', '3 AM', '4 AM', '5 AM', '6 AM',
    '7 AM', '8 AM', '9 AM', '10 AM', '11 AM',
    '12 PM', '1 PM', '2 PM', '3 PM', '4 PM', '5 PM', '6 PM',
    '7 PM', '8 PM', '9 PM', '10 PM', '11 PM'
  ];

  constructor() {

  }

  // ngOnInit(): void {
  //   const weekdayOrder = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  //   const todayIndex = new Date().getDay();
  //   const tomorrowIndex = (todayIndex + 1) % 7;
  //   const tomorrow = weekdayOrder[tomorrowIndex];
  //   const dataDayKeys = Object.keys(this.statsByDay || {});
  //   const isValid = dataDayKeys.length >= 2;
  //   const validDays = isValid ? weekdayOrder.filter(day => dataDayKeys.includes(day)) : [...weekdayOrder];
  //   const startIndex = validDays.indexOf(tomorrow);
  //   const resultKeys = validDays.slice(startIndex).concat(validDays.slice(0, startIndex));
  //   this.days = resultKeys;
  // }

  ngOnInit(): void {
    this.getAccessToken = JSON.parse(localStorage.getItem('access_token'));
    const weekdayOrder = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const nowInTZ = DateTime.now().setZone(this.getAccessToken.timeZone);
    const todayIndex = nowInTZ.weekday % 7;
    const tomorrowIndex = (todayIndex + 1) % 7;

    const today = weekdayOrder[todayIndex];
    const tomorrow = weekdayOrder[tomorrowIndex];

    const currentDay = nowInTZ.toFormat('cccc');
    const currentHour = nowInTZ.toFormat('HH');
    const currentMinute = nowInTZ.toFormat('mm');
    const currentTimeFormatted = nowInTZ.toFormat('hh:mm a');

    // console.log('Day:', currentDay);
    // console.log('Hour:', currentHour);
    // console.log('Minute:', currentMinute);
    // console.log('Formatted Time:', currentTimeFormatted);

    // const dataDayKeys = Object.keys(this.statsByDay || {});
    // const isValid = dataDayKeys.length >= 2;
    // const validDays = isValid ? weekdayOrder.filter(day => dataDayKeys.includes(day)) : [...weekdayOrder];
    const startIndex = weekdayOrder.indexOf(tomorrow);
    const resultKeys = weekdayOrder.slice(startIndex).concat(weekdayOrder.slice(0, startIndex));

    this.days = resultKeys;
    this.today = today;
    this.currentHourIndex = nowInTZ.hour;
  }

  toggleWidget() {
    this.toggleWidgetEvent.emit('daily-hourly-move-average');
  }

  getCellColor(avgMoves: { [key: string]: string }, day: string, hour: string): string {
    const hourIndex = this.hours.indexOf(hour);

    if (!avgMoves) {
      if (day === this.today) {
        if (hourIndex >= this.currentHourIndex) return '#f0f0f0';
      }
      return '#FF0000';
    }

    const moveCount = Object.keys(avgMoves)[0];
    return avgMoves[moveCount];
  }


  getCellValue(avgMoves: { [key: string]: string }, day: string, hour: string): string {
    const hourIndex = this.hours.indexOf(hour);

    // If no data exists for this cell
    if (!avgMoves) {
      // If it's today
      if (day === this.today) {
        // Current hour is still in progress, so don't show 0 yet
        if (hourIndex >= this.currentHourIndex) return '';
      }
      return '0'; // Completed hour with no data
    }

    return Object.keys(avgMoves)[0];
  }



  // getCellColor(avgMoves: { [key: string]: string }): string {
  //   const moveCount = Object.keys(avgMoves)[0];
  //   return avgMoves[moveCount];
  // }

  // getCellValue(avgMoves: { [key: string]: string }): string {
  //   return avgMoves ? Object.keys(avgMoves)[0] : '0';
  // }

  // getCellColor(avgMoves: { [key: string]: string }, day: string, hour: string): string {
  //   const hourIndex = this.hours.indexOf(hour);

  //   if (!avgMoves) {
  //     // No data: decide based on time
  //     if (day === this.today && hourIndex > this.currentHourIndex) return ''; // Future hour
  //     return '#FF0000'; // Past hour with missing data → red
  //   }

  //   const moveCount = Object.keys(avgMoves)[0];
  //   return avgMoves[moveCount]; // Valid color
  // }

  // getCellValue(avgMoves: { [key: string]: string }, day: string, hour: string): string {
  //   const hourIndex = this.hours.indexOf(hour);

  //   if (!avgMoves) {
  //     if (day === this.today && hourIndex > this.currentHourIndex) return ''; // Future hour
  //     return '0'; // Missing data → show 0
  //   }

  //   return Object.keys(avgMoves)[0]; // Valid value
  // }

  // getCurrentDay(): string {
  //   return new Date().toLocaleDateString('en-US', { weekday: 'long' });
  // }
}
