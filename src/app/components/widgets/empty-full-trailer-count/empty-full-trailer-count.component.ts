import {
  Component,
  Input,
  Output,
  EventEmitter,
  SimpleChanges,
  ViewChild,
  ElementRef,
  AfterViewInit,
  OnDestroy,
  OnInit,
} from "@angular/core";
import { ErrorService } from "src/app/error-handling/error.service";
import { BaseChartDirective } from "ng2-charts";
import { ManageLocationsService } from "../../manage-locations/manage-locations.service";
import { IpagedQuery } from "src/app/model/IpagedQuery";
import { ManageSpotsService } from "../../manage-spots/manage-spots.service";

@Component({
    selector: "app-empty-full-trailer-count",
    templateUrl: "./empty-full-trailer-count.component.html",
    styleUrls: ["./empty-full-trailer-count.component.scss"],
    standalone: false
})
export class EmptyFullTrailerCountComponent
  implements OnInit {
  @Input() clientId: any;
  @Output() toggleWidgetEvent = new EventEmitter<string>();
  @ViewChild(BaseChartDirective) chart?: BaseChartDirective;
  @ViewChild("chartContainer", { static: false }) chartContainer?: ElementRef;
  selectedLocationId: string;
  loading: boolean = false;
  filteredLineChartData: any;
  resizeObserver?: ResizeObserver;
  locationList: any[];
  query: IpagedQuery;
  pieChartData: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
      hoverBackgroundColor: string[];
    }[];
  };
  chartOptionsPie: any;
  spotList = [];
  selectLocation: any;
  pieOptions: {
    responsive: boolean;
    plugins: {
      legend: { position: string; labels: { color: string } };
      tooltip: {
        enabled: boolean;
        callbacks: {
          label: (tooltipItem: any) => string;
        };
      };
    };
  };
  totalCOunt: any;
  fullCount: any;
  emptyCount: any;
  constructor(
    private manageLocationsService: ManageLocationsService,
    private errorService: ErrorService,
    private manageSpotsService: ManageSpotsService
  ) { }

  ngOnInit(): void {
    // this.query = { isActive: true, size: 1000, page: 0 };
    // if (this.clientId) {
    //   this.getClientLocations(this.query, this.clientId);
    // }
    // setTimeout(() => {
    //   this.forceChartTooltipRefresh();
    // }, 500);
  }

  toggleWidget() {
    this.toggleWidgetEvent.emit("showEmptyFullTrailerCount");
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes.clientId && changes.clientId.currentValue) {
      this.getClientLocations(this.query, this.clientId);
    }
    setTimeout(() => {
      this.forceChartTooltipRefresh();
    }, 500);
  }

  getClientLocations(query, clientId) {
    if (!query) {
      query = { isActive: true, size: 1000, page: 0 };
    }
    this.manageLocationsService.viewLocations(query, clientId).subscribe(
      (response) => {
        this.locationList = response.list;
        if (this.locationList.length > 0) {
          this.selectLocation = this.locationList[0];
          this.getClientSpots(this.query, this.clientId, this.selectLocation.locationId);
        }
      },
      (error) => {
        this.errorService.handleError(error, true);
      }
    );
  }

  onLocationChange() {
    if (this.selectLocation) {
      this.getClientSpots(this.query, this.clientId, this.selectLocation.locationId
      );
    }
  }

  getClientSpots(query: any, clientId: any, locationId: any) {
    this.loading = true;
    this.manageSpotsService.viewFleetStatusCount(clientId, locationId).subscribe((data) => {
      this.spotList = data;
      this.calculateFleetCounts();
    },
      (error) => {
        this.errorService.handleError(error, true);
      }
    );
  }

  calculateFleetCounts() {
    let emptyCount = 0;
    let fullCount = 0;

    this.spotList.forEach((entry) => {
      if (entry.fleetStatus === "EMPTY") {
        emptyCount = entry.count;
      } else if (entry.fleetStatus === "FULL") {
        fullCount = entry.count;
      }
    });
    const totalCount = emptyCount + fullCount;
    this.totalCOunt = totalCount;
    this.emptyCount = emptyCount;
    this.fullCount = fullCount;
    this.chartOptionsPie = {
      responsive: true,
      maintainAspectRatio: false,
    };
    this.pieChartData = {
      labels: ["Empty", "Loaded"],
      datasets: [
        {
          data: [emptyCount, fullCount],
          backgroundColor: ["#ffcc66", "#99ccff"],
          hoverBackgroundColor: ["#ffcc66", "#99ccff"],
        },
      ],
    };

    this.pieOptions = {
      responsive: true,
      plugins: {
        legend: {
          position: "right",
          labels: {
            color: "#A0A7B5",
          },
        },
        tooltip: {
          enabled: true,
          callbacks: {
            label: (tooltipItem) => {
              const index = tooltipItem.dataIndex;
              const label = this.pieChartData.labels[index];
              const value = tooltipItem.dataset.data[index];
              return `${label}: ${value}`;
            },
          },
        },
      },
    };
    setTimeout(() => {
      if (this.chart) {
        this.chart.chart?.destroy();
        this.chart.update();
      }
    }, 300);
    this.loading = false;
  }

  forceChartTooltipRefresh() {
    setTimeout(() => {
      if (this.chart?.chart) {
        this.chart.chart.update();
      }
    }, 300);
  }
}
