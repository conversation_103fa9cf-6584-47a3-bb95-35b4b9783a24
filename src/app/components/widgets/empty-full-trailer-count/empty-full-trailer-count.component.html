<div class="metrics-header">
  <span class="metrics-title">Trailer Status</span>
  <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
    severity="secondary" />
</div>

<!-- Dropdown Container -->
<div class="dropdown-container">
  <p-select [options]="locationList" [(ngModel)]="selectLocation" (onChange)="onLocationChange()"
    optionLabel="locationName" size="small" />
</div>

<!-- Loader -->
<div *ngIf="loading" class="loader-container">
  <mat-spinner diameter="40"></mat-spinner>
  <p>Loading data...</p>
</div>

<!-- Chart Container -->
<div class="chart-container" *ngIf="!loading && pieChartData">
  <p-chart type="pie" [data]="pieChartData" [options]="pieOptions"></p-chart>
  <h6 class="total-count">Total Trailers: {{ totalCOunt }}</h6>
</div>