import { Component, Input, Output, EventEmitter, NgZone, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { BaseChartDirective } from 'ng2-charts';

@Component({
    selector: 'app-average-dock-turnaround-time',
    templateUrl: './average-dock-turnaround-time.component.html',
    styleUrls: ['./average-dock-turnaround-time.component.scss'],
    standalone: false
})
export class AverageDockTurnaroundTimeComponent {
  chartOptionsBar = {
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        left: 0,
        right: 10,
        top: 20,
        bottom: 10
      }
    },
    hover: {
      mode: null
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          font: { size: 13 },
          color: '#000',
          generateLabels: (chart) => {
            return chart.data.datasets.map((dataset) => ({
              text: dataset.label,
              fillStyle: dataset.backgroundColor,
              strokeStyle: dataset.backgroundColor,
              lineWidth: 2
            }));
          }
        }
      },
      tooltip: {
        enabled: true,
        callbacks: {
          label: function (tooltipItem: any) {
            const value = tooltipItem.raw;
            const hours = Math.floor(value);
            const minutes = Math.round((value - hours) * 60);
            const hoursStr = hours.toString().padStart(2, '0');
            const minutesStr = minutes.toString().padStart(2, '0');
            return `Dock Turnaround Time: ${hoursStr}:${minutesStr} hrs`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          align: 'start',
          color: '#000',
          font: { size: 12 },
          maxRotation: 30,
          minRotation: 30
        },
        title: {
          display: true,
          text: 'Location',
          font: { size: 13 }
        }
      },
      // x: {
      //   offset: false,
      //   grid: { display: false },
      //   ticks: {
      //     align: 'start',  // 👈 force left alignment
      //     color: '#000',
      //     font: { size: 12 },
      //     maxRotation: 30,
      //     minRotation: 30
      //   },
      //   title: {
      //     display: true,
      //     text: 'Location',
      //     font: { size: 13 }
      //   }
      // },
      y: {
        beginAtZero: true,
        min: 0,
        suggestedMax: 20,
        grid: {
          display: true,
          drawBorder: false
        },
        ticks: {
          stepSize: 2,
          color: '#000',
          font: { size: 12 },
          padding: 4,
          callback: function (value: any) {
            const hours = Math.floor(value);
            const minutes = Math.round((value - hours) * 60);
            const hoursStr = hours.toString().padStart(2, '0');
            const minutesStr = minutes.toString().padStart(2, '0');

            if (value === 0) {
              return '00';
            }
            return `${hoursStr}:${minutesStr}`;
          }
        },
        title: {
          display: true,
          text: 'Dock Turnaround Time - Hours',
          font: { size: 13 }
        }
      }
    },
    elements: {
      bar: {
        borderWidth: 0
      }
    }
  };




  @Input() barChartData: any;
  // @Input() chartOptionsBar: any;
  @Input() loading: boolean = true;

  @Output() toggleWidgetEvent = new EventEmitter<string>();


  toggleWidget() {
    this.toggleWidgetEvent.emit('average-dock-turnaround-time');
  }

}
