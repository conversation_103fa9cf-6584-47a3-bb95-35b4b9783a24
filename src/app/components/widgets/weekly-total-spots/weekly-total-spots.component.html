<div class="metrics-header">
    <h5 class="metrics-title"></h5>
    <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
        severity="secondary" />
</div>
<div *ngIf="loading" class="loader-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading data...</p>
</div>
<div class="averageCard" *ngIf="!loading && weeklyTotalSpots">
    <div class="content">
        <div class="title"><span>Previous Week Total Spots</span></div>
        <div class="subtitle">{{ weeklyTotalSpots.weekStart }} - {{ weeklyTotalSpots.weekEnd }}</div>
        <div class="divider"></div>
        <div class="value">{{ weeklyTotalSpots.total }}</div>
        <div class="label">Total</div>
    </div>
</div>