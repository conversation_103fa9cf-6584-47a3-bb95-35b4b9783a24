import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-weekly-total-spots',
  templateUrl: './weekly-total-spots.component.html',
  styleUrls: ['./weekly-total-spots.component.scss'],
  standalone: false
})
export class WeeklyTotalSpotsComponent implements OnInit {
  @Input() weeklyTotalSpots: any;
  @Input() loading: boolean;
  @Output() toggleWidgetEvent = new EventEmitter<string>();
  constructor() { }

  ngOnInit(): void {
  }

  toggleWidget() {
    this.toggleWidgetEvent.emit('weekly-total-spots'); // Emit event to parent
  }

}
