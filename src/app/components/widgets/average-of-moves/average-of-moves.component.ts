import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'app-average-of-moves',
    templateUrl: './average-of-moves.component.html',
    styleUrls: ['./average-of-moves.component.scss'],
    standalone: false
})
export class AverageOfMovesComponent implements OnInit {
  @Input() averageMoves: any;
  @Input() loading: boolean;
  @Output() toggleWidgetEvent = new EventEmitter<string>();
  constructor() { }

  ngOnInit(): void {
  }


  toggleWidget() {
    this.toggleWidgetEvent.emit('averageOfMoves'); // Emit event to parent
  }

}
