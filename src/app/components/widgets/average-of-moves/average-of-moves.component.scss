.metrics-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 10px;
}

.metrics-title {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    color: #333;
    flex: 1;
}


:host {
    // display: block;
    // width: 100%;
    // height: 100%;
    overflow-y: scroll; // Prevent scrolling from GridStack
}

.averageCard {
    position: relative;
    width: -webkit-fill-available;
    margin: 12px 15px 12px 15px;
    height: auto;
    background: #ffffff;
    border: 2px solid #000;
    border-radius: 10px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding: 0px;
    z-index: 1;
}

.loader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.close-btn {
    position: absolute;
    right: 10px;
    background: transparent;
    border: none;
}

.close-btn {
    position: absolute;
    right: 3px;
    cursor: pointer;
    z-index: 10;
    background: transparent;
    border: none;
}

.close-btn mat-icon {
    font-size: 20px;
    /* Adjust size */
    color: black;
    /* Set color */
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #333;
    /* Slightly darker color on hover */
}

.content {
    text-align: center;
    padding: 10px;
    width: 100%;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
}

.subtitle {
    font-size: 14px;
    color: #777;
    margin-bottom: 10px;
}

.divider {
    height: 2px;
    background-color: #333;
    margin: 0px auto 10px; //6px auto 12px;
    width: 90%;
}

.value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.label {
    font-size: 1rem;
    margin-top: 6px;
}