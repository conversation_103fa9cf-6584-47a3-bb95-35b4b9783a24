<div class="metrics-header">
    <h5 class="metrics-title"></h5>
    <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
        severity="secondary" />
</div>
<div *ngIf="loading" class="loader-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading data...</p>
</div>
<div class="averageCard" *ngIf="!loading && averageMoves">
    <div class="content">
        <div class="title"><span>{{averageMoves.key}}</span> - Average # of Moves</div>
        <div class="subtitle">Over the last 3 weeks</div>
        <div class="divider"></div>
        <div class="value">{{ averageMoves.value }}</div>
        <div class="label">Moves</div>
    </div>
</div>