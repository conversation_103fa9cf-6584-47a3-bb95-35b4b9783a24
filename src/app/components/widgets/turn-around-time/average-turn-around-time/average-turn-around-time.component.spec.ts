// import { ComponentFixture, TestBed } from '@angular/core/testing';

// import { AverageTurnAroundTimeComponent } from './average-turn-around-time.component';

// describe('AverageTurnAroundTimeComponent', () => {
//   let component: AverageTurnAroundTimeComponent;
//   let fixture: ComponentFixture<AverageTurnAroundTimeComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [ AverageTurnAroundTimeComponent ]
//     })
//     .compileComponents();

//     fixture = TestBed.createComponent(AverageTurnAroundTimeComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
