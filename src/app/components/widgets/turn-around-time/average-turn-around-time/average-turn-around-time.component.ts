import {
  Component, Input, Output, EventEmitter, SimpleChanges, ViewChild, ElementRef, AfterViewInit, OnDestroy,
  NgZone,
  OnInit
} from '@angular/core';
import { DatePipe } from '@angular/common';
import { BaseChartDirective } from 'ng2-charts';

@Component({
    selector: 'app-average-turn-around-time',
    templateUrl: './average-turn-around-time.component.html',
    styleUrls: ['./average-turn-around-time.component.scss'],
    standalone: false
})
export class AverageTurnAroundTimeComponent {

  // chartOptionsLine = {
  //   responsive: true,
  //   plugins: {
  //     legend: {
  //       position: 'top',
  //       labels: {
  //         color: "#000",
  //         font: {
  //           size: 14
  //         }
  //       }
  //     },
  //     tooltip: {
  //       callbacks: {
  //         label: function (tooltipItem: any) {
  //           const value = tooltipItem.raw;
  //           if (typeof value !== 'number' || isNaN(value)) {
  //             return tooltipItem.dataset.label + ': N/A';
  //           }

  //           const minutes = Math.floor(value / 60);
  //           const seconds = Math.round(value % 60);
  //           return `${tooltipItem.dataset.label}: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  //         }
  //       }
  //     }
  //   },
  //   interaction: {
  //     mode: 'index',
  //     intersect: false,
  //   },
  //   scales: {
  //     x: {
  //       title: {
  //         display: true,
  //         text: 'Date',
  //         font: { size: 14 }
  //       },
  //       ticks: {
  //         font: { size: 12 }
  //       }
  //     },
  //     y: {
  //       beginAtZero: false,
  //       ticks: {
  //         callback: function (value: number) {
  //           const minutes = Math.floor(value / 60);
  //           const seconds = Math.round(value % 60);
  //           return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  //         },
  //         font: { size: 12 }
  //       },
  //       title: {
  //         display: true,
  //         text: 'Turnaround Time (mm:ss)',
  //         font: { size: 14 }
  //       }
  //     }
  //   },
  //   elements: {
  //     line: { borderWidth: 2 },
  //     point: { radius: 4 },
  //   }
  // };

  chartOptionsLine = {
    responsive: true,
    maintainAspectRatio: false, // Allows better height control
    layout: {
      padding: {
        left: 0,
        right: 10,
        top: 20,
        bottom: 10
      }
    },
    hover: {
      mode: null
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          font: {
            size: 13
          },
          color: '#000',
          generateLabels: (chart) => {
            return chart.data.datasets.map((dataset) => ({
              text: dataset.label,
              fillStyle: dataset.borderColor,
              strokeStyle: dataset.borderColor,
              lineWidth: 2
            }));
          }
        }
      },
      tooltip: {
        enabled: true,
        callbacks: {
          label: function (tooltipItem: any) {
            return `Value: ${tooltipItem.raw} minutes`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#000',
          font: {
            size: 12
          },
          maxRotation: 30,
          minRotation: 30
        },
        title: {
          display: true,
          text: 'Driver/Spotter',
          font: {
            size: 13
          }
        }
      },
      y: {
        beginAtZero: true,
        min: 0,
        suggestedMax: 30,
        grid: {
          display: true,
          drawBorder: false
        },
        ticks: {
          stepSize: 3,
          color: '#000',
          font: {
            size: 12
          },
          padding: 4
        },
        title: {
          display: true,
          text: 'Average Move Time (Minutes)',
          font: {
            size: 13
          }
        }
      }
    },
    elements: {
      line: {
        borderWidth: 2,
        tension: 0.4 // Smooth lines
      },
      point: {
        radius: 3,
        backgroundColor: 'pink'
      }
    }
  };


  @Input() lineChartData: any;
  @Input() loading: boolean = true;
  @Input() clientId: any;
  @Output() toggleWidgetEvent = new EventEmitter<string>();
  resizeObserver?: ResizeObserver;
  toggleWidget() {
    this.toggleWidgetEvent.emit('showAverageTurnaroundTime');

  }

  ngOnInit(): void {
  }

}
