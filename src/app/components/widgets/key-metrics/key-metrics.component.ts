import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';

@Component({
    selector: 'app-key-metrics',
    templateUrl: './key-metrics.component.html',
    styleUrls: ['./key-metrics.component.scss'] // Using the dashboard styles
    ,
    standalone: false
})
export class KeyMetricsComponent {
  @Input() generalStatistics: any;
  @Input() messageStatistics: any;
  @Input() jobStatistics: any;
  @Input() isLoggedUserClient: boolean;

  @Output() toggleWidgetEvent = new EventEmitter<string>(); // Event to notify parent
  
    toggleWidget() {
      this.toggleWidgetEvent.emit('showKeyMetrics'); // Emit event to parent
    }

    @Output() routeToJobsEvent = new EventEmitter<string>();

    routeToJobs(filter: string) {
      this.routeToJobsEvent.emit(filter); // Emit event to parent
    }

    constructor(private router: Router) {}

  routeTo(section: string) {
    switch (section) {
      case 'Users':
        this.router.navigate(['/main/manage-users']);
        break;
      case 'Clients':
        this.router.navigate(['/main/manage-clients']);
        break;
      case 'Unread Messages':
        this.router.navigate(['/main/manage-messages']);
        break;
      case 'In-Transit Spots':
        this.routeToJobs('IN_TRANSIT');
        break;
      default:
        console.warn('Unknown section:', section);
        break;
    }
  }
}
