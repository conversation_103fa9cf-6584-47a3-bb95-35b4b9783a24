<div class="row">
    <div class="col-12">
        <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
        severity="secondary" />
    </div>
    <div class="col-12 text-align-center">
        <span class="metrics-title">Key Metrics
        </span>
    </div>
    <div class="col-12">
        <div class="row dynamicCardRow">
            <div class="col-12">
                <div class="metrics-row">
                    <div class="metrics-column">
                        <div class="metric-card" *ngIf="!isLoggedUserClient">
                            <div class="metric-info">
                                <div class="metric" (click)="routeTo('Users')">
                                    <span class="metric-title text-orange">Users</span>
                                    <span class="metric-value">{{ generalStatistics.totalUsers }}</span>
                                </div>
                            </div>
                            <div class="metric-icon bg-orange-dark">
                                <img alt="Users Icon" src="assets/dashboard/users.svg">
                            </div>
                        </div>
                    </div>

                    <div class="metrics-column">
                        <div class="metric-card" *ngIf="!isLoggedUserClient">
                            <div class="metric-info">
                                <div class="metric" (click)="routeTo('Clients')">
                                    <span class="metric-title text-purple">Clients</span>
                                    <span class="metric-value">{{ generalStatistics.totalClients }}</span>
                                </div>
                            </div>
                            <div class="metric-icon bg-purple">
                                <img alt="Clients Icon" src="assets/dashboard/clients.svg">
                            </div>
                        </div>
                    </div>

                    <div class="metrics-column">
                        <div class="metric-card">
                            <div class="metric-info">
                                <div class="metric" (click)="routeTo('Unread Messages')">
                                    <span class="metric-title text-cyan">Unread Messages</span>
                                    <span class="metric-value">{{ messageStatistics.newCount }}</span>
                                </div>
                            </div>
                            <div class="metric-icon bg-cyan">
                                <i class="pi pi-envelope p-white" style="font-size: 1.5rem"></i>
                            </div>
                        </div>
                    </div>

                    <div class="metrics-column">
                        <div class="metric-card">
                            <div class="metric-info">
                                <div class="metric" (click)="routeTo('In-Transit Spots')">
                                    <span class="metric-title text-orange">In-Transit Spots</span>
                                    <span class="metric-value">{{ jobStatistics.activeJobs }}</span>
                                </div>
                            </div>
                            <div class="metric-icon bg-orange">
                                <img alt="Active Jobs Icon" src="assets/dashboard/active-jobs.svg">
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <!-- <div class="row">
            <div class="col-4">
                <div class="metric-card" *ngIf="!isLoggedUserClient">
                    <div class="metric-info">
                        <div class="metric" (click)="routeTo('Users')">
                            <span class="metric-title text-orange">Users</span>
                            <span class="metric-value">{{ generalStatistics.totalUsers }}</span>
                        </div>
                    </div>
                    <div class="metric-icon bg-orange-dark">
                        <img alt="" src="assets/dashboard/users.svg">
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="metric-card" *ngIf="!isLoggedUserClient">
                    <div class="metric-info">
                        <div class="metric" (click)="routeTo('Clients')">
                            <span class="metric-title text-purple">Clients</span>
                            <span class="metric-value">{{ generalStatistics.totalClients }}</span>
                        </div>
                    </div>
                    <div class="metric-icon bg-purple">
                        <img alt="" src="assets/dashboard/clients.svg">
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="metric-card">
                    <div class="metric-info">
                        <div class="metric" (click)="routeTo('Unread Messages')">
                            <span class="metric-title text-cyan">Unread Messages</span>
                            <span class="metric-value">{{ messageStatistics.newCount }}</span>
                        </div>
                    </div>
                    <div class="metric-icon bg-cyan">
                        <i class="pi pi-envelope p-white" style="font-size: 1.5rem"></i>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="metric-card">
                    <div class="metric-info">
                        <div class="metric" (click)="routeTo('In-Transit Spots')">
                            <span class="metric-title text-orange">In-Transit Spots</span>
                            <span class="metric-value">{{ jobStatistics.activeJobs }}</span>
                        </div>
                    </div>
                    <div class="metric-icon bg-orange">
                        <img alt="" src="assets/dashboard/active-jobs.svg">
                    </div>
                </div>
            </div>
        </div> -->
    </div>
</div>




<!-- <div class="metrics-grid">
    <div class="metric-card" *ngIf="!isLoggedUserClient">
        <div class="metric-info">
            <div class="metric" (click)="routeTo('Users')">
                <span class="metric-title text-orange">Users</span>
                <span class="metric-value">{{ generalStatistics.totalUsers }}</span>
            </div>
        </div>
        <div class="metric-icon bg-orange-dark">
            <img alt="" src="assets/dashboard/users.svg">
        </div>
    </div>

    <div class="metric-card" *ngIf="!isLoggedUserClient">
        <div class="metric-info">
            <div class="metric" (click)="routeTo('Clients')">
                <span class="metric-title text-purple">Clients</span>
                <span class="metric-value">{{ generalStatistics.totalClients }}</span>
            </div>
        </div>
        <div class="metric-icon bg-purple">
            <img alt="" src="assets/dashboard/clients.svg">
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-info">
            <div class="metric" (click)="routeTo('Unread Messages')">
                <span class="metric-title text-cyan">Unread Messages</span>
                <span class="metric-value">{{ messageStatistics.newCount }}</span>
            </div>
        </div>
        <div class="metric-icon bg-cyan">
            <i class="pi pi-envelope p-white" style="font-size: 1.5rem"></i>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-info">
            <div class="metric" (click)="routeTo('In-Transit Spots')">
                <span class="metric-title text-orange">In-Transit Spots</span>
                <span class="metric-value">{{ jobStatistics.activeJobs }}</span>
            </div>
        </div>
        <div class="metric-icon bg-orange">
            <img alt="" src="assets/dashboard/active-jobs.svg">
        </div>
    </div>
</div> -->