import { Component, Input, Output, EventEmitter, NgZone, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { BaseChartDirective } from 'ng2-charts';

@Component({
    selector: 'app-average-dock-dwell-time',
    templateUrl: './average-dock-dwell-time.component.html',
    styleUrls: ['./average-dock-dwell-time.component.scss'],
    standalone: false
})

export class AverageDockDwellTimeComponent {
  chartOptionsBar = {
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        left: 0,
        right: 10,
        top: 20,
        bottom: 10
      }
    },
    hover: {
      mode: null
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          font: { size: 13 },
          color: '#000',
          generateLabels: (chart) => {
            return chart.data.datasets.map((dataset) => ({
              text: dataset.label,
              fillStyle: dataset.backgroundColor,
              strokeStyle: dataset.backgroundColor,
              lineWidth: 2
            }));
          }
        }
      },
      tooltip: {
        enabled: true,
        callbacks: {
          label: function (tooltipItem: any) {
            const value = tooltipItem.raw;
            const totalMinutes = Math.round(value * 60);
            const days = Math.floor(totalMinutes / 1440);
            const hours = Math.floor((totalMinutes % 1440) / 60);
            const minutes = totalMinutes % 60;

            const daysStr = days.toString().padStart(2, '0');
            const hoursStr = hours.toString().padStart(2, '0');
            const minutesStr = minutes.toString().padStart(2, '0');
            return [
              `${tooltipItem.dataset.label}`,
              `${daysStr}:${hoursStr}:${minutesStr}`,
              `(DD:HH:MM)`
            ];
            // return `${tooltipItem.dataset.label}: ${daysStr}:${hoursStr}:${minutesStr} (DD:HH:MM)`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#000',
          font: { size: 12 },
          maxRotation: 30,
          minRotation: 30
        },
        title: {
          display: true,
          text: 'Location',
          font: { size: 13 }
        }
      },
      y: {
        beginAtZero: true,
        min: 0,
        max: 120,
        ticks: {
          stepSize: 10,
          callback: function (value: number) {
            const hours = Math.floor(value);
            const minutes = Math.round((value - hours) * 60);
            const hoursStr = hours.toString().padStart(2, '0');
            const minutesStr = minutes.toString().padStart(2, '0');
            return `${hoursStr}:${minutesStr}`;
          },
          color: '#000',
          font: { size: 12 },
          padding: 4,
        },
        title: {
          display: true,
          text: 'Dock Dwell Time - Hours',
          font: { size: 13 },
        },
        grid: {
          display: true,
          drawBorder: false,
        },
      }
    },
    elements: {
      bar: {
        borderWidth: 0
      }
    }
  };




  @Input() barChartData: any;
  // @Input() chartOptionsBar: any;
  @Input() loading: boolean = true;

  @Output() toggleWidgetEvent = new EventEmitter<string>();


  toggleWidget() {
    this.toggleWidgetEvent.emit('average-dock-dwell-time');
  }
}
