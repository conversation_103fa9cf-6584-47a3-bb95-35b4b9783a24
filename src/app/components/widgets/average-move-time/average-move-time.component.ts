import {
  Component, Input, Output, EventEmitter, SimpleChanges, ViewChild, ElementRef, AfterViewInit, OnDestroy,
  NgZone
} from '@angular/core';
import { DatePipe } from '@angular/common';
import { BaseChartDirective } from 'ng2-charts';

@Component({
    selector: 'app-average-moves',
    templateUrl: './average-move-time.component.html',
    styleUrls: ['./average-move-time.component.scss'],
    providers: [DatePipe],
    standalone: false
})
export class AverageMoveTimeComponent {
  chartOptionsLine = {
    responsive: true,
    maintainAspectRatio: false, // Allows better height control
    layout: {
      padding: {
        left: 0,
        right: 10,
        top: 20,
        bottom: 10
      }
    },
    hover: {
      mode: null
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          font: {
            size: 13
          },
          color: '#000',
          generateLabels: (chart) => {
            return chart.data.datasets.map((dataset) => ({
              text: dataset.label,
              fillStyle: dataset.borderColor,
              strokeStyle: dataset.borderColor,
              lineWidth: 2
            }));
          }
        }
      },
      // tooltip: {
      //   enabled: true,
      //   backgroundColor: '#e6f4ea', // Tooltip box background color
      //   titleColor: '#000',
      //   bodyColor: '#000',
      //   borderColor: '#00aa55',
      //   borderWidth: 1,
      //   usePointStyle: true, // ✅ Use point style for legend box in tooltip
      //   callbacks: {
      //     labelPointStyle: () => {
      //       return {
      //         pointStyle: 'circle', // You can use 'rect', 'triangle', etc.
      //         rotation: 0
      //       };
      //     },
      //     label: function (tooltipItem: any) {
      //       return `Value: ${tooltipItem.raw} minutes`;
      //     }
      //   }
      // }

      tooltip: {
        enabled: true,
        callbacks: {
          label: function (tooltipItem: any) {
            return `Value: ${tooltipItem.raw} minutes`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#000',
          font: {
            size: 12
          },
          maxRotation: 30,
          minRotation: 30
        },
        title: {
          display: true,
          text: 'Driver/Spotter',
          font: {
            size: 13
          }
        }
      },
      y: {
        beginAtZero: true,
        min: 0,
        suggestedMax: 30,
        grid: {
          display: true,
          drawBorder: false
        },
        ticks: {
          stepSize: 3,
          color: '#000',
          font: {
            size: 12
          },
          padding: 4
        },
        title: {
          display: true,
          text: 'Average Move Time (Minutes)',
          font: {
            size: 13
          }
        }
      }
    },
    elements: {
      line: {
        borderWidth: 2,
        tension: 0.4 // Smooth lines
      },
      point: {
        radius: 3,
        backgroundColor: 'pink'
      }
    }
  };

  @Input() lineChartData: any;
  @Input() loading: boolean = true;
  @Input() clientId: any;
  @Output() toggleWidgetEvent = new EventEmitter<string>();
  resizeObserver?: ResizeObserver;
  toggleWidget() {
    this.toggleWidgetEvent.emit('showAverageMoveTime');

  }

  ngOnInit(): void {
  }
}
