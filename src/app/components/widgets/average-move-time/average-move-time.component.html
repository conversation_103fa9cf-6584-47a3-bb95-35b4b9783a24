

<div class="metrics-header">
  <span class="metrics-title">Average Move Time - Driver/Spotter</span>
  <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
        severity="secondary" />
</div>

<!-- Loader -->
<div *ngIf="loading" class="loader-container">
  <mat-spinner diameter="40"></mat-spinner>
  <p>Loading data...</p>
</div>

<!-- Chart Container -->
<div class="chart-container" *ngIf="!loading && lineChartData">
  <canvas [datasets]="lineChartData.datasets" [labels]="lineChartData.labels" [options]="chartOptionsLine" baseChart
    type="line">
  </canvas>
</div>