// import { ComponentFixture, TestBed } from '@angular/core/testing';

// import { AverageMoveTimeComponent } from './average-move-time.component';

// describe('AverageMoveTimeComponent', () => {
//   let component: AverageMoveTimeComponent;
//   let fixture: ComponentFixture<AverageMoveTimeComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [ AverageMoveTimeComponent ]
//     })
//     .compileComponents();

//     fixture = TestBed.createComponent(AverageMoveTimeComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
