import { Component, Input, Output, EventEmitter, OnInit, HostListener, SimpleChanges, } from '@angular/core';
import { ManageLocationsService } from "../../manage-locations/manage-locations.service";
import { ManageSpotsService } from "../../manage-spots/manage-spots.service";
import { ErrorService } from "src/app/error-handling/error.service";
import { IpagedQuery } from "src/app/model/IpagedQuery";
import { DashboardServiceNew } from "../../dashboard-new/dashboard.service";
import { catchError } from 'rxjs';

@Component({
  selector: 'app-dock-usage',
  templateUrl: './dock-usage.component.html',
  styleUrls: ['./dock-usage.component.scss'],
  standalone: false
})
export class DockUsageComponent implements OnInit {
  @Output() toggleWidgetEvent = new EventEmitter<string>();
  locationList: any[];
  query: IpagedQuery;
  selectLocation: any;
  spotList: any[];
  selectSpots: any;
  @Input() clientId: any;
  loading: boolean = true;
  isRefresh: boolean = false;

  spotCounts: any;

  constructor(
    private manageLocationsService: ManageLocationsService,
    private errorService: ErrorService,
    private manageSpotsService: ManageSpotsService,
    private dashboardServiceNew: DashboardServiceNew,
  ) { }

  ngOnInit(): void {
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.clientId && changes.clientId.currentValue) {
      this.getClientLocations(this.query, this.clientId);
    }
  }

  refresh() {
    this.isRefresh = true;
    this.loading = true;
    this.getDockUsageSpots();
  }

  getClientLocations(query, clientId) {
    if (!query) {
      query = { isActive: true, size: 1000, page: 0 };
    }
    this.loading = true;
    this.manageLocationsService.viewLocations(query, clientId).subscribe(
      (response) => {
        this.locationList = response.list;
        if (this.locationList.length > 0) {
          this.selectLocation = this.locationList[0];
          this.getDockUsageSpots();
        }
      },
      (error) => {
        this.errorService.handleError(error, true);
      }
    );
  }

  onLocationChange(event: any) {
    this.selectLocation = event.value;
    console.log('this.selectLocation',this.selectLocation)
    this.getDockUsageSpots();
  }


  toggleWidget() {
    this.toggleWidgetEvent.emit("dock-usage");
  }

  getDockUsageSpots() {
    this.dashboardServiceNew.viewDockUsageSpots(this.selectLocation.locationId, this.isRefresh).subscribe((data) => {
      this.spotList = [];
      this.selectSpots = ''
      this.spotList = data;
      if (this.spotList.length > 0) {
        this.selectSpots = this.spotList[0];
        this.getSpotsCounts();
      }
    },
      (error) => {
        this.errorService.handleError(error, true);
      }
    );
  }

  onSpotsChange(event: any) {
    this.getSpotsCounts();
  }

  getSpotsCounts() {
    this.dashboardServiceNew.viewSpotsCount(this.clientId, this.selectSpots.spotId, this.isRefresh).subscribe((data) => {
      if (data) {
        this.spotCounts = data.percentage;
        this.loading = false;
        this.isRefresh = false;
      }
    },
      (error) => {
        this.errorService.handleError(error, true);
      }
    );
  }
}
