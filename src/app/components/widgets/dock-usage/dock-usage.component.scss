.metrics-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    min-height: 38px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
}

.metrics-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    color: #333;
    font-family: inter, system-ui, Segoe UI, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol;
}

.close-btn {
    position: absolute;
    right: 3px;
    cursor: pointer;
    z-index: 9999;
    background: transparent;
    border: none;
}

.dropdown-container {
    position: relative;
    width: -webkit-fill-available;
    width: 100%;
    display: flex;
    justify-content: center;
    z-index: 9999;
}

.margin-left-5 {
    margin-left: 5px;
}


:host {
    // display: block;
    // width: 100%;
    // height: 100%;
    overflow-y: scroll; // Prevent scrolling from GridStack
}

.dockUsageCard {
    position: relative;
    width: -webkit-fill-available;
    margin: 7px 15px 12px 15px;
    height: auto;
    background: #ffffff;
    border: 2px solid #000;
    border-radius: 10px;
    box-sizing: border-box;
    //display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding: 0px;
    z-index: 1;
}

.loader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.content {
    text-align: center;
    padding: 10px;
    width: 100%;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
}

.subtitle {
    font-size: 14px;
    color: #777;
    margin-bottom: 10px;
}

.divider {
    height: 2px;
    background-color: #333;
    margin: 0px auto 10px; //6px auto 12px;
    width: 90%;
}

.value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
    margin-top: 15px;
}

.label {
    font-size: 1rem;
    margin-top: 6px;
    height: 10px;
}