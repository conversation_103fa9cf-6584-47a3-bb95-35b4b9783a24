<div class="metrics-header">
    <div class="dropdown-container" *ngIf="!loading">
    <p-select [options]="locationList" [(ngModel)]="selectLocation" (onChange)="onLocationChange($event)"
        optionLabel="locationName" size="small">
    </p-select>

    <p-select class="margin-left-5" [options]="spotList" [(ngModel)]="selectSpots" (onChange)="onSpotsChange($event)" optionLabel="spotName"
        placeholder="select" size="small" scrollHeight="150px">
    </p-select>
</div>
    <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
        severity="secondary" />
</div>



<div *ngIf="loading" class="loader-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading data...</p>
</div>
<div class="dockUsageCard" *ngIf="!loading">
    <div class="content">
        <div class="title">Dock Usage</div>
        <div class="subtitle">Last 7 Days</div>
        <div class="divider"></div>
        <div class="value">{{ spotCounts ? spotCounts + '%' : '0%' }}</div>
        <div class="label"></div>
    </div>
</div>