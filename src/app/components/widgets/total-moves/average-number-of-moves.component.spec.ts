// import { ComponentFixture, TestBed } from '@angular/core/testing';

// import { AverageNumberOfMovesComponent } from './average-number-of-moves.component';

// describe('AverageNumberOfMovesComponent', () => {
//   let component: AverageNumberOfMovesComponent;
//   let fixture: ComponentFixture<AverageNumberOfMovesComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [ AverageNumberOfMovesComponent ]
//     })
//     .compileComponents();

//     fixture = TestBed.createComponent(AverageNumberOfMovesComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
