import { Component, Input, Output, EventEmitter, NgZone, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { BaseChartDirective } from 'ng2-charts';

@Component({
    selector: 'app-average-number-of-moves',
    templateUrl: './average-number-of-moves.component.html',
    styleUrls: ['./average-number-of-moves.component.scss'],
    standalone: false
})
export class AverageNumberOfMovesComponent {
  // chartOptionsBar = {
  //   responsive: true,
  //   plugins: {
  //     legend: {
  //       display: true,
  //     },
  //     tooltip: {
  //       enabled: true,
  //       callbacks: {
  //         label: function (tooltipItem: any) {
  //           return `Value: ${tooltipItem.raw}`;
  //         },
  //       },
  //     },
  //   },
  //   scales: {
  //     x: {
  //       grid: {
  //         display: false,
  //       },
  //       title: {
  //         display: true,
  //         text: "Driver/Spotter",
  //       },
  //     },
  //     y: {
  //       beginAtZero: true,
  //       min: 0,
  //       suggestedMax: 6,
  //       ticks: {
  //         stepSize: 10,
  //         callback: function (value) {
  //           return Number.isInteger(value) ? value : null;
  //         },
  //       },
  //       grid: {
  //         display: true,
  //         drawBorder: false,
  //       },
  //       title: {
  //         display: true,
  //         text: "Total Moves",
  //       },
  //     },
  //   },
  //   elements: {
  //     bar: {
  //       borderWidth: 1,
  //       barThickness: 10,
  //     },
  //   },
  // };
  chartOptionsBar = {
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        left: 0,
        right: 10,
        top: 20,
        bottom: 10
      }
    },
    hover: {
      mode: null
    },
    plugins: {
      legend: {
        display: true,
        labels: {
          font: {
            size: 13
          },
          color: '#000',
        }
      },
      tooltip: {
        enabled: true,
        callbacks: {
          label: function (tooltipItem: any) {
            return `${tooltipItem.dataset.label}: ${tooltipItem.raw}`;
          }
        }
      }
    },
    scales: {
      // x: {
      //   stacked: false,
      //   grid: {
      //     display: false
      //   },
      //   ticks: {
      //     color: '#000',
      //     font: {
      //       size: 12
      //     },
      //     maxRotation: 45,
      //     minRotation: 45
      //   },
      //   title: {
      //     display: true,
      //     text: 'Driver/Spotter',
      //     font: {
      //       size: 13
      //     }
      //   }
      // },
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#000',
          font: {
            size: 12
          },
          maxRotation: 30,
          minRotation: 30
        },
        title: {
          display: true,
          text: 'Driver/Spotter',
          font: {
            size: 13
          }
        }
      },
      y: {
        beginAtZero: true,
        min: 0,
        suggestedMax: 30,
        grid: {
          display: true,
          drawBorder: false
        },
        ticks: {
          stepSize: 3,
          color: '#000',
          font: {
            size: 12
          },
          padding: 4
        },
        title: {
          display: true,
          text: 'Total Moves',
          font: {
            size: 13
          }
        }
      }
    }
  };
  @Input() barChartData: any;
  // @Input() chartOptionsBar: any;
  @Input() loading: boolean = true;

  @Output() toggleWidgetEvent = new EventEmitter<string>();


  toggleWidget() {
    this.toggleWidgetEvent.emit('showAverageNumberOfMoves');


  }
}
