// import { ComponentFixture, TestBed } from '@angular/core/testing';

// import { AverageMoveTimeClientComponent } from './average-move-time-client.component';

// describe('AverageMoveTimeClientComponent', () => {
//   let component: AverageMoveTimeClientComponent;
//   let fixture: ComponentFixture<AverageMoveTimeClientComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [ AverageMoveTimeClientComponent ]
//     })
//     .compileComponents();

//     fixture = TestBed.createComponent(AverageMoveTimeClientComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
