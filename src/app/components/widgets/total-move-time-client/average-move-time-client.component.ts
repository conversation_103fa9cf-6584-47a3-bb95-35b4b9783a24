import { Component, Input, Output, EventEmitter, SimpleChanges } from '@angular/core';

@Component({
    selector: 'app-average-move-time-client',
    templateUrl: './average-move-time-client.component.html',
    styleUrls: ['./average-move-time-client.component.scss'],
    standalone: false
})
export class AverageMoveTimeClientComponent {

  @Input() barChartData: any;
  @Input() chartOptionsBar: any;
  @Input() loading: boolean = true; 
  @Output() toggleWidgetEvent = new EventEmitter<string>(); // Event to notify parent

  
  toggleWidget() {
    this.toggleWidgetEvent.emit('showAverageNumberOfMoves'); // Emit event to parent
  }
  

}
