// import { ComponentFixture, TestBed } from '@angular/core/testing';

// import { AverageNumberOfMovesClientComponent } from './average-number-of-moves-client.component';

// describe('AverageNumberOfMovesClientComponent', () => {
//   let component: AverageNumberOfMovesClientComponent;
//   let fixture: ComponentFixture<AverageNumberOfMovesClientComponent>;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       declarations: [ AverageNumberOfMovesClientComponent ]
//     })
//     .compileComponents();

//     fixture = TestBed.createComponent(AverageNumberOfMovesClientComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
