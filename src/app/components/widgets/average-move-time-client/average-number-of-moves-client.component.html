  <div class="metrics-header">
    <h5 class="metrics-title">Average Move Time - Driver/Spotter</h5>
    <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
        severity="secondary" />
</div>

<!-- Loader -->
<div *ngIf="loading" class="loader-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading data...</p>
</div>

<!-- Bar Chart Container -->
<div class="chart-container" *ngIf="!loading && barChartData">
    <canvas 
        [datasets]="barChartData.datasets" 
        [labels]="barChartData.labels"
        [options]="chartOptionsBar" 
        baseChart 
        type="bar">
    </canvas>
</div>

  