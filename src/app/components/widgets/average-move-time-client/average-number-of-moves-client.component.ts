import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
    selector: 'app-average-number-of-moves-client',
    templateUrl: './average-number-of-moves-client.component.html',
    styleUrls: ['./average-number-of-moves-client.component.scss'],
    standalone: false
})
export class AverageNumberOfMovesClientComponent {

  @Input() barChartData: any;
  @Input() chartOptionsBar: any;
  @Input() loading: boolean = true;
  @Output() toggleWidgetEvent = new EventEmitter<string>(); // Event to notify parent


  toggleWidget() {
    this.toggleWidgetEvent.emit('showAverageMoveTime'); // Emit event to parent
  }

}
