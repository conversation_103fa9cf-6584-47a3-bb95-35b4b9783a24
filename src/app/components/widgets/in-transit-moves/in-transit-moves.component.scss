.dashboard-container {
    display: flex;
    flex-direction: column;
    padding: 16px;
    background-color: #ffffff;
    color: #333;
    font-family: 'Roboto', sans-serif;
    min-height: 200vh;
    /* Ensure the container takes the full height */
}

.chart {
    flex-grow: 1;
    /* Makes sure the chart takes up full available space */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.chart canvas {
    width: 100% !important;
    height: 100% !important;
}


.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* Positions title and button at opposite ends */
    margin-bottom: 8px;
    position: relative;
    /* Adds spacing between header and canvas */
}

@media (max-width: 768px) {
    .chart {
        flex: 1 1 100%;
        /* Make charts take full width on small screens */
        max-width: 100%;
    }
}


.key-metrics-container {
    flex: 0 0 auto;
    /* Prevents the key metrics from stretching */
    width: auto;
}

.key-metrics {
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #ddd;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    font-size: 1rem;
    text-align: left;
    /* Align text to the left */
    width: fit-content;
    /* Adjusts size to fit content */
    max-width: 300px;
    /* Optional: to limit the width */
    text-align: left;

}

.key-metrics p {
    margin: 8px 0;
}

.widget-manager {
    position: absolute;
    top: 20%;
    right: 5%;
    width: 320px;
    /* Match width as per the reference */
    z-index: 1000;
    background: #ffffff;
    border-radius: 12px;
    /* Softer rounded corners */
    padding: 16px;
    /* Reduced padding for compact layout */
    border: 1px solid #e0e0e0;
    /* Subtle border */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    /* Shadow for elevation */
}

.widget-manager h2 {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
}

.widget-manager button {
    background-color: #1976d2;
    /* Blue button for primary action */
    color: #fff;
    font-size: 0.9rem;
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    text-align: center;
    margin-bottom: 16px;
    float: right;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* Subtle shadow */
}

.widget-manager button:hover {
    background-color: #1565c0;
    /* Darker blue on hover */
}

.widget-manager .toggle-container {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.widget-manager .toggle-container label {
    font-size: 1rem;
    color: #333;
    margin-left: 8px;
    /* Space between checkbox and text */
}


::ng-deep .mat-checkbox-checked .mat-checkbox-background {
    background-color: #5045a7 !important;
}

::ng-deep .mat-checkbox-checkmark {
    color: white !important;
    /* Keeps checkmark visible */
}

::ng-deep .mat-checkbox.mat-accent .mat-ripple-element {
    background-color: rgba(128, 0, 128, 0.4) !important;
    /* Adjust transparency */
}


::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
    background-color: #5045a7 !important;
}

::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
    background-color: #5045a7 !important;
}

::ng-deep .mat-slide-toggle-thumb {
    background-color: gray !important;
    /* Default thumb color when inactive */
}

::ng-deep .mat-slide-toggle-bar {
    background-color: lightgray !important;
    /* Default bar color when inactive */
}


/* Separate class for mat-checkbox */
.widget-manager .checkbox-group mat-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    /* Add consistent spacing between checkboxes */
    font-size: 1rem;
    color: #333;
}

.widget-manager .checkbox-group mat-checkbox .mat-checkbox-layout {
    display: flex;
    align-items: center;
    gap: 8px;
    /* Space between checkbox and label */
}

.widget-manager .checkbox-group mat-checkbox .mat-checkbox-label {
    font-size: 1rem;
    color: #333;
    white-space: nowrap;
    /* Prevent label text from wrapping */
}

.widget-manager h4 {
    font-size: 1.1rem;
    margin-top: 16px;
    margin-bottom: 12px;
    color: #333;
    font-weight: bold;
}

.widget-manager .checkbox-group mat-slide-toggle {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.widget-manager .checkbox-group mat-slide-toggle .mat-slide-toggle-label {
    font-size: 1rem;
    color: #333;
}

.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    background: #f5f5f5;
    padding: 8px 16px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.toolbar-buttons {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 16px;
}

.toolbar-buttons mat-form-field {
    margin: 0;
    /* Remove default margin */
    padding: 0;
    /* Remove extra padding */
    height: 36px;
    /* Match the height of mat-button */
    display: flex;
    /* Ensure proper alignment */
    align-items: center;
    /* Center align the contents */
    font-size: 1rem;
    /* Uniform font size */
}

.toolbar-buttons mat-form-field .mat-form-field-wrapper {
    padding: 0;
    /* Remove padding inside the form field */
    height: 36px;
    /* Match the height of mat-button */
    display: flex;
    align-items: center;
}

.toolbar-buttons mat-form-field .mat-form-field-flex {
    padding: 0 8px;
    /* Add horizontal padding for alignment */
    height: 36px;
    /* Uniform height */
    display: flex;
    align-items: center;
    border-radius: 8px;
    /* Rounded corners to match the theme */
}

.toolbar-buttons mat-select {
    height: 36px;
    /* Match the button height */
    line-height: 36px;
    /* Center align the text */
    display: flex;
    align-items: center;
}

.toolbar-buttons mat-form-field.mat-form-field-appearance-fill {
    background: #ffffff;
    /* Match the background color of the buttons */
    border: 1px solid #e0e0e0;
    /* Add border for consistency */
    border-radius: 8px;
    /* Match rounded corners */
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    /* Add subtle shadow */
}

.square-icons {
    display: flex;
    align-items: center;
    gap: 8px;
    /* Space between icons */
}

.square-icons mat-icon {
    font-size: 24px;
    /* Icon size */
    border-radius: 2px;
    /* Slightly rounded corners */
    padding: 8px;
    cursor: pointer;
}


::ng-deep .cdk-overlay-pane {
    z-index: 1000;
    /* Ensure it appears above other elements */
    transform: none !important;
    /* Prevent unwanted translations */
    position: absolute !important;
    /* Keep it aligned to the trigger */
}

// ::ng-deep .mat-select-panel {
//     margin-top: 4px !important;
//     /* Add spacing below the dropdown trigger */
// }

.toolbar-buttons mat-form-field {
    display: flex;
    align-items: center;
    position: relative;
}

.toolbar-buttons mat-form-field .mat-form-field-wrapper {
    position: relative;
    z-index: 1;
}

.refresh-button {
    cursor: pointer !important;
    pointer-events: auto !important;
    /* Ensures hover is detected */
    color: #5f6368;
    /* Subtle gray color */
    display: flex;
    align-items: center;
    gap: 8px;
    /* Space between icon and text */
}

.cooldown-timer,
.last-refresh-time {
    font-size: 0.985rem;
    /* Consistent font size */
    color: #5f6368;
    /* Subtle gray color */
    font-weight: 400;
    vertical-align: middle;
    margin-left: 4px;
    /* Space between text and button */
}

.close-btn {
    position: absolute;
    right: 3px;
    cursor: pointer;
    z-index: 10;
    background: transparent;
    border: none;
}

.close-btn mat-icon {
    font-size: 20px;
    /* Adjust size */
    color: black;
    /* Set color */
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #333;
    /* Slightly darker color on hover */
}

.bg-grey {
    background-color: #898989;
}

.text-grey {
    color: #898989;
}

.bg-orange {
    background-color: #FFAA00;
}

.text-orange {
    color: #FFAA00;
}

.bg-orange {
    background-color: #FFAA00;
}

.text-green {
    color: #0C7B00;
}

.bg-green {
    background-color: #0C7B00;
}

.text-blue {
    color: #2F468C;
}

.bg-blue {
    background-color: #2F468C;
}

.text-purple {
    color: #810BC5;
}

.bg-purple {
    background-color: #810BC5;
}

.text-brown {
    color: #65340D;
}

.bg-brown {
    background-color: #65340D;
}

.text-orange-dark {
    color: #FF6F00;
}

.bg-orange-dark {
    background-color: #FF6F00;
}

.text-cyan {
    color: #04A595;
}

.bg-cyan {
    background-color: #04A595;
}

.flex-container {
    display: flex;
    flex-direction: row;

}

/* Change flex direction to column for screens smaller than lg and xl */
@media (max-width: 991.98px) {
    .flex-container {
        flex-direction: column;
    }
}

@media (min-width: 1200px) {
    .card {
        min-height: 120px;
        /* Set your desired minimum height for xl screens here */
    }
}

.ng-container {
    position: relative;
    background: white;
    padding: 15px;
    /* Redung-containerce padding */
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 100%;
    /* Ensure it doesn’t overflow */
    overflow: hidden;
    /* Hide any excess content */
}


// .metrics-title {
//     font-size: 18px;
//     position: relative;
//     margin-top: 0px;  /* Reduce space below the title */
//     color: #333;
//     display: flex;
//     flex-direction: column;
//     align-items: center;
//     justify-content: center;
// }

// .metrics-grid {
//     display: flex;
//     justify-content: space-between;
//     flex-wrap: wrap;  /* Ensure cards wrap properly instead of overflowing */
//     gap: 5px;  /* Adjust gap between metric cards */
//     margin-top: 2px;  /* Reduce margin between title and grid */
// }


// .metric-card {
//     background: #fff;
//     padding: 10px;  /* Reduce padding to fit better */
//     border-radius: 10px;
//     box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
//     flex: 1 1 calc(25% - 10px);
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     min-width: 100px;  /* Reduce min-width to fit smaller screens */
//     max-width: 200px;  /* Reduce max-width to prevent overflow */
//     overflow: hidden;  /* Prevent content from overflowing */
// }


// .metric-value {
//     cursor: pointer;
//     font-size: 18px;
//     color: #333;
// }

// .metric-icon {
//     width: 3.5rem;
//     height: 3.5rem;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     border-radius: 8px;
// }

.metrics-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
}

.metrics-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    color: #333;
    font-family: inter, system-ui, Segoe UI, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol;
}

.close-btn {
    position: absolute;
    right: 10px;
    background: transparent;
    border: none;
}

.loader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.chart-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

/* Responsive */
@media (max-width: 768px) {
    .chart-container {
        max-width: 100%;
        padding: 10px;
    }

    .metrics-header {
        flex-direction: column;
        text-align: center;
    }

    .close-btn {
        position: static;
        margin-top: 5px;
    }
}


.grid-stack {
    padding-right: 20px;
}

.grid-stack-item {
    background: #fff;
    border-radius: 8px;
    box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
    display: flex;
    flex-direction: column;
    justify-content: stretch;
    padding: 10px;
    /* Space inside each widget */
    margin: 18px;
    /* Space between widgets */
    position: static !important;
}

.grid-stack-item-content {
    width: 100%;
    position: static !important;
    display: flex;
    flex-direction: column;

}


.loader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100px;
}

mat-spinner {
    margin-bottom: 10px;
}

.dropdown-container {
    position: relative;
    z-index: 1000;
    /* Ensure it's above other elements */
}

:host {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chart-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    z-index: 1;
    position: relative;
    cursor: pointer;
}

canvas {
    width: 100% !important;
    height: 100% !important;
}


.heatmap-table {
    overflow-x: auto;
    font-family: Arial, sans-serif;
    z-index: 1;
    position: relative;
    padding: 15px;
}

table {
    border-collapse: collapse;
    width: 100%;
}

th,
td {
    padding: 2px;
    text-align: center;
}

.day-label {
    font-weight: bold;
    background-color: #eee;
}

.cell {
    // width: 32px;
    width: 100%;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: #000;
}

.table{
    padding: 12px;
    z-index: 10;
}