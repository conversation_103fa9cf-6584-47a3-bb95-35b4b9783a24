import { Component, Input, Output, EventEmitter, OnInit, HostListener } from '@angular/core';

@Component({
  selector: 'app-in-transit-moves',
  templateUrl: './in-transit-moves.component.html',
  styleUrls: ['./in-transit-moves.component.scss'],
  standalone: false
})
export class InTransitMovesComponent implements OnInit {
  @Output() toggleWidgetEvent = new EventEmitter<string>();
  @Input() inTransitData: any;
  @Input() loading: boolean;
  dynamicScrollHeight: string;
  columnEnableByRole: boolean = true;
  constructor() { }

  toggleWidget() {
    this.toggleWidgetEvent.emit('in-transit-moves');
  }

  @HostListener('window:resize')
  onResize() {
    this.setScrollHeight();
  }

  setScrollHeight() {
    const windowHeight = window.innerHeight;
    const offset = 300;
    const calculatedHeight = windowHeight - offset;
    this.dynamicScrollHeight = `${calculatedHeight}px`;
  }


  ngOnInit(): void {
    let getAccessToken = JSON.parse(localStorage.getItem('access_token'));
    if (getAccessToken.roles[0].includes('ROLE_CLIENT')) {
      this.columnEnableByRole = false;
    }
  }

}
