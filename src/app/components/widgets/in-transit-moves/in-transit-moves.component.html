<div class="metrics-header">
    <span class="metrics-title">Transit Moves</span>
    <p-button icon="pi pi-times" [rounded]="false" [text]="true" (click)="toggleWidget()" class="close-btn"
        severity="secondary" />
</div>

<div *ngIf="loading" class="loader-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading data...</p>
</div>

<div class="table" *ngIf="!loading">
    <p-table [value]="inTransitData" [scrollable]="true" scrollHeight="350px" [tableStyle]="{ 'min-width': '50rem' }">
        <ng-template #header>
            <tr>
                <th></th>
                <th>Spot pickup time</th>
                <th>Priority</th>
                <th>Pickup Location</th>
                <th>Drop Location</th>
                <th>Trailer/Container</th>
                <th *ngIf="columnEnableByRole">Assigned to</th>
            </tr>
        </ng-template>

        <ng-template #body let-getinTransitSpot let-rowIndex="rowIndex">
            <tr>
                <td>{{ rowIndex + 1 }}</td>
                <td>{{ getinTransitSpot.spotPickupTime }}</td>
                <td>{{ getinTransitSpot.priority }}</td>
                <td>{{ getinTransitSpot.pickupLocation }}</td>
                <td>{{ getinTransitSpot.dropLocation }}</td>
                <td>{{ getinTransitSpot.trailer }}</td>
                <td *ngIf="columnEnableByRole">{{ getinTransitSpot.assignedTo }}</td>
            </tr>
        </ng-template>

        <ng-template #emptymessage>
            <tr>
                <td colspan="7" class="text-center">No Transit Moves</td>
            </tr>
        </ng-template>

    </p-table>
</div>