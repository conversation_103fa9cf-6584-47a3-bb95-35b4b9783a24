import { Component, Input, ViewChild, ComponentFactoryResolver, ViewContainerRef, OnChanges, SimpleChanges } from '@angular/core';
import { AverageMoveTimeComponent } from './average-move-time/average-move-time.component'; 
import { AverageNumberOfMovesComponent } from './total-moves/average-number-of-moves.component';
import { KeyMetricsComponent } from './key-metrics/key-metrics.component';

const COMPONENT_MAP = {
  'average-move-time': AverageMoveTimeComponent,
  'average-number-of-moves': AverageNumberOfMovesComponent,
  'key-metrics': KeyMetricsComponent,
};

@Component({
  selector: 'app-dynamic-widget',
  template: `<ng-container #container></ng-container>`,
})
export class DynamicWidgetComponent implements OnChanges {
  @Input() widgetType: string;
  @Input() data: any;
  @ViewChild('container', { read: ViewContainerRef, static: true }) container!: ViewContainerRef;

  constructor(private componentFactoryResolver: ComponentFactoryResolver) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['widgetType']) {
      this.loadComponent();
    }
  }

  loadComponent() {
    this.container.clear();
    const componentClass = COMPONENT_MAP[this.widgetType];

    if (componentClass) {
      const componentFactory = this.componentFactoryResolver.resolveComponentFactory(componentClass);
      const componentRef = this.container.createComponent(componentFactory);
      Object.assign(componentRef.instance, this.data);
    }
  }
}
