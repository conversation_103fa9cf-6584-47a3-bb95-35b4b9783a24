:host ::ng-deep {
    .p-toolbar{
    background-color: #f5f5f5 !important;
    border: none !important;
    }

    .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
        text-align: center !important;
       
    }

    .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
        text-align: center !important;
    }
    
    .p-dialog .p-dialog-content {
        padding: 0rem 1.5rem !important
    }
 
.knobs,
.layer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.button {
    position: relative;
    border: 1px solid rgb(99, 121, 150);
    // top: 50%;
    width: 74px;
    height: 36px;
    // margin: -20px auto 0 auto;
    margin-left: 50px;
    overflow: hidden;
    
  }

  .button.r,
.button.r .layer {
  border-radius: 100px;
  
}
  
  .checkbox {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 3;
  }
  
  .knobs {
    z-index: 2;
  }
  
  .layer {
    width: 100%;
    background-color: #a2a7a8;
    transition: 0.3s ease all;
    z-index: 1;
  }

  #button-1 .knobs:before {
    content: "NO";
    position: absolute;
    top: 4px;
    left: 4px;
    width: 20px;
    height: 10px;
    color: #fff;
    font-size: 10px;
    font-weight: bold;
    text-align: center;
    line-height: 1;
    padding: 9px 4px;
    background-color: #E03C32;
    border-radius: 50%;
    transition: 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15) all;
  }
  
  #button-1 .checkbox:checked + .knobs:before {
    content: "YES";
    left: 42px;
    background-color: #639754;
  }
  
  #button-1 .checkbox:checked ~ .layer {
    background-color: #cae7f3;
  }
  
  #button-1 .knobs,
  #button-1 .knobs:before,
  #button-1 .layer {
    transition: 0.3s ease all;
  }
  
}