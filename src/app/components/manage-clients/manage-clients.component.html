<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>
    <div class="col-12">
        <div class="card">
            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <ng-template pTemplate="left">
                    <div class="my-2">
                        <h5 class="m-0">Clients</h5>
                    </div>
                </ng-template>
                
                <ng-template pTemplate="right">
                    <button *ngIf="hideButtons == false && hideButtonsIfSupervisor == false" pButton pRipple label="New"
                        icon="pi pi-plus" class="p-button-success mr-2" (click)="routeToAddClient()"></button>
                    <!-- <button pButton pRipple label="Export" icon="pi pi-upload" class="p-button-help" (click)="exportExcel()"></button> -->
                
                    <p-splitbutton label="Export" [model]="items" (onClick)="exportExcel()" raised severity="help" />
                
                </ng-template>
            
            </p-toolbar>

            <p-table showGridlines #dt [value]="clients" [loading]="loading" styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true" [columns]="cols" [globalFilterFields]="['clientName']" [rows]="10" [rowHover]="true" dataKey="id">
                <ng-template #caption>
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <p-iconfield iconPosition="left" class="ml-auto">
                            <p-inputicon>
                                <i class="pi pi-search"></i>
                            </p-inputicon>
                            <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')"
                                placeholder="Search..." />
                        </p-iconfield>
                    </div>
                </ng-template>
                <ng-template #header>
                    <tr>
                        <th></th>
                        <th pSortableColumn="clientName">Name</th>
                        <th pSortableColumn="contactEmail">Email</th>
                        <th pSortableColumn="contactPhone">Phone</th>
                        <th pSortableColumn="address">Address</th>
                        <th pSortableColumn="flag">Feature</th>
                    </tr>
                </ng-template>
                <ng-template #body let-client>
                    <tr>
                        <td style="min-width: 255px;">
                            <button pButton pRipple icon="pi pi-eye" pTooltip="View" class="p-button-rounded p-button-success mr-2" (click)="routeToDetails(client.clientId)"></button>
                            <button pButton pRipple icon="pi pi-map-marker" pTooltip="Locations" class="p-button-rounded p-button-help mr-2" (click)="routeToLocation(client.clientId,client.clientName)"></button>
                            <button *ngIf="hideButtons == false && hideButtonsIfSupervisor == false" pButton pRipple icon="pi pi-pencil" pTooltip="Edit" class="p-button-rounded p-button-warning mr-2" (click)="routeToEdit(client.clientId)"></button>
                            <button *ngIf="hideButtons == false && hideButtonsIfSupervisor == false" pButton pRipple icon="pi pi-flag" pTooltip="Edit" class="p-button-rounded p-button-secondary mr-2" (click)="showConfig(client)"></button>
                            <button *ngIf="hideButtons == false && hideButtonsIfSupervisor == false" pButton pRipple icon="pi pi-trash" pTooltip="Delete/Deactivate" class="p-button-rounded p-button-danger" (click)="deleteClientConfirmation(client)"></button>
                        </td>
                        <td>
                            {{client.clientName}}
                        </td>
                        <td>
                            {{client.contactEmail}}
                        </td>
                        <td>
                            {{client.contactPhone}}
                        </td>
                        <td>
                            {{client.street}}, 
                            {{client.city}}, 
                            {{client.state}}, 
                            {{client.zip}}
                        </td>
                        <td style="min-width: 220px;">
                           <div *ngIf="client.bol == true">BoL</div>
                           <!-- <div *ngIf="client.dvir == true">DVIR</div> -->
                           <div *ngIf="client.accountDeactivation == true">Inactive Account</div>
                           <div *ngIf="client.trailerAudit == true">Trailer Audit</div>
                           <div *ngIf="client.jobReAssign == true">Bucket System Fallback Timer</div>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="5">No Client found.</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-paginator [rows]="10" [showCurrentPageReport] = "true" currentPageReportTemplate="Showing {first} to {last} of {totalRecords}" [totalRecords]="totalRecords"  (onPageChange)="paginate($event)"></p-paginator>
        </div>

        <p-dialog [(visible)]="deleteClientDialog" header="Confirm" [modal]="true" [style]="{width:'450px'}">
            <div class="flex align-items-center justify-content-center">
                <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem"></i>
                <span *ngIf="client">Are you sure you want to delete/deactivate <b>{{client.clientName}}</b>?</span>
            </div>
            <ng-template #footer>
                <button pButton pRipple icon="pi pi-times" class="p-button-text" label="No" (click)="deleteClientDialog = false"></button>
                <button pButton pRipple icon="pi pi-check" class="p-button-text" label="Yes" (click)="confirmDelete(client.clientId)"></button>
            </ng-template>
        </p-dialog>

        <p-dialog [(visible)]="clientConfigModal" [style]="{width: '450px'}" [modal]="true"
            (onHide)="hideDialog1()">
            <ng-template #header>
                <h4>{{selectedClient.clientName}}&nbsp;&nbsp;Configurations</h4>
            </ng-template>
            <ng-template pTemplate="content">
                <div class="field"
                    style="display: flex;flex-direction: row; justify-content: space-between; margin-bottom: 20px;margin-top: 20px;">
                    <h5 style="margin-top: 10px;margin-right: 140px;">BoL</h5>
                    <p-selectButton [options]="stateOptions" [(ngModel)]="selectedClient.bol" optionLabel="label"
                        optionValue="value" (ngModelChange)="onSelectChange('bol',$event)"></p-selectButton>
                </div>
                <div class="field" style="display: flex;flex-direction: row; justify-content: space-between;">
                    <h5 style="margin-top: 10px;margin-right:70px;">Trailer Audit</h5>
                    <p-selectButton [options]="stateOptions" [(ngModel)]="selectedClient.trailerAudit" optionLabel="label"
                        optionValue="value" (ngModelChange)="onSelectChange('trailerAudit',$event)"></p-selectButton>
                </div>
                <!-- <div class="field" style="display: flex;flex-direction: row;">
                                                   <h5 style="margin-top: 10px;margin-right:130px;">DVIR</h5>
                                                      <p-selectButton [options]="stateOptions" [(ngModel)]="selectedClient.dvir" optionLabel="label" optionValue="value" (ngModelChange)="onSelectChange('dvir',$event)" class="ng-invalid ng-dirty"></p-selectButton>
                                                   </div>   -->
                <div *ngIf="hideFlagAccountDeletion === false" class="field"
                    style="display: flex;flex-direction: row; justify-content: space-between; margin-top: 22px;">
                    <h5 style="margin-right: -30px;width: 200px;">Inactive Account Deletion</h5>
                    <p-selectButton [options]="stateOptions" [(ngModel)]="selectedClient.accountDeactivation" optionLabel="label"
                        optionValue="value" (ngModelChange)="onSelectChange('accountDeactivation',$event)"></p-selectButton>
                </div>
                <div class="field" style="display: flex;flex-direction: row; justify-content: space-between;">
                    <h5 style="margin-right:-10px;width: 180px;">Bucket System Fallback Timer</h5>
                    <p-selectButton [options]="stateOptions" [(ngModel)]="selectedClient.jobReAssign" optionLabel="label"
                        optionValue="value" (ngModelChange)="onSelectChange('jobReAssign',$event)"></p-selectButton>
                </div>
            </ng-template>
            <ng-template #footer>
                <div style="text-align: left;">
                    <button pButton pRipple label="Return" icon="pi pi-arrow-left" class="p-button-text" (click)="hideDialog1()"
                        type="button"></button>
                </div>
            </ng-template>
        </p-dialog>
    </div>
</div>
