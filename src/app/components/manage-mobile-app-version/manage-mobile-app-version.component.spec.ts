import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ManageMobileAppVersionComponent } from './manage-mobile-app-version.component';

describe('ManageMobileAppVersionComponent', () => {
  let component: ManageMobileAppVersionComponent;
  let fixture: ComponentFixture<ManageMobileAppVersionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ManageMobileAppVersionComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ManageMobileAppVersionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
