import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { ManageJobsService } from '../manage-jobs/manage-jobs.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import * as moment from 'moment';
import { map } from 'rxjs';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { ManageFleetsService } from '../manage-fleets/manage-fleets.service';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { TokenService } from 'src/app/security/token.service';
import { ManageClientsService } from '../manage-clients/manage-clients.service';

@Component({
  selector: 'app-manage-report-trailer-history',
  templateUrl: './manage-report-trailer-history.component.html',
  styleUrls: ['./manage-report-trailer-history.component.scss'],
  standalone: false
})
export class ManageReportTrailerHistoryComponent implements OnInit {

  breadcrumbItems: MenuItem[];
  selectedTrailerLogs = [];
  cols: any[];
  loadingSelectedTrailerJobs = false;
  fromDate:any;
  toDate:any;
  querySelectedTrailerJobs: IpagedQuery;
  query:IpagedQuery;
  totalRecords:any;
  uniqueFleetId:string;
  filteredFleetId;
  filteredFleets: any[] = [];
  filteredFleet;
  sixMonthValidDate: boolean = true;
  validDate: boolean = true;
  isTrailerSelected: boolean = true;
  items: MenuItem[];
  accessToken: string;
  userRoles: any;
  clientList = [];
  selectedClient: any;
  clientId: string;
  selectedLocations= [];
  isITOrAdmin: boolean = false;
  isSupervisorOrClient: boolean = false;
  clientName: string;

  constructor(private activatedRoute: ActivatedRoute,
              private manageJobsService: ManageJobsService,
              private errorService: ErrorService,
              private manageClientService: ManageClientsService,
              private loader: AppLoaderService,
              private manageFleetsService: ManageFleetsService,
              private tokenService: TokenService
  ) {

    this.activatedRoute.queryParams.subscribe(qparams => {

      if (qparams["clientId"]) {
        this.clientId = qparams["clientId"];
      }

      if (qparams["fleetId"]) {
        this.uniqueFleetId= qparams["fleetId"];
      }
      
      if(qparams["fromDate"]) {
        this.fromDate = qparams["fromDate"]
      }

      if(qparams["toDate"]) {
        this.toDate = qparams["toDate"]
      }
       
    })

    this.items = [
      {
          label: 'Excel',
          icon: 'pi pi-download',
          command: () => {
              this.exportExcel();
          }
      }
      ,
      {
          label: 'PDF',
          icon: 'pi pi-download',
          command: () => {
              this.exportPdf();
          }
      }
  ];
}

  ngOnInit(): void {

    this.breadcrumbItems = [];
    this.breadcrumbItems.push({ label: 'Reports',routerLink:'../manage-report'});
    this.breadcrumbItems.push({ label: 'Trailer History'});
    this.querySelectedTrailerJobs = {isActive: true, size:50, page: 0};
    this.query = { isActive: true, size: 50, page: 0 };
    this.filterFleets(null, this.uniqueFleetId);
    this.getTrailerHistory();
    // this.getJobs();
    // this.getTrailerHistory("COMPLETED");
    this.accessToken = this.tokenService.getAccessToken();
    this.userRoles = this.tokenService.getUserRoles();
    this.isITOrAdmin = this.userRoles.some(
      role => role === APP_CONSTANTS.USER_ROLES.ROLE_IT || role === APP_CONSTANTS.USER_ROLES.ROLE_ADMIN
    );
    
    if(this.clientId != null){
      this.getClientList(null, this.clientId);
    }

  }

    jobCompletionTime(completionTime) {
      if (completionTime) {
        completionTime = parseInt(completionTime) //because moment js dont know to handle number in string format
        var hours = Math.floor(moment.duration(completionTime, 'seconds').asHours()).toLocaleString();
        var minutes = moment.duration(completionTime, 'seconds').minutes().toLocaleString();
        var seconds = moment.duration(completionTime, 'seconds').seconds().toLocaleString();
        hours = hours.length == 1 ? "0" + hours : hours;
        minutes = minutes.length == 1 ? "0" + minutes : minutes;
        seconds = seconds.length == 1 ? "0" + seconds : seconds;
        var format = hours + ":" + minutes + ":" + seconds;
        //Math.floor(moment.duration(seconds,'seconds').asHours()) + ':' + moment.duration(seconds,'seconds').minutes() + ':' + moment.duration(seconds,'seconds').seconds();
        return format;
      } else {
        return completionTime;
      }
    }

    extractTextBeforeColon(text) {
      let colonIndex = text.indexOf(':');
      if (colonIndex !== -1) {
        return text.substring(0, colonIndex + 1);
      }
      return null; // Return full text if ':' is not found
    }

    removeTextBeforeColon(text) {
      let parts = text.split(':');
      if (parts.length > 1) {
        return parts[1].trim();
      }
      return text.trim(); // Return trimmed text if ':' is not found
    }

    getJobs() {

      if(this.uniqueFleetId == null) {
        this.isTrailerSelected = false;
        return;
      }

      this.loadingSelectedTrailerJobs = true;
      this.manageJobsService.viewJobs(this.querySelectedTrailerJobs, null, null, this.fromDate, this.toDate, this.uniqueFleetId, null, null, null, null, null, true)
        .pipe(
          map(jobs => {
            let jobList = [];
            for (let job of jobs.list) {
              let obj = {
                ...job,
                jobCompletionSeconds: this.jobCompletionTime(job.jobCompletionSeconds)
                //job.jobCompletionSeconds ? moment.utc(job.jobCompletionSeconds*1000).format('HH:mm:ss') : '-'
              }
              if (job.description) {
                var descriptionRole = this.extractTextBeforeColon(job.description);
                var description = this.removeTextBeforeColon(job.description);
                obj = {
                  ...obj,
                  description: description,
                  descriptionRole: descriptionRole
                }
              }
              if (job.pickupNotes) {
                var pickupRole = this.extractTextBeforeColon(job.pickupNotes);
                var pickupNotes = this.removeTextBeforeColon(job.pickupNotes);
                obj = {
                  ...obj,
                  pickupNotes: pickupNotes,
                  pickupRole: pickupRole
                }
              }
  
              if (job.dropNotes) {
                var dropRole = this.extractTextBeforeColon(job.dropNotes);
                var dropNotes = this.removeTextBeforeColon(job.dropNotes);
                obj = {
                  ...obj,
                  dropNotes: dropNotes,
                  dropRole: dropRole
                }
              }
              jobList.push(obj);
            }
            return {
              ...jobs,
              list: jobList
            }
          })
        )
        .subscribe(response => {
            this.selectedTrailerLogs = response.list;
            this.totalRecords = response.totalElements;
            this.loadingSelectedTrailerJobs = false; 
        }, (error) => {
          this.loader.hide();
          this.loadingSelectedTrailerJobs = false;
          this.errorService.handleError(error, true);
        })
    }

    onFleetSelect(event) {
      console.log("event", event);
      
      this.isTrailerSelected = true;
     
      this.selectedTrailerLogs = [];
      this.uniqueFleetId = event.value.fleetId ? event.value.fleetId : null;
      console.log("uniqueFleetId",this.uniqueFleetId);
      
      this.getTrailerHistory();
    }
  
    filterFleets(event?, fleetId?) {

        this.manageFleetsService.viewFleets(this.query, this.clientId, event?.query, null, null, null,fleetId)
          .pipe(
            map(fleets => {
              let fleetsArray = [];
              for (let fleet of fleets.list) {
                let obj = {
                  ...fleet,
                  fleetAndHotTrailer: this.checkIfHotTrailer(fleet)
                };
                fleetsArray.push(obj);
              }
              return {
                list: fleetsArray
              }
            })
          ).subscribe(response => {
            this.filteredFleets = response.list;
            
            if(fleetId) {
                this.filteredFleetId = this.filteredFleets[0];
            }
            this.loader.hide();
          }, (error) => {
            this.loader.hide();
            this.errorService.handleError(error, true);
          });
  
    }
  
    clearUniqueId(event) {

       this.uniqueFleetId = null;
       this.getTrailerHistory();
    }
  
    checkIfHotTrailer(fleet) {
      if (fleet.isHotTrailer) {
        return `${fleet.unitNumber} - (Hot Trailer)`
      } else {
        return `${fleet.unitNumber}`
      }
    }
    
    filterJobsByDate(){
      this.sixMonthValidDate = true;
      if(this.fromDate == '' || this.toDate == ''){
        this.validDate = false;
        return;
      }
      if(this.fromDate != '' && this.toDate != '') {
        this.validDate = true;
        const from = new Date(this.fromDate);
        const to = new Date(this.toDate);
        const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());
        if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
          this.sixMonthValidDate = false;
          return;
        }
          this.getTrailerHistory();
      }
    }

    paginate(event){
  
      this.querySelectedTrailerJobs.page = event.page;
      this.getTrailerHistory();
    }

    exportExcel(){

      if(this.fromDate == '' || this.toDate == ''){
        this.validDate = false;
        return;
      }

      const from = new Date(this.fromDate);
      const to = new Date(this.toDate);
      const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());
      if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
        this.sixMonthValidDate = false;
        return;
      }
      const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/fleets/history/export/excel`);
      url.searchParams.append("isActive", "true");
      url.searchParams.append("access_token", this.accessToken);
      
      if (this.fromDate) {
          url.searchParams.append("fromDate", this.fromDate);
      }
          
      if (this.toDate) {
         url.searchParams.append("toDate", this.toDate);
      }

      if(this.uniqueFleetId) {
        url.searchParams.append("fleet.uuid", this.uniqueFleetId);
      }

      window.open(url.toString(), '_blank');    
  } 

   exportPdf() {
   
    if(this.fromDate == '' || this.toDate == ''){
      this.validDate = false;
      return;
    }

    const from = new Date(this.fromDate);
    const to = new Date(this.toDate);
    const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());
    if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
      this.sixMonthValidDate = false;
      return;
    }
    const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/fleets/history/export/pdf`);
    url.searchParams.append("isActive", "true");
    url.searchParams.append("access_token", this.accessToken);
    
    if (this.fromDate) {
        url.searchParams.append("fromDate", this.fromDate);
    }
        
    if (this.toDate) {
       url.searchParams.append("toDate", this.toDate);
    }

    if(this.uniqueFleetId) {
      url.searchParams.append("fleet.uuid", this.uniqueFleetId);
    }

    window.open(url.toString(), '_blank');    
          
  }

  getTrailerHistory() {

    if(this.uniqueFleetId == null) {
      this.isTrailerSelected = false;
      return;
    }

    this.loadingSelectedTrailerJobs = true;
    this.manageFleetsService.getTrailerHistory(this.query, null, null, this.uniqueFleetId, this.fromDate, this.toDate)
      .subscribe(response => {
        this.selectedTrailerLogs = response.list;
        this.totalRecords = response.totalElements;
        this.loadingSelectedTrailerJobs = false;
        this.loader.hide();
      }, (error) => {
        this.loader.hide();
        this.loadingSelectedTrailerJobs = false;
        this.errorService.handleError(error, true);
      });
  }

  onClientSelect(event) {
    this.filteredFleetId = "";
    this.uniqueFleetId = null;
    this.clientId = event.value.clientId;
    this.selectedTrailerLogs = [];
  }

  getClientList(event?,clientId?) {
    if(event) {
      this.clientName = event.query;
    }
    this.loader.show();           
    this.manageClientService.viewClients(this.query, this.clientName, clientId).subscribe(response => {
      this.clientList = response.list;
      if(clientId) {
        this.selectedClient = this.clientList[0];
      }
      
      this.loader.hide();
      if(this.isSupervisorOrClient)
      {
        this.clientId = this.clientList[0].clientId;
        this.selectedClient = this.clientList[0];
      }
      
    }, (error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }

  clearClientFilter(event) {
      
    this.clientId = '';
    this.selectedClient = '';
}

}
