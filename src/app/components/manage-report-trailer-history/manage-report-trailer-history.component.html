<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>
    <div class="col-12" >
        <div class="card">
            <p-toast></p-toast>

            <p-toolbar styleClass="mb-4">
                <div class="w-full">
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <div class="my-2">
                            <h5 class="m-0">Trailer History</h5>
                        </div>

                        <div>

                            <p-splitButton  label="Export" [model]="items" (onClick)="exportExcel()" styleClass="p-button-raised p-button-help mr-2 "></p-splitButton>
                   
                        </div>
                    </div>    
            
                <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center mrgt-30">
                    
                        <span>
                            <p-autocomplete placeholder="Filter By Client" 
                                [suggestions]="clientList" (onSelect)="onClientSelect($event)" dataKey="clientId"
                                (completeMethod)="getClientList($event)" [dropdown]="true" [(ngModel)]="selectedClient"
                                (onClear)="clearClientFilter($event)" class="mrgr-10" field="clientName">
                                <ng-template let-client pTemplate="item">
                                    <div>{{client.clientName}}</div>
                                </ng-template>
                                </p-autocomplete>
                            <p-autoComplete placeholder="Filter By Trailer/Unit#" [(ngModel)]="filteredFleetId"
                            [suggestions]="filteredFleets" (onSelect)="onFleetSelect($event)" dataKey="fleetId"
                            (completeMethod)="filterFleets($event)" [dropdown]="true"
                            (onClear)="clearUniqueId($event)" class="mrgr-10" field="fleetAndHotTrailer">
                            <ng-template let-fleet pTemplate="item">
                              <div>{{fleet.fleetAndHotTrailer}}</div>
                            </ng-template>
                            </p-autoComplete>
                            <span class="text-danger" *ngIf="isTrailerSelected === false">
                                  *Select a trailer
                            </span>
                        </span>
                    
                        <div style="display: flex;flex-direction: row; margin-top: 8px;">
                            <span class="mx-2 font-medium" >*From Date : <input pInputText type="date"
                                [(ngModel)]="fromDate" name="fromDate"
                                placeholder="Select Date" (change)="filterJobsByDate()"/></span>        
                            <span class="mx-2 font-medium">*To Date : <input pInputText type="date" [(ngModel)]="toDate"
                                 name="toDate" placeholder="Select Date" (change)="filterJobsByDate()"/></span>
                        </div>
                        <span class="text-danger" *ngIf="validDate == false">* Please select From Date and To Date</span> 
                        <span class="text-danger" *ngIf="sixMonthValidDate === false">
                            * Cannot export more than Six months. Please change the dates.
                        </span>
                </div>
              </div>  
            </p-toolbar>
        <p-table #dt3 [value]="selectedTrailerLogs"
        styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
        [columns]="cols" [rows]="10"
        [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
        [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'" [loading]="loadingSelectedTrailerJobs">

        <ng-template pTemplate="header">
            <tr>
                <th pSortableColumn="createdDate">Creation Date</th>
                <th pSortableColumn="actions">Action</th>
                <th pSortableColumn="jobPickupTime">Spot Pickup Time</th>
                <th pSortableColumn="jobDropoffTime">Spot Dropoff Time</th>
                <th pSortableColumn="jobNumber">Spot Number</th>
                <th pSortableColumn="jobPriority">Spot Priority</th>
                <th pSortableColumn="pickupLocation">Pickup Location</th>
                <th pSortableColumn="dropLocation">Drop Location</th>
                <th pSortableColumn="description" style="width: 160px;">Notes</th>
                <th pSortableColumn="trailerTruck">Trailer/Container</th>
                <th pSortableColumn="status">Status</th>
                <th pSortableColumn="assignedTo">Assigned To</th>
                <th pSortableColumn="createdBy">Created By</th>
                <th pSortableColumn="lastModifiedDate">Last Updated</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-logs>
            <tr>
                <td>
                    {{logs.audit?.createdDate}}
                </td>
                <td>
                    <span *ngIf="logs.job != undefined">
                        Move of trailer ({{logs.fleet?.unitNumber}}) from the location ({{ logs.job?.pickupLocation?.locationName }} - {{logs.job?.pickupSpot?.spotName}}) to the location ({{logs.job?.dropLocation?.locationName}} - {{logs.job?.dropSpot?.spotName}}) 
                    </span>
                    <span *ngIf="logs.actions != undefined && logs.job == undefined">
                        {{logs.actions}}
                    </span>
                </td>
                <td>
                    
                    {{logs.job?.pickupDateTime ? logs.job?.pickupDateTime : '-'}}
                </td>
                <td>
                    
                    {{logs.job?.dropDateTime ? logs.job?.dropDateTime : '-'}}
                </td>
                <td>
                    {{logs.job?.jobNumber}}
                </td>
                <td class="text-center">
                    <p-tag *ngIf="logs.job?.priority == 'HIGH'" rounded="true" severity="danger"
                        value="High"></p-tag>
                    <p-tag *ngIf="logs.job?.priority == 'MEDIUM'" rounded="true" severity="primary"
                        value="Medium"></p-tag>
                    <p-tag *ngIf="logs.job?.priority == 'LOW'" rounded="true" severity="success"
                        value="Low"></p-tag>
                </td>
                <td>
                    
                    <span *ngIf="logs.job?.pickupLocation != undefined">
                        {{logs.job?.pickupLocation?.locationName}}, {{logs.job?.pickupLocation?.state}} - 
                    <br/>
                    {{logs.job?.pickupSpot?.spotName}}
                    </span>
                </td>
                <td>
                   
                    <span *ngIf="logs.job?.dropLocation != undefined">
                        {{logs.job?.dropLocation?.locationName}}, {{logs.job?.dropLocation?.state}} - 
                    <br/>
                    {{logs.job?.dropSpot?.spotName}}
                    </span>
                </td>  
                <td style="max-width: 100px; overflow: hidden;word-wrap: break-word;">
                   
                    <div *ngIf="logs.job?.description">
                        <b>{{logs.job?.descriptionRole}}</b> {{logs.job?.description}}
                    </div>
                    <div *ngIf="logs.job?.pickupNotes">
                        <b>{{logs.job?.pickupRole}}</b> {{logs.job?.pickupNotes}}
                    </div>
                    <div *ngIf="logs.job?.dropNotes">
                        <b>{{logs.job?.dropRole}}</b> {{logs.job?.dropNotes}}
                    </div>
                </td>
                <td>
                    
                    <span *ngIf="logs.job != undefined">
                        {{logs.job?.fleet?.unitNumber}}
                    </span>
                    <span *ngIf="logs.job == undefined">
                        {{logs.fleet?.unitNumber}}
                    </span>
                </td>
                <td>
                   
                    <p-tag *ngIf="logs.job?.status == 'OPEN'" rounded="true" severity="warning"
                        [value]="logs.job?.status"></p-tag>
                    <p-tag *ngIf="logs.job?.status == 'IN_TRANSIT'" rounded="true" severity="primary"
                        [value]="logs.job?.status"></p-tag>
                    <p-tag *ngIf="logs.job?.status == 'COMPLETED'" rounded="true" severity="success"
                        [value]="logs.job?.status"></p-tag>
                    <p-tag *ngIf="logs.job?.status == 'EXCEPTION'" rounded="true" severity="danger"
                        [value]="logs.job?.status"></p-tag>
                    <p-tag *ngIf="logs.job?.status == 'QUEUE'" rounded="true" severity="danger"
                                            [value]="logs.job?.status"></p-tag>
                </td>
                <td>
                    {{logs.job?.assignedTo?.firstName}} {{logs.job?.assignedTo?.lastName}}
                </td>
                <td>
	                <span *ngIf="logs.job != undefined">{{logs.job?.audit?.lastModifiedBy?.firstName}} {{logs.job?.audit?.lastModifiedBy?.lastName}}</span>
                    <span *ngIf="logs.job == undefined">{{logs.audit.createdBy?.firstName}} {{logs.audit.createdBy?.lastName}}</span>
                </td>
                <td>
                    {{logs.audit?.lastModifiedDate}}
                </td>
            </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
            <tr>
                <td colspan="14">No spots found.</td>
            </tr>
        </ng-template>
        </p-table>
        <p-paginator [rows]="50" [showCurrentPageReport]="true"
        currentPageReportTemplate="Showing {first} to {last} of {totalRecords}" [totalRecords]="totalRecords"
        (onPageChange)="paginate($event)" #paginator></p-paginator>
        </div>

    </div>
</div>
