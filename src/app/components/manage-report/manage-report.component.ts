import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { EntryExit, EntryExitType, Report, ReportType, ReportTypes } from 'src/app/model/Report';
import { ManageUsersService } from '../manage-users/manage-users.service';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { Subscription, map } from 'rxjs';
import { ManageLocationsService } from '../manage-locations/manage-locations.service';
import { TokenService } from 'src/app/security/token.service';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { ManageFleetsService } from '../manage-fleets/manage-fleets.service';
import { AutoCompleteSelectEvent } from 'primeng/autocomplete';
@Component({
    selector: 'app-manage-report',
    templateUrl: './manage-report.component.html',
    styleUrls: ['./manage-report.component.scss'],
    standalone: false
})
export class ManageReportComponent implements OnInit {

  breadcrumbItems: MenuItem[];
  reports:Report[]=[{id:1,name: "Moves By Driver",description:"List all moves done by drivers",type:ReportType.Moves},
                    {id:2,name: "Moves By Spotter",description:"List all moves done by spotters",type:ReportType.Moves},
                    {id:5,name: "Trailer Report by Entry and Exit",description:"List trailer information based on entry and exit", type:ReportType.TrailerReport},                   
                    {id:6,name: "Asset Inventory Report",description:"List all assets",type:ReportType.TrailerReport},
                    {id:8,name:"Trailer History", description:"List all jobs done by a trailer", type:ReportType.TrailerReport}
                  ];
  reportTypeList:ReportTypes[]=[{type:ReportType.Moves, number:2},
                                {type:ReportType.TrailerReport, number:2}];
  selectedReports:Report[]=[];
  searchboxValue:string;
  folderView:boolean = true;
  filterMovesByDriverModal : boolean = false;
  filterMovesBySpotterModal : boolean = false;
  filterEntryExitModal : boolean = false;
  subscription = new Subscription();
  fromDate : any = '';
  toDate: any;
  startDate: any;
  lastUpdated: any;
  clientId: string;
  assignedToUser: string;
  selectedUser: any = [];
  selectedClient: any;
  clientName: string;
  clientList = [];
  isSupervisorOrClient: boolean = false;
  isITOrAdmin: boolean = false;
  query :IpagedQuery;
  firstName: string;
  userQuery: IpagedQuery;
  drivers: any[] = [];
  selectedLocation: any;
  locationList = [];
  locationId: string;
  locationName: string;
  loading: boolean;
  totalRecords: any;
  Types: EntryExit[] =[{code:'Entry',value:EntryExitType.Entry},{code:'Exit',value:EntryExitType.Exit}];
  selectedType: string;
  presentDate: any;
  validDate: boolean = true;
  sixMonthValidDate: boolean = true;
  validClientId:boolean=true;
  validLocationId:boolean=true;
  userRoles: any;
  jobStatusList: string[] = ["Queue", "Open", "In_Transit", "Completed"];
  selectedStatus: string | null = null;
  jobstatus: string;
  filteredStatuses = [
    {name:'All', code: 'ALL'},
    {name:'Queue', code:'QUEUE'},
    {name:'Open', code:'OPEN'},
    {name:'In-Transit', code:'IN_TRANSIT'},
    {name:'Completed', code:'COMPLETED'}
  ];
  filterAssetInventoryReportModal: boolean=false;
  filterTrailerAuditModal: boolean = false;
  selectedLocations= [];
  dropdownStatus: { name: string; code: boolean; }[];
  trailerStatus = [
    {name:'Empty', code: 'EMPTY'},
    {name:'Loaded', code:'FULL'},
    {name:'All', code:'ALL'}
  ];
  selectedTrailerStatus:any=null;
  selectedFleetStatus:any=null;
  currentUser: any;
  istrailerAuditEnabled = false;
  trailerHistoryModal:boolean = false;
  uniqueFleetId: any;
  filteredFleets: any[] = [];
  filterFleetId;
  isFleetSelected:boolean = true;

  isClient: boolean = false;
  
  constructor(private router:Router,
              private loader: AppLoaderService,
              private manageUserService: ManageUsersService,
              private manageClientService: ManageClientsService,
              private errorService: ErrorService,
              private manageLocationsService: ManageLocationsService,
              private tokenService: TokenService,
              private manageFleetsService: ManageFleetsService) {

               }

  ngOnInit(): void {

    this.breadcrumbItems = [];
    this.breadcrumbItems.push({ label: 'Reports'});

    this.query = { isActive: true, size: 50, page: 0 };
    this.userQuery = { isActive: true, size: 1000, page: 0 };

    this.currentUser = this.tokenService.getUserClientId();
    this.clientId = this.currentUser;
    if(this.currentUser){
        this.getLoggedInClientList(this.userQuery, this.currentUser);
    }else{
      this.reports.push({id:7,name: "Trailer Audit Report",description:"List all trailer audit",type:ReportType.TrailerReport});
      this.reportTypeList.map((x:any)=>{
        if(x.type == ReportType.TrailerReport){
          x.number = x.number+1;
        }
      });
    }
    this.selectedReports = [...this.reports];
    

    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${yyyy}-${mm}-${dd}`;
    this.presentDate = formattedDate;
    this.toDate = formattedDate;
    
    const fourteenDaysBefore = new Date(today);
    fourteenDaysBefore.setDate(today.getDate() - 14);
    const yyyy2 = fourteenDaysBefore.getFullYear();
    const mm2 = String(fourteenDaysBefore.getMonth() + 1).padStart(2, '0');
    const dd2 = String(fourteenDaysBefore.getDate()).padStart(2, '0');
    const formattedDate2 = `${yyyy2}-${mm2}-${dd2}`;
    this.startDate = formattedDate2;
    
    this.userRoles = this.tokenService.getUserRoles();
    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_CLIENT || role === APP_CONSTANTS.USER_ROLES.ROLE_SUPERVISOR)) {        
        this.isSupervisorOrClient = true;
    }
    if(this.isSupervisorOrClient == true) {
      this.getClientList();
      this.getAssetInvClientList();
    }

    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_CLIENT)){
        this.isClient = true;
    }

    this.isITOrAdmin = this.userRoles.some(
      role => role === APP_CONSTANTS.USER_ROLES.ROLE_IT || role === APP_CONSTANTS.USER_ROLES.ROLE_ADMIN
    );

    this.dropdownStatus = [
      {name:"Active",code:true},
      {name:"Inactive",code:false},
    ]

  }

  updateReports(reportType:ReportType) {

    this.selectedReports = [];
    for(let report of this.reports) {
      if(reportType === report.type) {

        this.selectedReports.push(report);
      }
    }
  }

  search() {
    
    this.selectedReports = [];
    for(let report of this.reports) {
      if(report.type.toString().toLowerCase().includes(this.searchboxValue.toLowerCase()) || report.name.toString().toLowerCase().includes(this.searchboxValue.toLowerCase())) {

        this.selectedReports.push(report);
      }
    }
  }

  toggleView(view:string) {

    if(view === 'folder') {
      this.folderView = true;
    }
    else {
      this.folderView = false;
    }
  }

  clearFilters() {
    this.selectedReports = [];
    this.selectedReports = [...this.reports];
  }

  routeToManageReport(id:number) {
    console.log(id);
    
    switch(id) {

      case 1:
        this.filterMovesByDriverModal = true;
        this.viewUsers();
        
        if (this.isClient) {
          this.selectedUser = ["ALL"];
        }
        break;

      case 2:
        this.filterMovesBySpotterModal = true;
        this.viewUsers()
        if (this.isClient) {
          this.selectedUser = ["ALL"];
        }
        break;

      case 3:
        break;
        
      case 4:
        break;
        
      case 5:
        this.filterEntryExitModal = true;
        break; 
      case 6:
        this.filterAssetInventoryReportModal = true;
        break; 
      case 7:
        this.filterTrailerAuditModal = true;
        this.selectedTrailerStatus = "ALL";
        if(this.isSupervisorOrClient){
          this.locationList.unshift({
            locationId: 'ALL',
            locationName: 'All Locations'
          });
        }
        break;
      case 8:
        this.trailerHistoryModal = true;
        break;
    }

  }

  filterByClient(event: AutoCompleteSelectEvent) {
    this.clientId = event.value.clientId;
    this.viewUsers();
  }

  filterByLocation(event: AutoCompleteSelectEvent) {
    this.locationId = event.value.locationId;
  }

  filterByType(event: AutoCompleteSelectEvent) {
     this.selectedType = event.value.value;
  }

  filterByUser(event) {
    const selected = event.value;

    if (selected.length > 1) {
      if(selected[selected.length - 1] == "ALL"){
          this.selectedUser = [selected[selected.length - 1]];
      }else{
        if (selected.includes('ALL')){
          this.selectedUser = selected.filter(loc => loc !== 'ALL');
        }else{
          this.selectedUser = selected;
        }
      }
    }else{
        this.selectedUser = selected;
    }
  }

  filterByStatus(event: AutoCompleteSelectEvent) {
    this.jobstatus = event.value;
  }

  filterFleetsByStatus(event) {
    this.selectedFleetStatus = event.value;
  }

  selectTrailerStatus(event){
    this.selectedTrailerStatus = event.value;
  }

  filterByLocations(event) {
   
    const selected = event.value;

    if (selected.length > 1) {
      if(selected[selected.length - 1] == "ALL"){
          this.selectedLocations = [selected[selected.length - 1]];
      }else{
        if (selected.includes('ALL')){
          this.selectedLocations = selected.filter(loc => loc !== 'ALL');
        }else{
          this.selectedLocations = selected;
        }
      }
    }else{
        this.selectedLocations = selected;
    }

  }

  getLoggedInClientList(query,currentUser) {
    this.loader.show()
      this.manageClientService.viewClients(query, null, currentUser).subscribe(response => {
        // this.clientList = response.list;

          if (!this.isITOrAdmin) {
            this.istrailerAuditEnabled = response.list[0].trailerAudit;
          }
          if(this.istrailerAuditEnabled){
            this.reports.push({id:7,name: "Trailer Audit Report",description:"List all trailer audit",type:ReportType.TrailerReport});
            this.selectedReports = [...this.reports];
            this.reportTypeList.map((x:any)=>{
              if(x.type == ReportType.TrailerReport){
                x.number = x.number+1;
              }
            });
          }
        
        this.loader.hide();
      }, (error) => {
        this.loader.hide();
        this.errorService.handleError(error, true);
      })
  }

  getClientList(event?) {
    this.validClientId = true;  
    if(event) {
      this.clientName = event.query;
      this.assignedToUser = '';
      this.selectedUser = [];
      this.locationId = '';
      this.selectedLocation = '';
      this.uniqueFleetId = "";
      this.filterFleetId = "";
    }
    this.loader.show();
    this.manageClientService.viewClients(this.query, this.clientName).subscribe(response => {
       if(this.isITOrAdmin && this.filterTrailerAuditModal){
        this.clientList = response.list.filter((x:any) => { return x.trailerAudit == true });
      }else{
        this.clientList = response.list;
      }
      this.loader.hide();
      if(this.isSupervisorOrClient)
      {
        this.clientId = this.clientList[0].clientId;
        this.selectedClient = this.clientList[0];
        this.viewUsers();
        this.loadLocations({value: {clientId: this.clientId}});
      }
     
      
    }, (error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }

  getAssetInvClientList(event?) {
    if(event) {
      this.clientName = event.query;
      this.selectedLocations = []
      this.locationList =[];
    }
    this.loader.show();
    this.manageClientService.viewClients(this.query, this.clientName).subscribe(response => {
      this.clientList = response.list;
      this.loader.hide();
      if(this.isSupervisorOrClient)
      {
        this.clientId = this.clientList[0].clientId;
        this.selectedClient = this.clientList[0];
        this.loadLocations({ value: {clientId: this.clientId} });
      }
      if (this.isITOrAdmin && this.filterAssetInventoryReportModal) {
        this.clientList.unshift({
          clientId: 'ALL',
          clientName: 'All Clients'
        });
      }
    }, (error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }

  getClientLocations(event?) {
    if(this.clientId){
      this.locationName = event.query;
      this.loading = true;
      this.subscription.add(
          this.manageLocationsService.viewLocations(this.query,this.clientId,null,this.locationName).subscribe(response=>{
              this.locationList = response.list;
              this.totalRecords = response.totalElements;
              this.loading = false;
          },(error) => {
              this.loader.hide();
              this.errorService.handleError(error, true);
          })
      )
    }
    else{
      this.validClientId = false; 
    }
   
}

loadLocations(event?) {
    this.locationList =[];
    this.clientId = event.value.clientId;
  if (this.clientId!="ALL") {  
    this.loading = true;
    this.subscription.add(
      this.manageLocationsService.viewLocations(this.query, this.clientId, null, '').subscribe(
        response => {
          this.locationList = response.list || [];
          console.log("loc", this.locationList);
          
          
          if (this.filterTrailerAuditModal) {
            this.locationList.unshift({
              locationId: 'ALL',
              locationName: 'All Locations'
            });
          }
          this.totalRecords = response.totalElements;
          this.loading = false;
        },
        error => {
          this.loading = false;
          this.errorService.handleError(error, true);
        }
      )
    );
  }
}
  clearClientFilter($event) {
     this.clientId = '';
     this.selectedClient = '';
     this.assignedToUser = '';
     this.selectedUser = [];
     this.locationId = '';
     this.selectedLocation = '';
     this.selectedLocations = [];
     this.locationList = [];
  }

   viewUsers(event?) {
    
         this.firstName = event?.query;
          this.loader.show();
          this.subscription.add(
            this.manageUserService.viewUsers(this.userQuery, this.clientId, this.firstName).
              pipe(
                map(res => {
                  let users = [];
                  for (let user of res.list) {
                    let obj = {
                      ...user,
                      fullName: user.firstName + " " + user.lastName
                    };
                    users.push(obj);
                  }
                  return users
                })
              ).subscribe(users => {
                console.log("users", users);
                
                let usersList = [];
                users.map(user => {
                  for (let role of user["roles"]) {
                    
                    if (((role.roleName === "DRIVER" || role.roleName === "SUPERVISOR") && this.filterMovesByDriverModal === true) 
                      || ((role.roleName === "SPOTTER" || role.roleName === "SUPERVISOR") && this.filterMovesBySpotterModal === true)) {
                      usersList.push(user);
                    }
                  }
                })
                this.drivers = usersList;
                this.drivers.unshift({
                  userId: 'ALL',
                  fullName: 'All'
                });
                if(this.isClient){
                  this.selectedUser = ["ALL"];
                }
                
                this.loader.hide();
              }, (error) => {
                this.loader.hide();
                this.errorService.handleError(error, true);
              })
          )
        }

  onClientSelect(event) {
    this.clientId = event.value.clientId;
  }

  clearUserFilter($event) {
    this.assignedToUser = '';
    this.selectedUser = [];
  }

  clearLocationFilter(event) {
    this.locationId = '';
    this.selectedLocation = '';
  }

  clearStatusFilter(event) {
    this.selectedStatus = '';
    this.jobstatus='';
  }

  reDirectToMovesByDriver() {
    if(this.fromDate != '' && this.toDate != '') {   

      const from = new Date(this.fromDate);
      const to = new Date(this.toDate);

      const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());

      if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
        this.sixMonthValidDate = false;
        return;
      }
      this.router.navigate(['/main/manage-report-moves-by-driver'], { queryParams: {clientId:this.clientId,fromDate:this.fromDate,toDate:this.toDate,jobstatus:this.selectedStatus, userIds:this.selectedUser?.join(',')}});
    } else {
      this.validDate = false;
    }
  }

  reDirectToMovesBySpotter() {
    if(this.fromDate != '' && this.toDate != '') { 
      const from = new Date(this.fromDate);
      const to = new Date(this.toDate);

      const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());

      if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
        this.sixMonthValidDate = false;
        return;
      }
      this.router.navigate(['/main/manage-report-moves-by-spotter'], { queryParams: {clientId:this.clientId,fromDate:this.fromDate,toDate:this.toDate,jobstatus:this.jobstatus, userIds:this.selectedUser?.join(',')}});
    } else {
      this.validDate = false;
    }
  }

  reDirectToEntryExitReport() {
    if(this.fromDate != '' && this.toDate != '') { 
      const from = new Date(this.fromDate);
      const to = new Date(this.toDate);

      const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());

      if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
        this.sixMonthValidDate = false;
        return;
      }
      this.router.navigate(['/main/manage-report-trailer-report-entryexit'], { queryParams: {clientId:this.clientId,locationId:this.locationId,fromDate:this.fromDate,toDate:this.toDate,type:this.selectedType}});
    } else {
      this.validDate = false;
    } 
  }

  getJobStatusList(event?) {
    const query = event.query.toLowerCase();
    // this.filteredStatuses = this.jobStatusList.filter(status =>
    //   status.toLowerCase().includes(query)
    // );
  }

  reDirectToAssetInventoryReport() {
    //console.log("reDirectToAssetInventoryReport: this.selectedLocations: "+this.selectedLocations)
     if (this.isITOrAdmin && !this.clientId) {
        this.validClientId = false; 
        return;
      }
      if(this.selectedFleetStatus==null){
        this.selectedFleetStatus=true;
      }
      if(this.selectedTrailerStatus==null){
        this.selectedTrailerStatus="ALL";
      }
      this.router.navigate(['/main/manage-report-asset-inventory'], { queryParams: {clientId:this.clientId,locationIds:this.selectedLocations?.join(','),
      fleetStatus:this.selectedFleetStatus,trailerStatus:this.selectedTrailerStatus}});
  }

  reDirectToTrailerAuditReport() {
    //console.log("reDirectToAssetInventoryReport: this.selectedLocations: "+this.selectedLocations)
     if (this.isITOrAdmin && !this.clientId) {
        this.validClientId = false; 
        return;
      }
      if (this.selectedLocations.length == 0) {
        this.validLocationId = false; 
        return;
      }
      if(this.selectedTrailerStatus==null){
        this.selectedTrailerStatus="ALL";
      }
      this.router.navigate(['/main/manage-report-trailer-audit'], { queryParams: {clientId:this.clientId,locationIds:this.selectedLocations?.join(','),
      trailerStatus:this.selectedTrailerStatus}});
  }

  reDirectToTrailerHistoryReport() {

    if(this.startDate != '' && this.toDate != '') { 
      const from = new Date(this.startDate);
      const to = new Date(this.toDate);

      const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());

      if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
        this.sixMonthValidDate = false;
         return;
      }
    }
    if(this.uniqueFleetId == null){
        this.isFleetSelected = false;
    }
    else{
      this.router.navigate(['/main/manage-report-trailer-history'], {queryParams: {fleetId: this.uniqueFleetId,fromDate:this.startDate,toDate:this.toDate,clientId:this.clientId}});
    }
  }

  cancel() {

    if(this.isSupervisorOrClient == false) {
     
      this.selectedClient = '';
      this.clientId = '';
      this.locationList = [];
    }
    this.assignedToUser = '';
    this.selectedUser = [];
    this.fromDate = '';
    this.toDate = this.presentDate;
    this.selectedLocation = '';
    this.locationId = '';
    this.selectedType = '';

    this.filterMovesByDriverModal = false;
    this.filterMovesBySpotterModal = false;
    this.filterEntryExitModal = false;
    this.validDate = true;
    this.sixMonthValidDate = true;
    this.jobstatus="";
    this.selectedStatus="";
    this.filterAssetInventoryReportModal = false;
    this.selectedLocations = [];

    this.validClientId = true;
    this.validLocationId = true;

    this.filterTrailerAuditModal = false;


    this.filterFleetId = null;
    this.trailerHistoryModal = false;

  }

  onUnitNumberChange(event){
     
     this.uniqueFleetId = "";
  }

  onFleetSelect(event) {
    
    this.isFleetSelected = true;
    this.uniqueFleetId = event.value.fleetId ? event.value.fleetId : null;
  }


  filterFleets(event) {
   
      this.manageFleetsService.viewFleets(this.query, this.clientId, event.query)
        .pipe(
          map(fleets => {
            let fleetsArray = [];
            for (let fleet of fleets.list) {
              let obj = {
                ...fleet,
                fleetAndHotTrailer: this.checkIfHotTrailer(fleet)
              };
              fleetsArray.push(obj);
            }
            return {
              list: fleetsArray
            }

          })
        ).subscribe(response => {
          this.filteredFleets = response.list;
          this.loader.hide();
        }, (error) => {
          this.loader.hide();
          this.errorService.handleError(error, true);
        });

  }

  clearUniqueId(event) {
     this.uniqueFleetId = null;
  }

  checkIfHotTrailer(fleet) {
    if (fleet.isHotTrailer) {
      return `${fleet.unitNumber} - (Hot Trailer)`
    } else {
      return `${fleet.unitNumber}`
    }
  }
  
  
}
