import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { IpagedQuery } from 'src/app/model/IpagedQuery';

@Injectable({
  providedIn: 'root'
})
export class ManageReportService {

  constructor(private http: HttpClient) { }

  viewMoves(query:IpagedQuery, status: any, role?:string, clientId?:any, fromDate?:any, toDate?:any, assignedTo?:string, jobTrailerStatus?: string, 
    currentLocation?: string, bucket?: string, usersIds?:string[]): Observable<any> {

    var params = new HttpParams({
      fromObject:{
        isActive:query.isActive.valueOf(),
        page:query.page.toString(),
        size:query.size.toString(),
        
      }
    })

    if(status){
      params = params.append("status",status.toString())
    }

    if(fromDate && toDate){
      params = params.append("fromDate",fromDate.toString())
      params = params.append("toDate",toDate.toString())
    }

    if(clientId){
      params = params.append("pickupLocation.client.uuid",clientId.toString())
    }

    if(currentLocation) {
      params = params.append("fleet.spot.location.uuid",currentLocation)
    }
    
    if(assignedTo)
    {
      params = params.append("assignedTo.uuid",assignedTo)
    }

    if(usersIds)
    {
      params = params.append("userIds", usersIds.join(","));
    }

    // if(role)
    // {
    //   params =params.append("assignedTo.roles.roleName",role)
    // }

    if(role)
    {
      params =params.append("roleNames",role)
    }
    
    if(jobTrailerStatus)
    {
      params = params.append("fleetStatus",jobTrailerStatus)
    }

    if(bucket){
      params = params.append("bucket",bucket)
    }

    params = params.append("sort",'createdDate,desc')
    
    return this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/jobs`,{params});

  }

}
