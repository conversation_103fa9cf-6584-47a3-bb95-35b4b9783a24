:host ::ng-deep {
    
    .p-toolbar{
    background-color: #f5f5f5 !important;
    border: none !important;
    }

    .flex-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        height: fit-content;  
    }

    // .flex-container > div {
    //   flex: 0 0 48%; /* 2 cards per row with some gap */
    //   box-sizing: border-box;
    // }

    .report-types {
      margin-top: 10px;
      margin-bottom: 10px;       
      border-radius: 12px; 
      font-size: 15px;
      padding: 7px 15px 7px 15px; 
      display: flex;
      justify-content: space-between; 
    }

    .report-types-heading {
      margin: 20px 0px 20px 0px;    
      font-size: 18px;
      justify-content: space-between;
      display: flex;
    }

    .report-types:hover {
      background-color: rgb(240, 237, 235);
      cursor: pointer;
    }

    .report-footer {
      text-align: right; 
      position: absolute; 
      bottom: 1rem;
      right: 1rem;
    }

    .custom-card2 {
   //   width: 500px !important;
    
      min-width: 280px !important;
      height: 800px !important;
      margin-top: 6px;
      margin-bottom: 19px;
      margin-right: 15px;
    }

    @media (max-width: 991.98px) {
        .flex-container {
          flex-direction: column;
        }
      }
     
    .text-green{
       color: #0C7B00;
    }

    .bg-green{
       background-color: #0C7B00;
    }

    .text-xl {
      font-size: 1.7rem !important;
    }

    .text-normal {
      font-size: 1rem !important;
    }

    .text-l {
      font-size:1.5rem !important;
    }

    .text-small {
      font-size: 0.8rem !important;
    }

    .icon{
      color:black;
      background-color:rgb(240, 237, 235);;
      border-radius: 12px;
      padding: 2px 2px 2px 2px;
    }

    .bg-blue{
      background-color: #2F468C;
    }

    .text-brown{
      color: #65340D;
    }

    .bg-brown{
      background-color: #65340D;
    }

    .bg-grey{
      background-color: #898989;
    }

    .text-grey{
      color: #898989;
    }

    .bg-orange{
      background-color: #FFAA00;
    }

    .text-orange{
      color: #FFAA00;
    }

    .text-cyan{
      color: #04A595;
    }

   .bg-cyan{
      background-color: #04A595;
   }

   .text-purple{
     color: #810BC5;
   }

   .bg-purple{
     background-color: #810BC5;
   }

   .custom-card{
     
      height: 120px !important;
      position: relative;
     // width:35rem !important;
      margin-right: 1rem;
      cursor: pointer;
      
   }

   .clear-button {

    color: black; 
    background-color: #56ffb5;
    border: 1px solid #56ffb5
   }

   .clear-button:hover {
    background-color:#71839d;
    border: 1px solid #71839d;
   }

   .toggle-button {
    background-color: #56ffb5;
    border: 1px solid #56ffb5;
    margin-right: 1px;

   }

   .toggle-button:hover {
    background-color: #71839d;
    border: 1px solid #71839d;
   }

   .toggle-button .pi {
    color:black;
   }

   .toggle-button:hover .pi {
    color:white;
   }

   .text-danger{
    color: red;
}

  //  @media (min-width: 1200px) {
  //   .custom-card {
  //       height: 250px; 
  //   }
  //  }
   
}