<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>

    <div class="col-12">
        <div class="card" style="min-height: 600px;">
            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <ng-template #start>
                    <div class="my-2">
                        <h5 class="m-0">Reports</h5>
                    </div>
                </ng-template>
                <ng-template #end>
                    <p-button class="mr-2" icon="pi pi-bars" aria-label="List" (onClick)="toggleView('file')" />
                    <p-button icon="pi pi-table" aria-label="Grid" (onClick)="toggleView('folder')" />
                </ng-template>
            </p-toolbar>
            <div style="display: flex; flex-direction: row;">
                <div class="card custom-card2">
                    <div style="margin-bottom: 20px;">
                        <p-fluid>
                            <p-iconfield iconPosition="left" class="ml-auto">
                                <p-inputicon>
                                    <i class="pi pi-search"></i>
                                </p-inputicon>
                                <input pInputText type="text" placeholder="Search Report" (input)="search()" [(ngModel)]="searchboxValue" />
                            </p-iconfield>
                        </p-fluid>
                    </div>
                    <hr />
                    <div>

                        <div class="report-types-heading">
                            <div>Report Types</div>
                            <div>
                                <button pButton icon="pi pi-filter-slash" class="p-button-rounded clear-button"
                                    (click)="clearFilters()"></button>
                            </div>
                        </div>

                        <div *ngFor="let reportType of reportTypeList" class="report-types"
                            (click)="updateReports(reportType.type)">
                            <div>
                                {{reportType.type}}
                            </div>
                            <div>
                                {{reportType.number}}
                            </div>
                        </div>

                    </div>
                </div>
                <div *ngIf="folderView" class="flex-container" style="width: 100%;">

                    <div *ngFor="let report of selectedReports" style="width: 30%;">
                        <div class="card custom-card" (click)="routeToManageReport(report.id)">
                            <div style="text-align: left;">
                                <span class="block font-medium mb-2 text-normal">{{report.name}}</span>
                            </div>
                            <div style="text-align: left;">
                                <span class="block font-medium mb-3 text-small">{{report.description}}</span>
                            </div>
                            <div class="report-footer">
                                <span class="font-medium text-normal icon">{{report.type}}</span>
                            </div>
                        </div>
                    </div>

                </div>
                <div *ngIf="!folderView">
                    <p-table showGridlines #dt [value]="selectedReports" [loading]="loading"
                        styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                        [rowHover]="true" dataKey="id">

                        <ng-template #header>
                            <tr>
                                <th pSortableColumn="name" style="width: 30rem;">Name</th>
                                <th pSortableColumn="description" style="width: 80rem;">Description</th>
                                <th pSortableColumn="type" style="width: 30rem;">Type</th>
                            </tr>
                        </ng-template>
                        <ng-template #body let-report>
                            <tr (click)="routeToManageReport(report.id)" style="cursor: pointer;">
                                <td>
                                    {{report.name}}
                                </td>
                                <td>
                                    {{report.description}}
                                </td>
                                <td>
                                    {{report.type}}
                                </td>
                            </tr>
                        </ng-template>

                    </p-table>
                </div>
            </div>
        </div>
    </div>
</div>
<p-dialog [(visible)]="filterMovesByDriverModal" [style]="{width: '450px'}" header="Select Filter"
    [modal]="true" (onHide)="cancel()">
    <p-fluid>
        <div style="height: 285px;">
            <p-autocomplete placeholder="Filter By Client" [suggestions]="clientList"
                [(ngModel)]="selectedClient" (completeMethod)="getClientList($event)" [dropdown]="true"
                (onSelect)="filterByClient($event)" (onClear)="clearClientFilter($event)" class="mrgr-10"
                field="clientName">
                <ng-template let-client pTemplate="item">
                    <div>{{client.clientName}}</div>
                </ng-template>
            </p-autocomplete>
            <!-- <p-autocomplete placeholder="Filter By User" [suggestions]="drivers" dataKey="userId" [(ngModel)]="selectedUser"
                (completeMethod)="viewUsers($event)" [dropdown]="true" (onSelect)="filterByUser($event)"
                (onClear)="clearUserFilter($event)" class="mrgr-10" field="fullName" appendTo="body">
                <ng-template let-user pTemplate="item">
                    <div>{{user.fullName}}</div>
                </ng-template>
            </p-autocomplete> -->
            <p-multiSelect [options]="drivers" [(ngModel)]="selectedUser" appendTo="body" placeholder="Filter By User"
                optionLabel="fullName" optionValue="userId" (onChange)="filterByUser($event)" class="mrgb-10" [filter]="true"
                [disabled]="isClient">
            </p-multiSelect>
            <!-- <p-autocomplete placeholder="Filter By Status" [(ngModel)]="selectedStatus" [suggestions]="filteredStatuses"
                (completeMethod)="getJobStatusList($event)" [dropdown]="true" (onSelect)="filterByStatus($event)"
                (onClear)="clearStatusFilter($event)" class="mrgr-10" appendTo="body">
                <ng-template let-item pTemplate="item">
                    <div>{{ item }}</div>
                </ng-template>
            </p-autocomplete> -->
            <p-multiSelect [options]="filteredStatuses" [(ngModel)]="selectedStatus" appendTo="body" placeholder="Filter By Status"
                optionLabel="name" optionValue="code" class="mrgb-10" [filter]="true">
            </p-multiSelect>
    
    
            <div style="display: flex;flex-direction: row; justify-content: space-between;">
                <span class="mr-2 font-medium">
                    *From Date :
                    <input pInputText type="date" [(ngModel)]="fromDate" name="fromDate" placeholder="Select Date" />
                </span>
                <span class="ml-2 font-medium">
                    To Date :
                    <input pInputText type="date" [(ngModel)]="toDate" name="toDate" placeholder="Select Date" />
                </span>
            </div>
            <span class="text-danger" *ngIf="(fromDate =='' || toDate == '' )&&(validDate == false)">
                * Please select From Date and To Date
            </span>
            <span class="text-danger" *ngIf="sixMonthValidDate === false">
                * Cannot export more than Six months. Please change the dates.
            </span>
        </div>
    
    </p-fluid>
    
    <ng-template #footer>
        <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text" (click)="cancel()"
            type="button"></button>
        <button pButton pRipple label="Apply" icon="pi pi-check" class="p-button-text"
            (click)="reDirectToMovesByDriver()"></button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="filterMovesBySpotterModal" [style]="{width: '450px'}" header="Select Filter"
    [modal]="true" (onHide)="cancel()">
    <p-fluid>
        <div style="height: 285px;">
            <p-autocomplete placeholder="Filter By Client" [suggestions]="clientList" dataKey="clientId"
                [(ngModel)]="selectedClient" (completeMethod)="getClientList($event)" [dropdown]="true"
                (onSelect)="filterByClient($event)" (onClear)="clearClientFilter($event)" class="mrgr-10"
                field="clientName">
                <ng-template let-client pTemplate="item">
                    <div>{{client.clientName}}</div>
                </ng-template>
            </p-autocomplete>
            <!-- <p-autocomplete placeholder="Filter By User" [suggestions]="drivers" dataKey="userId" [(ngModel)]="selectedUser"
                (completeMethod)="viewUsers($event)" [dropdown]="true" (onSelect)="filterByUser($event)"
                (onClear)="clearUserFilter($event)" class="mrgr-10" field="fullName" appendTo="body">
                <ng-template let-user pTemplate="item">
                    <div>{{user.fullName}}</div>
                </ng-template>
            </p-autocomplete> -->
            <p-multiSelect [options]="drivers" [(ngModel)]="selectedUser" appendTo="body" placeholder="Filter By User"
                optionLabel="fullName" optionValue="userId" (onChange)="filterByUser($event)" class="mrgb-10" [filter]="true"
                [disabled]="isClient">
            </p-multiSelect>
            <!-- <p-autocomplete placeholder="Filter By Status" [(ngModel)]="selectedStatus" [suggestions]="filteredStatuses"
                (completeMethod)="getJobStatusList($event)" [dropdown]="true" (onSelect)="filterByStatus($event)"
                (onClear)="clearStatusFilter($event)" class="mrgr-10" appendTo="body">
                <ng-template let-item pTemplate="item">
                    <div>{{ item }}</div>
                </ng-template>
            </p-autocomplete> -->   
            <p-multiSelect [options]="filteredStatuses" [(ngModel)]="selectedStatus" appendTo="body" placeholder="Filter By Status"
                optionLabel="name" optionValue="code" class="mrgb-10" [filter]="true">
            </p-multiSelect>
  
            <div style="display: flex;flex-direction: row;">
                <span class="mr-2 font-medium">
                    *From Date :
                    <input pInputText type="date" [(ngModel)]="fromDate"
                        name="fromDate" placeholder="Select Date" />
                </span>
    
                <span class="ml-2 font-medium">
                    To Date :
                    <input pInputText type="date" [(ngModel)]="toDate" name="toDate"
                        placeholder="Select Date" />
                </span>
            </div>
            <span class="text-danger" *ngIf="(fromDate =='' || toDate == '' )&&(validDate == false)">* Please select From
                Date and To Date</span>
            <span class="text-danger" *ngIf="sixMonthValidDate === false">
                * Cannot export more than Six months. Please change the dates.
            </span>
        </div>
    </p-fluid>
    <ng-template #footer>
        <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text" (click)="cancel()"
            type="button"></button>
        <button pButton pRipple label="Apply" icon="pi pi-check" class="p-button-text"
            (click)="reDirectToMovesBySpotter()"></button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="filterEntryExitModal" [style]="{width: '450px'}" header="Select Filter"
    [modal]="true" (onHide)="cancel()">
    <p-fluid>
        <div style="height: 285px;">
            <p-autocomplete placeholder="Filter By Client" [suggestions]="clientList" dataKey="clientId"
                [(ngModel)]="selectedClient" (completeMethod)="getClientList($event)" [dropdown]="true"
                (onSelect)="filterByClient($event)" (onClear)="clearClientFilter($event)" class="mrgr-10"
                field="clientName">
                <ng-template let-client pTemplate="item">
                    <div>{{client.clientName}}</div>
                </ng-template>
            </p-autocomplete>
    
            <p-autocomplete placeholder="Filter By Location" [suggestions]="locationList"
                (onSelect)="filterByLocation($event)" dataKey="locationId" [(ngModel)]="selectedLocation"
                (completeMethod)="getClientLocations($event)" [dropdown]="true" (onClear)="clearLocationFilter($event)"
                class="mrgr-10" field="locationName">
                <ng-template let-location pTemplate="item">
                    <div>{{location.locationName}}</div>
                </ng-template>
            </p-autocomplete>
    
            <div style="display: flex;flex-direction: row; ">
                <span class="mr-2 font-medium">*From Date : <input pInputText type="date" [(ngModel)]="fromDate"
                        name="fromDate" placeholder="Select Date" /></span>
                <span class="ml-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toDate" name="toDate"
                        placeholder="Select Date" /></span>
            </div>
            <span class="text-danger" *ngIf="(fromDate =='' || toDate == '' )&&(validDate == false)">
                * Please select From Date and To Date
            </span>
            <span class="text-danger" *ngIf="sixMonthValidDate === false">
                * Cannot export more than Six months. Please change the dates.
            </span>
    
            <div style="margin-top: 20px;">
                <p-select class="mrgr-10 full-width" [showClear]="true" placeholder="Select Type" [options]="Types" (onChange)="filterByType($event)"
                    optionLabel="code" optionValue="value"></p-select>
            </div>
            <span class="text-danger" *ngIf="validClientId === false">
                * Choose a client.
            </span>
        </div>
    </p-fluid>
    <ng-template #footer>
        <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text" (click)="cancel()"
            type="button"></button>
        <button pButton pRipple label="Apply" icon="pi pi-check" class="p-button-text"
            (click)="reDirectToEntryExitReport()"></button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="filterAssetInventoryReportModal" [style]="{width: '450px'}" header="Select Filter"
    [modal]="true" (onHide)="cancel()">
    <p-fluid>
        <div style="height: 310px;">
            <p-autocomplete placeholder="Filter By Client" [suggestions]="clientList" dataKey="clientId"
                [(ngModel)]="selectedClient" (onSelect)="loadLocations($event)"  field="clientName"
                (completeMethod)="getAssetInvClientList($event)" [dropdown]="true" (onClear)="clearClientFilter($event)">
                <ng-template let-client pTemplate="item">
                    <div>{{client.clientName}}</div>
                </ng-template>
            </p-autocomplete>
    
            <p-multiSelect class="mt-2" [options]="locationList" [(ngModel)]="selectedLocations" placeholder="Filter By Locations"
                optionLabel="locationName" optionValue="locationId" (onSelect)="filterByLocations($event)"
                [filter]="true" *ngIf="clientId !== 'ALL'">
            </p-multiSelect>
    
            <p-select class="mt-2 full-width" [options]="dropdownStatus" placeholder="Select Asset Status" optionLabel="name"
                optionValue="code" (onChange)="filterFleetsByStatus($event)" [(ngModel)]="selectedFleetStatus"></p-select>
    
            <p-select class="mt-2 full-width" [showClear]="true" [options]="trailerStatus"
                placeholder="Select Trailer Status" optionLabel="name" optionValue="code"
                (onChange)="selectTrailerStatus($event)" [(ngModel)]="selectedTrailerStatus"></p-select>
            <br>
    
            <span class="text-danger" *ngIf="validClientId === false">
                * Choose a client.
            </span>
        </div>
    </p-fluid>
    <ng-template #footer>

        <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text" (click)="cancel()"
            type="button"></button>
        <button pButton pRipple label="Apply" icon="pi pi-check" class="p-button-text"
            (click)="reDirectToAssetInventoryReport()"></button>
    </ng-template>
</p-dialog>

<!-- TODO: Fix p-fluid and modal height-->
<p-dialog [(visible)]="filterTrailerAuditModal" [style]="{width: '450px', minHeight:'500px'}" header="Select Filter"
    [modal]="true" (onHide)="cancel()">
    <p-fluid>
        <div style="height: 310px;">
            <p-autoComplete placeholder="Filter By Client" [suggestions]="clientList" dataKey="clientId"
                [(ngModel)]="selectedClient" (completeMethod)="getClientList($event)" [dropdown]="true"
                (onSelect)="loadLocations($event)" (onClear)="clearClientFilter($event)" class="mrgr-10"
                field="clientName">
                <ng-template let-client pTemplate="item">
                    <div>{{client.clientName}}</div>
                </ng-template>
            </p-autoComplete>

            <p-multiSelect [options]="locationList" [(ngModel)]="selectedLocations" placeholder="Filter By Locations"
                optionLabel="locationName" optionValue="locationId" (onChange)="filterByLocations($event)"
                class="mrgb-10" [filter]="true">
            </p-multiSelect>

            <p-select class="mrgb-10" [showClear]="true" [options]="trailerStatus" placeholder="Select Trailer Status"
                optionLabel="name" optionValue="code" (onChange)="selectTrailerStatus($event)"
                [(ngModel)]="selectedTrailerStatus"></p-select>
            <br />
            <!-- <div class="mr-2 font-medium">Last Updated : <input pInputText type="date"
               [(ngModel)]="lastUpdated" name="lastUpdated"
               placeholder="Select Date" /></div> -->

            <br>


            <span class="text-danger" *ngIf="validClientId === false">
                * Choose a client.
            </span>

            <span class="text-danger" *ngIf="validLocationId === false">
                * Choose a Location.
            </span>

        </div>

    </p-fluid>
    <ng-template #footer>

        <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text" (click)="cancel()"
            type="button"></button>
        <button pButton pRipple label="Apply" icon="pi pi-check" class="p-button-text"
        (click)="reDirectToTrailerAuditReport()"></button>
</ng-template>
</p-dialog>

<p-dialog [(visible)]="trailerHistoryModal" [style]="{width: '450px', minHeight:'250px'}" header="Select Filter"
[modal]="true" class="p-fluid" (onHide)="cancel()">

<p-fluid>

    <p-autoComplete placeholder="Filter By Client" [suggestions]="clientList" dataKey="clientId"
    [(ngModel)]="selectedClient" (completeMethod)="getClientList($event)" [dropdown]="true"
    (onSelect)="onClientSelect($event)" (onClear)="clearClientFilter($event)" class="mrgr-10"
    field="clientName" appendTo="body">
    <ng-template let-client pTemplate="item">
        <div>{{client.clientName}}</div>
    </ng-template>
    </p-autoComplete>

        <p-autoComplete placeholder="Filter By Trailer/Unit#" [(ngModel)]="filterFleetId"
        [suggestions]="filteredFleets" (onSelect)="onFleetSelect($event)" dataKey="fleetId"
        (completeMethod)="filterFleets($event)" [dropdown]="true"
        (onClear)="clearUniqueId($event)" class="mrgr-10" field="fleetAndHotTrailer" appendTo="body">
        <ng-template let-fleet pTemplate="item">
            <div>{{fleet.fleetAndHotTrailer}}</div>
        </ng-template>
        </p-autoComplete>

        <div style="display: flex;flex-direction: row; margin-top: 8px;">
            <span class="mx-2 font-medium" >*From Date : <input pInputText type="date"
                [(ngModel)]="startDate" name="fromDate"
                placeholder="Select Date" /></span>        
            <span class="mx-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toDate"
                 name="toDate" placeholder="Select Date" /></span>
        </div>
        <span class="text-danger" *ngIf="(startDate =='' || toDate == '' )">* Please select From Date and To Date</span> 
        <span class="text-danger" *ngIf="sixMonthValidDate === false">
            * Cannot export more than Six months. Please change the dates.
        </span>
        <span class="text-danger" *ngIf="isFleetSelected == false">
            * Select a trailer
        </span>
       
    </p-fluid>
<ng-template pTemplate="footer">
    
    <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text"
        (click)="cancel()" type="button"></button>
        <button pButton pRipple label="Apply" icon="pi pi-check" class="p-button-text"
        (click)="reDirectToTrailerHistoryReport()"></button>
</ng-template>
</p-dialog>