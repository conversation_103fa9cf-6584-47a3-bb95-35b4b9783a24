:host ::ng-deep {
    .p-toolbar{
    background-color: #f5f5f5 !important;
    border: none !important;
    }
    .add-new {
        padding: 8px;
        cursor: pointer;
        color: #007bff;
        border-top: 1px solid #ccc;
        text-align: center;
    }

    .audit-table td, .audit-table thead > tr > th{
        text-align: center !important;
    }

    .p-button.clear_btn_style{
        color: #3e5f76 !important;
        background: #d6ebfe !important;
        border: 1px solid #6366F1 !important;
    }
}

::ng-deep .p-autocomplete-panel {
  display: flex;
  flex-direction: column;
  max-height: 250px; /* or your desired height */
  overflow: hidden;
}

::ng-deep .p-autocomplete-items {
  flex: 1 1 auto;
  overflow-y: auto;
}

::ng-deep .p-autocomplete-footer {
  flex: 0 0 auto;
  border-top: 1px solid #ddd;
  padding: 8px;
  background-color: #fff;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
}


