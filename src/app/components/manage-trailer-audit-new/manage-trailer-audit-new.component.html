<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>
    <div class="col-12">
        <div class="card">
            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <ng-template pTemplate="left">
                    <div class="my-2">
                        <h5 class="m-0">Trailer Audit</h5>
                    </div>
                </ng-template>
            </p-toolbar>
            <!-- <div class="my-2 ms-4">
            <h5 class="m-0">Clients</h5>
        </div> -->
            <!-- <div class="card"> -->
            <!-- </div> -->
            <p-table #dt [value]="spotsArray" showGridlines [globalFilterFields]="['spotName','unitNumber','fleetStatus','notes']"
                [loading]="loading" styleClass="audit-table" [responsive]="true" [columns]="" [rows]="10" [rowHover]="true" dataKey="id">
                <ng-template #caption>
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <span>
                            <p-select class="mr-2" *ngIf="isClientDropdown" [showClear]="true"
                                placeholder="Select Client" [options]="clientList" (onChange)="clientChange($event)"
                                optionLabel="clientName" optionValue="clientId"></p-select>
                            <p-select class="mr-2" [showClear]="true" placeholder="Select Location"
                                [options]="locations" (onChange)="locationChange($event)" optionLabel="locationName"
                                optionValue="locationId"></p-select>
                        </span>
                        <span class="block mt-2 md:mt-0 p-input-icon-left">
                            <button pButton pRipple label="Save & Refresh" class="p-button-success mr-2"
                                (click)="saveAndRefresh()"></button>
                        </span>
                    </div>
                </ng-template>
                <ng-template #header>
                    <tr>
                        <th pSortableColumn="dock">Dock</th>
                        <th pSortableColumn="trailer">Trailer</th>
                        <th pSortableColumn="status">Status</th>
                        <th pSortableColumn="notes">Notes</th>
                        <th pSortableColumn="action">Action</th>
                    </tr>
                </ng-template>
                <ng-template #body let-spot>
                    <tr>
                        <td>{{spot.spotName}}</td>
                        <td>
                            <p-autoComplete appendTo="body" [(ngModel)]="spot.fleetIdIfExist" [suggestions]="fleetList"
                                (onFocus)="onAutoCompleteFocus(spot)" (onSelect)="onChange(spot.spotId, 1)"
                                dataKey="fleetId" field="unitNumber" (completeMethod)="filterTrailers($event)"
                                [dropdown]="true" (onClear)="clearUniqueId($event, spot)">
                                <ng-template let-fleet pTemplate="item">
                                    <div>{{fleet.unitNumber}}</div>
                                </ng-template>
                                <ng-template pTemplate="footer">
                                    <div class="add-new" (click)="addNewTrailerDialog(spot)">
                                        + Add New
                                    </div>
                                </ng-template>
                            </p-autoComplete>
                        </td>
                        <td>
                            <p-select appendTo="body" class="mr-2" [(ngModel)]="spot.fleetStatusIfExist"
                                (onChange)="onChange(spot.spotId, 2)" (click)="onAutoCompleteFocus(spot)"
                                [showClear]="true" placeholder="Select Status" [options]="status" optionLabel="label"
                                optionValue="value"
                                [disabled]="spot.fleetIdIfExist == '' || (spot.fleetIdIfExist != '' && spot.fleetIdIfExist.fleetId == null)"></p-select>
                        </td>
                        <td>
                            <textarea pInputTextarea  rows="3" cols="30"
                                (ngModelChange)="onChange(spot.spotId)" [(ngModel)]="spot.notes"></textarea>
                        </td>

                        <td>
                            <button pButton pRipple label="Clear" class="p-button-primary clear_btn_style mr-2"
                                (click)="clear(spot)"></button>
                        </td>

                    </tr>
                </ng-template>
                <ng-template #emptymessage>
                    <tr style="height: 100px; text-align: center; vertical-align: middle;">
                        <td colspan="5">
                            <span>Select a Location</span>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
            <p-paginator [rows]="10" [showCurrentPageReport]="true"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords}" [totalRecords]="totalRecords"
                (onPageChange)="paginate($event)" #paginator></p-paginator>
        </div>
    </div>
</div>

<p-dialog [(visible)]="addTrailer" [style]="{width: '500px'}" header="Add Trailer Number" [modal]="true"
    (onHide)="hideTrailerModel()">
    <ng-template pTemplate="content">
        <p-fluid>
            <form [formGroup]="trailerForm">
                <div class="field">
                    <label>Trailer Number</label>
                    <input pInputText id="unitNumber" formControlName="unitNumber" type="text" />
                    <span class="text-danger" *ngIf="
                                    trailerForm.controls.unitNumber.touched &&
    								trailerForm.controls.unitNumber.invalid
                                ">Trailer Number is required</span>
                </div>
                <div class="field">
                    <label>Carrier</label>
                    <input pInputText id="carrier" formControlName="carrier" type="text" />
                </div>
                <div class="field">
                    <label>Trailer Type</label>
                    <p-select class="full-width" appendTo="body" formControlName="type" [showClear]="true"
                        placeholder="Select Type" [options]="type" optionLabel="label" optionValue="value"></p-select>
                    <span class="text-danger" *ngIf="
                                    trailerForm.controls.type.touched &&
    								trailerForm.controls.type.invalid
                                ">Type is required</span>
                </div>
            </form>
        </p-fluid>
    </ng-template>

    <ng-template #footer>
        <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text"
            (click)="hideTrailerModel()"></button>
        <button pButton pRipple label="Save" icon="pi pi-check" class="p-button-text" (click)="addNewTrailer()"
            type="button"></button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="warningMessage" [style]="{width: '500px'}" header="Warning" [modal]="true" (onHide)="noEdit()">
    <p-fluid>
        <span>This trailer is currently in-transit. Do you want to override?</span>
    </p-fluid>
    <ng-template #footer>
        <button pButton pRipple label="No" icon="pi pi-times" class="p-button-text" (click)="noEdit()"></button>
        <button pButton pRipple label="Yes" icon="pi pi-check" class="p-button-text" (click)="confirmEdit()"
            type="button"></button>
    </ng-template>

</p-dialog>

<p-dialog [(visible)]="warningEditedDialog" [style]="{width: '500px'}" header="Warning" [modal]="true"
    (onHide)="noSave()">
    <p-fluid>
        <span>Would you like to save the changes?</span>
    </p-fluid>
    <ng-template #footer>
        <button pButton pRipple label="No" icon="pi pi-times" class="p-button-text" (click)="noSave()"></button>
        <button pButton pRipple label="Yes" icon="pi pi-check" class="p-button-text" (click)="confirmSave()"
            type="button"></button>
    </ng-template>
</p-dialog>