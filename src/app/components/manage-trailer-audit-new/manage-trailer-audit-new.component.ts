import { Component, OnInit, ViewChild } from '@angular/core';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { AddClientService } from '../add-client/add-client.service';
import { map, mergeMap } from 'rxjs';
import { ManageLocationsService } from '../manage-locations/manage-locations.service';
import { TokenService } from 'src/app/security/token.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { ManageSpotsService } from '../manage-spots/manage-spots.service';
import { ManageFleetsService } from '../manage-fleets/manage-fleets.service';
import { AddFleetService } from '../add-fleet/add-fleet.service';
import { AppAlertService } from 'src/app/app-alert/service/app-alert.service';
import { ManageTrailerAuditNewService } from './manage-trailer-audit-new.service';
import { <PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators } from '@angular/forms';
import { Paginator } from 'primeng/paginator';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { ManageJobsService } from '../manage-jobs/manage-jobs.service';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';

@Component({
  selector: 'app-manage-trailer-audit-new',
  templateUrl: './manage-trailer-audit-new.component.html',
  providers: [MessageService, ConfirmationService],
  styleUrls: ['./manage-trailer-audit-new.component.scss'],
  standalone: false
})
export class ManageTrailerAuditNewComponent implements OnInit {

  breadcrumbItems: MenuItem[];
  loading: boolean;
  totalRecords: any=0;
  clientId: any;
  client: any;
  locations: any;
  query: { isActive: boolean; size: number; page: number; };
  clientQuery:IpagedQuery;
  selectedLocation: any;
  selectedClient: any;
  spotsArray: any[] = [];
  fleetList: any[] = [];
  data: any[] = [];
  tempData: any;
  trailer: any;
  carrier: any;
  type: any = [{
    label: "Truck",
    value: "TRUCK"
  },
  {
    label: "Trailer",
    value: "TRAILER"
  },
  {
    label: "Container",
    value: "CONTAINER"
  }];
  addTrailer: boolean = false;
  status: any = [{
    label: "Empty",
    value: "EMPTY"
  },
  {
    label: "Loaded",
    value: "FULL"
  }];
  trailerForm: FormGroup;
  isEditedAny: boolean = false;
  clientList: any = [];
  jobsIntransit: any = [];
  warningMessage: boolean = false;
  warningSubmitted: boolean = false;
  intransit: any = null;
  newTrailerSpot: any[] = [];
  warningEditedDialog: boolean = false;
  isClientDropdown: boolean = false;
  submitted: boolean = false;
  selectedInTransit: any[] =[];
  oldIntransit: any = null;
  trailerOrStatus: any;
  isITOrAdmin: boolean = false;


  @ViewChild('paginator', { static: true }) paginator: Paginator;
  
  constructor(
    private addClientService: AddClientService,
    private manageLocationsService: ManageLocationsService,
    private tokenService: TokenService,
    private errorService: ErrorService,
    private manageSpotsServie: ManageSpotsService,
    private manageFleetsService:ManageFleetsService,
    private addFleetService: AddFleetService,
    private alertService: AppAlertService,
    private fb:FormBuilder,
    private manageClientsService:ManageClientsService,
    private trailerAuditService: ManageTrailerAuditNewService,
    private manageJobsService: ManageJobsService,
    private loader:AppLoaderService,
  ) { 

    this.trailerForm = this.fb.group({
                    clientIds: ['',Validators.required],
                    unitNumber: ['',Validators.required],
                    carrier: [''],
                    type:['', Validators.required],
                    fleetStatus:['']
                });
    
  }

  ngOnInit(): void {
    this.breadcrumbItems = [];
    this.breadcrumbItems.push({ label: 'Trailer Audit'});

    let userRoles = this.tokenService.getUserRoles();
    if (userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_ADMIN || role === APP_CONSTANTS.USER_ROLES.ROLE_IT)) {
        this.isClientDropdown = true;
        this.isITOrAdmin = true;
    }else{
      this.isClientDropdown = false;
    }

    this.clientId = this.tokenService.getUserClientId();
    this.query = {isActive:true,size:10,page:0};
    this.clientQuery = {isActive:true,size:1000,page:0};
    if(this.clientId != null){
      this.getClientDetailsAndLocations(this.clientQuery, this.clientId);
    }else{
      
      this.getClientList(this.clientQuery);
    }
    this.getJobs(this.clientQuery);
    // this.getSpots();
    
  }

  paginate(event) {
    if (this.isEditedAny) {
      this.warningEditedDialog = true;
    } 
    this.query.page = event.page;
    if(!this.warningEditedDialog){
      this.getSpots();
    }
  }

  private updateCurrentPage(currentPage: number): void {
    console.log("eneter");
    console.log("loc",this.selectedLocation);
    console.log("client",this.selectedClient);
    
    console.log(currentPage);
    
    this.paginator.changePage(currentPage);
  }

  // private updateCurrentPage(currentPage: number): void {
  //   setTimeout(() => this.paginator.changePage(currentPage));
  // }

  getClientList(clientQuery) {
    this.loading = true;
      this.manageClientsService.viewClients(clientQuery).subscribe(response=>{
        if(this.isITOrAdmin){
          this.clientList = response.list.filter((x:any) => { return x.trailerAudit == true });
        }else{
          this.clientList = response.list;
        }
        this.loading = false;
      },(error) => {
        this.errorService.handleError(error, true);
      })
  }

  getClientDetailsAndLocations(query,clientId) {
       this.addClientService.getClientById(clientId)
          .pipe(
            mergeMap((client)=>{
              return this.manageLocationsService.viewLocations(query,clientId)
              .pipe(
                map((locations)=>{
                  return {
                    client,
                    locations
                  }
                })
              )
            })
          )
          .subscribe(response=>{
            this.client = response.client;
            this.locations = response.locations.list;
            
          },(error) => {
            // this.loader.hide();
            this.errorService.handleError(error, true);
        })
    }

    getSpots(){
      if (!this.clientId || !this.selectedLocation) {
        return;
      }

      console.log('calling:::::::: ');
      this.loader.show();
      this.manageSpotsServie.viewSpots(this.query, this.clientId, this.selectedLocation)
        .pipe(
          map(res => {
            let spotsArray = [];
            for (let spot of res.list) {
              let inTransit = [];
              if(spot.fleet != null){
                  inTransit = this.jobsIntransit.filter((x: any) => { return x.fleet?.fleetId == spot.fleet?.fleetId });
                  
              }
              let obj = {
                ...spot,
                fleetIdIfExist: spot.fleet != null ? { "unitNumber" : spot.fleet.unitNumber, "fleetId": spot.fleet.fleetId} : '',
                fleetStatusIfExist: spot.fleet != null ? spot.fleet.fleetStatus : '',
                isEdited: false,
                isIntransit: inTransit.length > 0 ? true : false,
                isnewTrailerIntransit: false
              }
              spotsArray.push(obj);
            }
            this.totalRecords = res.totalElements;
            return spotsArray;

          })
        )
        .subscribe(spots => {
          this.loader.hide();
          this.spotsArray = spots;

        }, (error) => {
          this.loader.hide();
          this.errorService.handleError(error, true);
        });
    }

    locationChange(event: any){
      this.query.page = 0;
      this.totalRecords = 0;
      
        if(this.isEditedAny){
          this.warningEditedDialog = true;
          
        }else{
          this.spotsArray = [];
        }
      
          this.selectedLocation = event.value;
         this.updateCurrentPage(0);
          console.log('this.selectedLocation:::::::::: ', this.selectedLocation);
          // if(this.selectedLocation != null){
          //   console.log('check:::::::::::::::::');
          //   this.updateCurrentPage(0);
          // }

          if(this.selectedLocation != null && !this.warningEditedDialog){
            console.log('about to call api::::::::: ');
            this.query.page=0;
              this.getSpots();
          }
          
    }

    clientChange(event: any){
        this.query.page = 0;
        this.totalRecords = 0;
        
        if(this.isEditedAny){
          this.warningEditedDialog = true;
          
        }else{
          this.spotsArray = [];
        }
          this.selectedClient = event.value;
          this.selectedLocation = null;
          this.updateCurrentPage(0);

          this.clientId = this.selectedClient;
          
          this.locations = [];
          this.client = [];
          if(this.clientId != null){
            this.getClientDetailsAndLocations(this.clientQuery, this.clientId);
          }       
    }

    saveAndRefresh(edit: boolean = false){
        if(this.spotsArray.length > 0){

            let data = this.spotsArray.filter((x:any)=> { return x.isEdited == true });
            let res: any = [];
            
            data.map((x: any) => {
                res.push({
                  fleetId: x.fleetIdIfExist?.fleetId,
                  trailerStatus: x.fleetStatusIfExist != ''? x.fleetStatusIfExist:null,
                  notes: x.notes,
                  spotId: x.spotId
                });
            });

          this.trailerAuditService.saveTrailerAudit(res)
          .subscribe(res => {
            this.isEditedAny = false;
            
            if(!edit && this.selectedLocation != null){
              this.getSpots();
            }else{
              this.spotsArray = [];
            }
            this.alertService.alertSuccess([`Saved successfully`]);
          }, (error) => {
            this.errorService.handleError(error, true);
          });
        }
    }

    filterTrailers(event) {
      this.manageFleetsService.viewFleets(this.clientQuery, this.clientId, event.query)
        .subscribe(response => {
          this.fleetList = response.list;

        }, (error) => {
          this.errorService.handleError(error, true);
        });
    }

  addNewTrailer() {

    this.trailerForm.patchValue({
      clientIds: [this.clientId],
      fleetStatus: "EMPTY"
    });
    if (this.trailerForm.invalid) {
      this.trailerForm.markAllAsTouched();
    } else {

    
    this.addFleetService.saveFleet(this.trailerForm.value)
      .subscribe(res => { 
        
        if(this.newTrailerSpot.length > 0){
          
          let addedSpot = this.spotsArray.filter((x: any) => { return x.spotId == this.newTrailerSpot[0].spotId });
          
          addedSpot[0].fleetIdIfExist = res;
          addedSpot[0].fleetStatusIfExist = res.fleetStatus;
          addedSpot[0].isEdited = true;
        }
        this.isEditedAny = true;
        this.newTrailerSpot = [];
        this.trailerForm.reset();
        this.alertService.alertSuccess([`New Trailer added successfully`]);
        this.addTrailer = false;
        
      }, (error) => {
        this.errorService.handleError(error, true);
      });
    }
  }

  addNewTrailerDialog(spot: any){
    if(spot.isIntransit){
       this.warningMessage = true;
       this.warningSubmitted = false;
       this.newTrailerSpot = this.spotsArray.filter((x:any) => { return spot.spotId == x.spotId });
    }else{
      this.addTrailer = true;
      this.newTrailerSpot = this.spotsArray.filter((x:any) => { return spot.spotId == x.spotId });
    }
  }

  clearUniqueId(event, spot: any) {
    this.fleetList = null;
    spot.fleetStatusIfExist = null;
    
  }

  onChange(spot: any, trailer: any = 0){
      if(trailer > 0){
          this.isEditedAny = true;
          
          this.spotsArray.map((x:any)=> { 
            if(x.spotId == spot){
              if(trailer == 1){
                  this.trailerChange(x);
                  this.trailerOrStatus = 1;
              }else{
                  this.trailerOrStatus = 2;
                  this.statusChange(x);
              }
              
            }
          });
          
      }else{
        this.isEditedAny = true;
        let spots = this.spotsArray.filter((x: any) => { return x.spotId == spot });
        spots[0].isEdited = true;
      }
      
  }

  trailerChange(spot: any){
      this.selectedInTransit = this.jobsIntransit.filter((y:any) => { return y.fleet?.fleetId == spot.fleetIdIfExist?.fleetId });

      if(spot.fleetIdIfExist != ""){
          spot.fleetStatusIfExist = spot.fleetIdIfExist?.fleetStatus != null ? spot.fleetIdIfExist?.fleetStatus : null;      
      }

      // let selectedInTransit = this.jobsIntransit.filter((y:any) => { return y.fleet?.fleetId == x.fleetIdIfExist?.fleetId });
    this.oldIntransit = spot.isIntransit;
    if (spot.isIntransit || this.selectedInTransit.length > 0) {
      this.warningMessage = true;
      this.warningSubmitted = false;

    } else {
      spot.isEdited = true;
      spot.isIntransit = false;
    }
  }

  statusChange(spot: any){
      if (spot.isIntransit){
        this.warningMessage = true;
        this.warningSubmitted = false;
      }else{
        spot.isEdited = true;
        spot.isIntransit = false;
      }
  }

  onAutoCompleteFocus(spot: any) {
    // if(spot.fleet != null){
      this.intransit = spot;
    // }

  }

  clear(spot: any){
      this.trailerAuditService.clearTrailerAudit(spot.spotId)
      .subscribe(res => {
        // this.getSpots();
        spot.fleetIdIfExist = '';
        spot.fleetStatusIfExist = '';
        spot.notes = '';
        this.alertService.alertSuccess([`Trailer cleared successfully`]);
      }, (error) => {
        this.errorService.handleError(error, true);
      });
  }

  hideTrailerModel(){
    this.addTrailer = false;
    this.newTrailerSpot = [];
    this.trailerForm.reset();
  }

  getJobs(query) {
   this.loader.show();
    this.manageJobsService.viewJobs(query, "IN_TRANSIT")
      .subscribe(response => {
          this.jobsIntransit = response.list;      
          this.loader.hide();
      }, (error) => {
        this.loader.hide();
        this.errorService.handleError(error, true);
      })
  }

  noEdit(){
    
      if(this.warningMessage || !this.warningSubmitted){
        let editSpot = this.spotsArray.filter((x:any) => { return x.spotId == this.intransit.spotId });
        
        
        if(this.intransit.fleet != null){
          if(editSpot.length > 0){
            if(this.trailerOrStatus == 1){
              editSpot[0].fleetIdIfExist = editSpot[0].fleet;
              editSpot[0].fleetStatusIfExist = editSpot[0].fleet?.fleetStatus;
              editSpot[0].isIntransit = this.oldIntransit;
            }else{
              editSpot[0].fleetStatusIfExist = editSpot[0].fleetIdIfExist?.fleetStatus;
            }
          }
        }else{
          if(editSpot.length > 0){
            if(this.trailerOrStatus == 1){
              editSpot[0].fleetIdIfExist = '';
              editSpot[0].fleetStatusIfExist = '';
              editSpot[0].isIntransit = this.oldIntransit;
            }else{
              editSpot[0].fleetStatusIfExist = '';
            }
          }
        }
        this.warningMessage = false;
        this.warningSubmitted = true;  
        this.oldIntransit = null;
        this.selectedInTransit = [];
        this.intransit = null;
        if(this.newTrailerSpot.length > 0){
          this.newTrailerSpot = [];
        }

        }
        let editCheck = this.spotsArray.filter((x:any) => { return x.isEdited == true });
        if(editCheck.length == 0){
          this.isEditedAny = false;
        }else{
          this.isEditedAny = true;
        }
        
  }

  confirmEdit(){
    let editSpot = this.spotsArray.filter((x:any) => { return (x.spotId == this.intransit.spotId) });
    editSpot[0].isEdited = true;
    if(this.selectedInTransit.length > 0){
      editSpot[0].isIntransit = true;
    }else{
      editSpot[0].isIntransit = false;
    }
    this.oldIntransit = null;
    this.selectedInTransit = [];
    this.warningMessage = false;
    this.warningSubmitted = true;
    this.intransit = null;

    if(this.newTrailerSpot.length > 0){
      this.addTrailer = true;
    }
    
  }

  confirmSave(){
    this.saveAndRefresh(true);
    this.warningEditedDialog = false;
    // this.spotsArray = [];
    this.isEditedAny = false;
  }

  noSave() {
    this.warningEditedDialog = false;
    // this.spotsArray = [];
    if (this.selectedLocation != null) {
      
        this.getSpots();
      
    } else {
      this.spotsArray = [];
    }
    this.isEditedAny = false;
  }

}
