import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { IpagedQuery } from 'src/app/model/IpagedQuery';

@Injectable({
  providedIn: 'root'
})
export class NewUserService {

  
  constructor(private http: HttpClient) { }

  getUser(userId:string): Observable<any> {
  
    return  this.http.get(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/newUser/${userId}`);
  }
  

  activateUser(assignObj1:any):Observable<any> {

    return this.http.post(`${APP_CONSTANTS.BASE_API_URL}/${APP_CONSTANTS.VERSION}/users/activate`,assignObj1);
  }
}
