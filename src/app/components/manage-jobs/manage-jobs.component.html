<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>
    <div class="col-12">
        <div class="card">
            <p-toast></p-toast>

            <p-toolbar>
                <div class="w-full">
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <div class="my-2">
                            <h5 class="m-0">Manage Spots</h5>
                        </div>

                        <div>
                            <span class="cooldown-timer last-refresh-time">
                                {{ lastRefreshTime }}
                            </span>
                            <button class="refresh-button mr-4" (click)="refreshData()" [disabled]="isRefreshDisabled"
                                [matTooltip]="isRefreshDisabled ? 'Please wait ' + refreshCounter + 's before refreshing' : 'Refresh data'"
                                mat-icon-button matTooltipPosition="below">
                                <mat-icon>refresh</mat-icon>
                            </button>
                            <button pButton pRipple label="New" icon="pi pi-plus" class="p-button-success mr-2"
                                (click)="routeToAddJob()"></button>
                            <!-- <button pButton pRipple label="Export" icon="pi pi-upload" class="p-button-help" (click)="exportExcel()"></button> -->
                            <button pButton pRipple label="Export"
                                *ngIf="userType !== 'ROLE_SUPERVISOR' && userType !== 'ROLE_ADMIN' && userType === 'ROLE_IT' && userType !== 'ROLE_CLIENT' && userType !== 'ROLE_DRIVER' && userType !== 'ROLE_GUARD' && userType !== 'ROLE_SPOTTER'"
                                class="p-button-raised p-button-help mr-2" (click)="exportPopup()"></button>
                            <!-- <p-splitButton label="Export" [model]="items" (onClick)="exportExcel()"
                                styleClass="p-button-raised p-button-help mr-2 "></p-splitButton> -->
                        </div>
                    </div>


                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center mrgt-30">
                        <span>

                            <p-select *ngIf="userType !== 'ROLE_CLIENT'" [showClear]="true" placeholder="Select Client"
                                [options]="clientList" (onChange)="filterJobsByClient($event)" optionLabel="clientName"
                                optionValue="clientId" class="mrgr-10"></p-select>

                            <p-autocomplete placeholder="Filter By Trailer/Unit#" [(ngModel)]="filterFleetId"
                                [suggestions]="filteredFleets" (onSelect)="onFleetSelect($event)" dataKey="fleetId"
                                (completeMethod)="filterFleets($event)" [dropdown]="true"
                                (onClear)="clearFilter($event)" class="mrgr-10" field="fleetAndHotTrailer">
                                <ng-template let-fleet pTemplate="item">
                                    <div>{{fleet.fleetAndHotTrailer}}</div>
                                </ng-template>
                            </p-autocomplete>

                            <input pInputText type="text" [(ngModel)]="searchNotes" placeholder="Search Notes Or ASN"
                                (input)="searchInNotes()" />

                            <!-- <p-select [showClear]="true" class="ml-2" placeholder="Select Status"
                                (onChange)="filterJobsByStatus($event)" [options]="status" optionLabel="name"
                                optionValue="code"></p-select> -->

                            <!-- <div class="mx-2" style="display: inline;">
                                
                            </div> -->

                        </span>
                        <span class="block mt-2 md:mt-0 p-input-icon-left">
                            <span class="mr-2 font-medium">From Date : <input pInputText type="date"
                                    [(ngModel)]="fromDate" (change)="filterJobsByDate()" name="fromDate"
                                    placeholder="Select Date" /></span>
                            <span class="mx-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toDate"
                                    (change)="filterJobsByDate()" name="toDate" placeholder="Select Date" /></span>
                        </span>
                    </div>
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center mt-2">
                        <span>

                        </span>
                    </div>
                </div>
            </p-toolbar>
            <!-- <div class="mt-4 ml-4">
                <p class="mb-2"><span class="font-medium text-500">Open :</span> {{ totalRecordsOpen }}</p>
                <p class="mb-2"><span class="font-medium text-500">In-Transit :</span> {{ totalRecordsIntransit }} </p>
                <p class="mb-2"><span class="font-medium text-500">Exceptions :</span> {{ totalRecordsException }}</p>
            </div> -->
            <div class="mrgt-20">
                <p-accordion [activeIndex]="accordionActiveIndex">
                    <p-accordionTab header="Exceptions - {{ totalRecordsException }}" *ngIf="exceptionActive">
                        <p-table showGridlines #dt4 [value]="jobsException" [loading]="loadingException"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template #header>
                                <tr>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-popover #op>

                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-select placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDateException"
                                                            [options]="createdDateSort" optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'EXCEPTION')"
                                                            appendTo="body">
                                                        </p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="jobPickupTime">Spot Pickup Time</th>
                                    <th pSortableColumn="jobDropoffTime">Spot Dropoff Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                        <!-- <p-columnFilter type="text" field="jobNumber" display="menu"></p-columnFilter> -->
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                        <!-- <i class="pi pi-search" style="font-size: 1.5rem" (click)="demo($event)"></i> -->
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="status">Status</th>
                                    <th pSortableColumn="exception">Exceptions</th>
                                    <th pSortableColumn="assignedTo" *ngIf="userType != 'ROLE_CLIENT'">
                                        <div style="display: flex;flex-direction: row;">
                                            Assigned To
                                            <div class="flex justify-content-center">
                                                <p-popover #op1>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="drivers" [showClear]="true"
                                                            [(ngModel)]="assignedDriverException" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByAssignedUser($event,op1,'EXCEPTION')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op1.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="createdBy">Created By</th>
                                    <th pSortableColumn="weather">Weather</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job>
                                <tr>
                                    <td>

                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>

                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>
                                        {{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>
                                        {{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>
                                        {{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>

                                    <td style="max-width: 100px; overflow: hidden;word-wrap: break-word;">
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                        <div *ngIf="job.pickupNotes">
                                            <b>{{job.pickupRole}}</b> {{job.pickupNotes}}
                                        </div>
                                        <div *ngIf="job.dropNotes">
                                            <b>{{job.dropRole}}</b> {{job.dropNotes}}
                                        </div>
                                    </td>
                                    <td>
                                        {{job.fleet?.unitNumber}}
                                    </td>
                                    <td>
                                        <p-tag *ngIf="job.status == 'OPEN'" rounded="true" severity="warning"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'IN_TRANSIT'" rounded="true" severity="primary"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'COMPLETED'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'EXCEPTION'" rounded="true" severity="danger"
                                            [value]="job.status"></p-tag>
                                    </td>
                                    <td>
                                        {{ job.trailerException }}
                                    </td>
                                    <td *ngIf="userType != 'ROLE_CLIENT'">
                                        {{job.assignedTo?.firstName}} {{job.assignedTo?.lastName}}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                    <td>
                                        <div *ngIf="job.climate">
                                            {{job.climate}}
                                            <div>
                                                Temp:{{job.temperature}}&nbsp;F
                                            </div>

                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="15">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsException"
                            (onPageChange)="paginateExceptions($event,'EXCEPTION')" #paginator4></p-paginator>
                    </p-accordionTab>
                    <p-accordionTab header="Scheduled (Driver)- {{ totalRecordsDriverScheduled }}">
                        <p-table showGridlines #dt5 [value]="jobsDriverScheduled" [loading]="loadingScheduledDriver"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">
                            <ng-template #header>
                                <tr>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-popover #op>
                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-select placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDate" [options]="createdDateSort"
                                                            optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'QUEUE','BUCKET_DRIVER')"
                                                            appendTo="body">
                                                        </p-select>
                                                    </ng-template>
                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th pSortableColumn="sheduledDateAndTime">Scheduled Date and Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                    <th pSortableColumn="createdBy">
                                        <div style="display: flex;flex-direction: row;">
                                            Created By
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op2>
                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="allUsers" [showClear]="true"
                                                            [(ngModel)]="createdUserDriverQueue" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByCreatedUser($event,op2,'QUEUE','BUCKET_DRIVER')"></p-select>
                                                    </ng-template>
                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op2.toggle($event)"></i>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job let-columns="columns" let-index="rowIndex">
                                <tr>
                                    <td>
                                        <button
                                            *ngIf="(job.status !== 'COMPLETED' && job.status !== 'IN_TRANSIT') && userType !== 'ROLE_CLIENT'"
                                            pButton pRipple icon="pi pi-pencil" pTooltip="Edit"
                                            class="p-button-rounded p-button-help mr-2"
                                            (click)="routeToEdit(job.jobId,'SCHEDULED')"></button>
                                        <button
                                            *ngIf="job.status == 'SCHEDULED' || job.status == 'OPEN' || job.status == 'QUEUE' && userType !== 'ROLE_CLIENT' && hideButtonsIfGuard === false && hideButtonsIfSpotter === false && hideButtonsIfDriver === false"
                                            pButton pRipple icon="pi pi-trash" pTooltip="Delete Job"
                                            class="p-button-rounded p-button-danger mr-2"
                                            (click)="deleteJob(job.jobId, 'SCHEDULED',1)"></button>
                                        <button *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Pickup"
                                            class="p-button-rounded p-button-success mr-2"
                                            (click)="openStatusDialog(job,'Confirm Pickup')"></button>
                                        <button *ngIf="job.status == 'IN_TRANSIT' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Drop"
                                            class="p-button-rounded p-button-warning mr-2"
                                            (click)="openStatusDialog(job,'Confirm Drop')"></button>
                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>
                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>{{job?.scheduleDateTime ? job?.scheduleDateTime : '-'}}
                                    </td>
                                    <td>{{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>{{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>{{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>
                                    <td>
                                        <!-- <span class="p-column-title">Trailer/Container</span> -->
                                        <span (click)="onTrailerSelect(job)" style="cursor: pointer;color:blue;text-decoration: underline;">{{job.fleet?.unitNumber}}</span>
                                    </td>
                                    <td>
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="16">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsDriverScheduled" (onPageChange)="paginateDriverQueue($event,'QUEUE')"
                            #paginator7></p-paginator>
                    </p-accordionTab>
                    <p-accordionTab header="Scheduled (Spotter)- {{ totalRecordsSpotterScheduled }}">
                        <p-table showGridlines #dt5 [value]="jobsSpotterScheduled" [loading]="loadingScheduledDriver"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">
                            <ng-template #header>
                                <tr>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-popover #op>
                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-select placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDate" [options]="createdDateSort"
                                                            optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'QUEUE','BUCKET_DRIVER')"
                                                            appendTo="body">
                                                        </p-select>
                                                    </ng-template>
                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th pSortableColumn="sheduledDateAndTime">Scheduled Date and Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                    <th pSortableColumn="createdBy">
                                        <div style="display: flex;flex-direction: row;">
                                            Created By
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op2>
                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="allUsers" [showClear]="true"
                                                            [(ngModel)]="createdUserDriverQueue" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByCreatedUser($event,op2,'QUEUE','BUCKET_DRIVER')"></p-select>
                                                    </ng-template>
                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op2.toggle($event)"></i>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job let-columns="columns" let-index="rowIndex">
                                <tr>
                                    <td>
                                        <button
                                            *ngIf="(job.status !== 'COMPLETED' && job.status !== 'IN_TRANSIT') && userType !== 'ROLE_CLIENT'"
                                            pButton pRipple icon="pi pi-pencil" pTooltip="Edit"
                                            class="p-button-rounded p-button-help mr-2"
                                            (click)="routeToEdit(job.jobId,'SCHEDULED')"></button>
                                        <button
                                            *ngIf="job.status == 'SCHEDULED' || job.status == 'OPEN' || job.status == 'QUEUE' && userType !== 'ROLE_CLIENT' && hideButtonsIfGuard === false && hideButtonsIfSpotter === false && hideButtonsIfDriver === false"
                                            pButton pRipple icon="pi pi-trash" pTooltip="Delete Job"
                                            class="p-button-rounded p-button-danger mr-2"
                                            (click)="deleteJob(job.jobId, 'SCHEDULED',2)"></button>
                                        <button *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Pickup"
                                            class="p-button-rounded p-button-success mr-2"
                                            (click)="openStatusDialog(job,'Confirm Pickup')"></button>
                                        <button *ngIf="job.status == 'IN_TRANSIT' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Drop"
                                            class="p-button-rounded p-button-warning mr-2"
                                            (click)="openStatusDialog(job,'Confirm Drop')"></button>
                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>
                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>{{job?.scheduleDateTime ? job?.scheduleDateTime : '-'}}
                                    </td>
                                    <td>{{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>{{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>{{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>
                                    <td>
                                        <!-- <span class="p-column-title">Trailer/Container</span> -->
                                        <span (click)="onTrailerSelect(job)" style="cursor: pointer;color:blue;text-decoration: underline;">{{job.fleet?.unitNumber}}</span>
                                    </td>
                                    <td>
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="16">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsSpotterScheduled" (onPageChange)="paginateSpotterQueue($event,'QUEUE')"
                            #paginator8></p-paginator>
                    </p-accordionTab>
                    <p-accordionTab header="Queue (Driver)- {{ totalRecordsDriverQueue }}">
                        <p-table showGridlines #dt5 [value]="jobsDriverQueue" [loading]="loading"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10" reorderableRows="true"
                            (onRowReorder)="onRowReorderFirstTable($event)"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template #header>
                                <tr>
                                    <th *ngIf="filterByClient"></th>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-popover #op>

                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-select placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDate" [options]="createdDateSort"
                                                            optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'QUEUE','BUCKET_DRIVER')"
                                                            appendTo="body">
                                                        </p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="jobPickupTime">Spot Pickup Time</th>
                                    <th pSortableColumn="jobDropoffTime">Spot Dropoff Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                        <!-- <p-columnFilter type="text" field="jobNumber" display="menu"></p-columnFilter> -->
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                        <!-- <i class="pi pi-search" style="font-size: 1.5rem" (click)="demo($event)"></i> -->
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <!-- <th pSortableColumn="pickupSpot">Pickup Parking Spot</th> -->
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="status">Status</th>
                                    <!-- <th pSortableColumn="exception">Exceptions</th> -->
                                    <th pSortableColumn="assignedTo" *ngIf="userType != 'ROLE_CLIENT'">
                                        <div style="display: flex;flex-direction: row;">
                                            Assigned To
                                            <!-- <div class="flex justify-content-center">
                                                <p-popover #op1>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="drivers" [showClear]="true"
                                                            [(ngModel)]="assignedDriver" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByAssignedUser($event,op1,'OPEN')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op1.toggle($event)"></i>
                                            </div> -->
                                        </div>

                                    </th>
                                    <th pSortableColumn="createdBy">
                                        <div style="display: flex;flex-direction: row;">
                                            Created By
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op2>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="allUsers" [showClear]="true"
                                                            [(ngModel)]="createdUserDriverQueue" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByCreatedUser($event,op2,'QUEUE','BUCKET_DRIVER')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op2.toggle($event)"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th pSortableColumn="weather">Weather</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job let-columns="columns" let-index="rowIndex">
                                <tr [pReorderableRow]="index">
                                    <td *ngIf="filterByClient">
                                        <span class="pi pi-bars" pReorderableRowHandle></span>
                                    </td>
                                    <td>
                                        <button
                                            *ngIf="(job.status !== 'COMPLETED' && job.status !== 'IN_TRANSIT') && userType !== 'ROLE_CLIENT'"
                                            pButton pRipple icon="pi pi-pencil" pTooltip="Edit"
                                            class="p-button-rounded p-button-help mr-2"
                                            (click)="routeToEdit(job.jobId,'QUEUE')"></button>
                                        <button
                                            *ngIf="job.status == 'OPEN' || job.status == 'QUEUE' && userType !== 'ROLE_CLIENT' && hideButtonsIfGuard === false && hideButtonsIfSpotter === false && hideButtonsIfDriver === false"
                                            pButton pRipple icon="pi pi-trash" pTooltip="Delete Job"
                                            class="p-button-rounded p-button-danger mr-2"
                                            (click)="deleteJob(job.jobId, 'QUEUE',1)"></button>
                                        <button *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Pickup"
                                            class="p-button-rounded p-button-success mr-2"
                                            (click)="openStatusDialog(job,'Confirm Pickup')"></button>
                                        <button *ngIf="job.status == 'IN_TRANSIT' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Drop"
                                            class="p-button-rounded p-button-warning mr-2"
                                            (click)="openStatusDialog(job,'Confirm Drop')"></button>
                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>
                                        <!-- <button pButton pRipple icon="pi pi-exclamation-circle"
                                            pTooltip="Exception for moves"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="exceptions(job.jobId)"></button> -->

                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>{{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>{{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>{{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>{{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>{{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>

                                    <td style="max-width: 100px; overflow: hidden;word-wrap: break-word;">
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                        <div *ngIf="job.pickupNotes">
                                            <b>{{job.pickupRole}}</b> {{job.pickupNotes}}
                                        </div>
                                        <div *ngIf="job.dropNotes">
                                            <b>{{job.dropRole}}</b> {{job.dropNotes}}
                                        </div>
                                    </td>
                                    <td>
                                        <!-- <span class="p-column-title">Trailer/Container</span> -->
                                        <span (click)="onTrailerSelect(job)" style="cursor: pointer;color:blue;text-decoration: underline;">{{job.fleet?.unitNumber}}</span>
                                    </td>
                                    <td>
                                        <p-tag *ngIf="job.status == 'OPEN'" rounded="true" severity="warning"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'IN_TRANSIT'" rounded="true" severity="primary"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'COMPLETED'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'EXCEPTION'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                    </td>
                                    <!-- <td>
                                        {{ job.trailerException }}
                                    </td> -->
                                    <td *ngIf="userType != 'ROLE_CLIENT'">
                                        {{job.assignedTo?.firstName}} {{job.assignedTo?.lastName}}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                    <td>
                                        <div *ngIf="job.climate">
                                            {{job.climate}}
                                            <div>
                                                Temp:{{job.temperature}}&nbsp;F
                                            </div>

                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="16">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsDriverQueue" (onPageChange)="paginateDriverQueue($event,'QUEUE')"
                            #paginator5></p-paginator>
                    </p-accordionTab>
                    <p-accordionTab header="Queue (Spotter)- {{ totalRecordsSpotterQueue }}">
                        <p-table showGridlines #dt6 [value]="jobsSpotterQueue" [loading]="loading"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10" reorderableRows="true"
                            (onRowReorder)="onRowReorderSecondTable($event)"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template #header>
                                <tr>
                                    <th *ngIf="filterByClient">

                                    </th>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-popover #op>

                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-select placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDate" [options]="createdDateSort"
                                                            optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'QUEUE', 'BUCKET_SPOTTER')"
                                                            appendTo="body">
                                                        </p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="jobPickupTime">Spot Pickup Time</th>
                                    <th pSortableColumn="jobDropoffTime">Spot Dropoff Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                        <!-- <p-columnFilter type="text" field="jobNumber" display="menu"></p-columnFilter> -->
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                        <!-- <i class="pi pi-search" style="font-size: 1.5rem" (click)="demo($event)"></i> -->
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <!-- <th pSortableColumn="pickupSpot">Pickup Parking Spot</th> -->
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="status">Status</th>
                                    <!-- <th pSortableColumn="exception">Exceptions</th> -->
                                    <th pSortableColumn="assignedTo" *ngIf="userType != 'ROLE_CLIENT'">
                                        <div style="display: flex;flex-direction: row;">
                                            Assigned To
                                            <!-- <div class="flex justify-content-center">
                                                <p-popover #op1>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="drivers" [showClear]="true"
                                                            [(ngModel)]="assignedDriver" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByAssignedUser($event,op1,'OPEN')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op1.toggle($event)"></i>
                                            </div> -->
                                        </div>

                                    </th>
                                    <th pSortableColumn="createdBy">
                                        <div style="display: flex;flex-direction: row;">
                                            Created By
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op2>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="allUsers" [showClear]="true"
                                                            [(ngModel)]="createdUserSpotterQueue" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByCreatedUser($event,op2,'QUEUE','BUCKET_SPOTTER')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op2.toggle($event)"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th pSortableColumn="weather">Weather</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job let-columns="columns" let-index="rowIndex">
                                <tr [pReorderableRow]="index">
                                    <td *ngIf="filterByClient">
                                        <span class="pi pi-bars" pReorderableRowHandle></span>
                                    </td>
                                    <td>
                                        <button
                                            *ngIf="(job.status !== 'COMPLETED' && job.status !== 'IN_TRANSIT') && userType !== 'ROLE_CLIENT'"
                                            pButton pRipple icon="pi pi-pencil" pTooltip="Edit"
                                            class="p-button-rounded p-button-help mr-2"
                                            (click)="routeToEdit(job.jobId,'QUEUE')"></button>
                                        <button
                                            *ngIf="job.status == 'OPEN' || job.status == 'QUEUE' && userType !== 'ROLE_CLIENT' && hideButtonsIfGuard === false && hideButtonsIfSpotter === false && hideButtonsIfDriver === false"
                                            pButton pRipple icon="pi pi-trash" pTooltip="Delete Job"
                                            class="p-button-rounded p-button-danger mr-2"
                                            (click)="deleteJob(job.jobId, 'QUEUE',2)"></button>
                                        <button *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Pickup"
                                            class="p-button-rounded p-button-success mr-2"
                                            (click)="openStatusDialog(job,'Confirm Pickup')"></button>
                                        <button *ngIf="job.status == 'IN_TRANSIT' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Drop"
                                            class="p-button-rounded p-button-warning mr-2"
                                            (click)="openStatusDialog(job,'Confirm Drop')"></button>
                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>
                                        <!-- <button pButton pRipple icon="pi pi-exclamation-circle"
                                            pTooltip="Exception for moves"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="exceptions(job.jobId)"></button> -->

                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>

                                        {{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>

                                        {{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>
                                        {{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>
                                        {{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>

                                    <td style="max-width: 100px; overflow: hidden;word-wrap: break-word;">
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                        <div *ngIf="job.pickupNotes">
                                            <b>{{job.pickupRole}}</b> {{job.pickupNotes}}
                                        </div>
                                        <div *ngIf="job.dropNotes">
                                            <b>{{job.dropRole}}</b> {{job.dropNotes}}
                                        </div>
                                    </td>
                                    <td>
                                        <!-- <span class="p-column-title">Trailer/Container</span> -->
                                        <span (click)="onTrailerSelect(job)" style="cursor: pointer;color:blue;text-decoration: underline;">{{job.fleet?.unitNumber}}</span>
                                    </td>
                                    <td>
                                        <p-tag *ngIf="job.status == 'OPEN'" rounded="true" severity="warning"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'IN_TRANSIT'" rounded="true" severity="primary"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'COMPLETED'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'EXCEPTION'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                    </td>
                                    <!-- <td>
                                        {{ job.trailerException }}
                                    </td> -->
                                    <td *ngIf="userType != 'ROLE_CLIENT'">
                                        {{job.assignedTo?.firstName}} {{job.assignedTo?.lastName}}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                    <td>
                                        <div *ngIf="job.climate">
                                            {{job.climate}}
                                            <div>
                                                Temp:{{job.temperature}}&nbsp;F
                                            </div>

                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="16">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsSpotterQueue" (onPageChange)="paginateSpotterQueue($event,'QUEUE')"
                            #paginator6></p-paginator>
                    </p-accordionTab>
                    <p-accordionTab header="Open - {{ totalRecordsOpen }}">
                        <p-table showGridlines #dt1 [value]="jobsOpen" [loading]="loading"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template #header>
                                <tr>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-popover #op>

                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-select placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDate" [options]="createdDateSort"
                                                            optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'OPEN')"
                                                            appendTo="body">
                                                        </p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="jobPickupTime">Spot Pickup Time</th>
                                    <th pSortableColumn="jobDropoffTime">Spot Dropoff Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                        <!-- <p-columnFilter type="text" field="jobNumber" display="menu"></p-columnFilter> -->
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                        <!-- <i class="pi pi-search" style="font-size: 1.5rem" (click)="demo($event)"></i> -->
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <!-- <th pSortableColumn="pickupSpot">Pickup Parking Spot</th> -->
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="status">Status</th>
                                    <!-- <th pSortableColumn="exception">Exceptions</th> -->
                                    <th pSortableColumn="assignedTo" *ngIf="userType != 'ROLE_CLIENT'">
                                        <div style="display: flex;flex-direction: row;">
                                            Assigned To
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op1>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="drivers" [showClear]="true"
                                                            [(ngModel)]="assignedDriver" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByAssignedUser($event,op1,'OPEN')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op1.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="createdBy">
                                        <div style="display: flex;flex-direction: row;">
                                            Created By
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op2>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="allUsers" [showClear]="true"
                                                            [(ngModel)]="createdUserOpen" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByCreatedUser($event,op2,'OPEN')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op2.toggle($event)"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th pSortableColumn="weather">Weather</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job>
                                <tr>
                                    <td>
                                        <button
                                            *ngIf="(job.status !== 'COMPLETED' && job.status !== 'IN_TRANSIT') && userType !== 'ROLE_CLIENT'"
                                            pButton pRipple icon="pi pi-pencil" pTooltip="Edit"
                                            class="p-button-rounded p-button-help mr-2"
                                            (click)="routeToEdit(job.jobId,'OPEN')"></button>
                                        <button
                                            *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT' && hideButtonsIfGuard === false && hideButtonsIfSpotter === false && hideButtonsIfDriver === false"
                                            pButton pRipple icon="pi pi-trash" pTooltip="Delete Job"
                                            class="p-button-rounded p-button-danger mr-2"
                                            (click)="deleteJob(job.jobId, 'OPEN')"></button>
                                        <button *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Pickup"
                                            class="p-button-rounded p-button-success mr-2"
                                            (click)="openStatusDialog(job,'Confirm Pickup')"></button>
                                        <button *ngIf="job.status == 'IN_TRANSIT' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Drop"
                                            class="p-button-rounded p-button-warning mr-2"
                                            (click)="openStatusDialog(job,'Confirm Drop')"></button>
                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>
                                        <!-- <button pButton pRipple icon="pi pi-exclamation-circle"
                                            pTooltip="Exception for moves"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="exceptions(job.jobId)"></button> -->

                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>
                                        {{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>
                                        {{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>
                                        {{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>

                                    <td style="max-width: 100px; overflow: hidden;word-wrap: break-word;">
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                        <div *ngIf="job.pickupNotes">
                                            <b>{{job.pickupRole}}</b> {{job.pickupNotes}}
                                        </div>
                                        <div *ngIf="job.dropNotes">
                                            <b>{{job.dropRole}}</b> {{job.dropNotes}}
                                        </div>
                                    </td>
                                    <td>
                                        <!-- <span class="p-column-title">Trailer/Container</span> -->
                                        <span (click)="onTrailerSelect(job)" style="cursor: pointer;color:blue;text-decoration: underline;">{{job.fleet?.unitNumber}}</span>
                                    </td>
                                    <td>
                                        <p-tag *ngIf="job.status == 'OPEN'" rounded="true" severity="warning"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'IN_TRANSIT'" rounded="true" severity="primary"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'COMPLETED'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'EXCEPTION'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                    </td>
                                    <!-- <td>
                                        {{ job.trailerException }}
                                    </td> -->
                                    <td *ngIf="userType != 'ROLE_CLIENT'">
                                        {{job.assignedTo?.firstName}} {{job.assignedTo?.lastName}}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                    <td>
                                        <div *ngIf="job.climate">
                                            {{job.climate}}
                                            <div>
                                                Temp:{{job.temperature}}&nbsp;F
                                            </div>

                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="15">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsOpen" (onPageChange)="paginate($event,'OPEN')"
                            #paginator1></p-paginator>
                    </p-accordionTab>
                    <p-accordionTab header="In transit - {{ totalRecordsIntransit }}">
                        <p-table showGridlines #dt2 [value]="jobsIntransit" [loading]="loadingIntransit"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template #header>
                                <tr>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-popover #op>

                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-select placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDateIntransit"
                                                            [options]="createdDateSort" optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'IN_TRANSIT')"
                                                            appendTo="body">
                                                        </p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="jobPickupTime">Spot Pickup Time</th>
                                    <th pSortableColumn="jobDropoffTime">Spot Dropoff Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                        <!-- <p-columnFilter type="text" field="jobNumber" display="menu"></p-columnFilter> -->
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                        <!-- <i class="pi pi-search" style="font-size: 1.5rem" (click)="demo($event)"></i> -->
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="status">Status</th>
                                    <!-- <th pSortableColumn="exception">Exceptions</th> -->
                                    <th pSortableColumn="assignedTo" *ngIf="userType != 'ROLE_CLIENT'">
                                        <div style="display: flex;flex-direction: row;">
                                            Assigned To
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op1>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="drivers" [showClear]="true"
                                                            [(ngModel)]="assignedDriverIntransit" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByAssignedUser($event,op1,'IN_TRANSIT')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op1.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="createdBy">
                                        <div style="display: flex;flex-direction: row;">
                                            Created By
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op2>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="allUsers" [showClear]="true"
                                                            [(ngModel)]="createdUserIntransist" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByCreatedUser($event,op2,'IN_TRANSIT')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op2.toggle($event)"></i>
                                            </div>
                                        </div>
                                    </th>
                                    <th pSortableColumn="weather">Weather</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job>
                                <tr>
                                    <td>
                                        <button
                                            *ngIf="userType !== 'ROLE_CLIENT' && (job.status !== 'COMPLETED' && job.status !== 'IN_TRANSIT')"
                                            pButton pRipple icon="pi pi-pencil" pTooltip="Edit"
                                            class="p-button-rounded p-button-help mr-2"
                                            (click)="routeToEdit(job.jobId)"></button>
                                        <button
                                            *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT' && hideButtonsIfGuard === false && hideButtonsIfSpotter === false && hideButtonsIfDriver === false"
                                            pButton pRipple icon="pi pi-trash" pTooltip="Delete Job"
                                            class="p-button-rounded p-button-danger mr-2"
                                            (click)="deleteJob(job.jobId)"></button>
                                        <button *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Pickup"
                                            class="p-button-rounded p-button-success mr-2"
                                            (click)="openStatusDialog(job,'Confirm Pickup')"></button>
                                        <button *ngIf="job.status == 'IN_TRANSIT' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Drop"
                                            class="p-button-rounded p-button-warning mr-2"
                                            (click)="openStatusDialog(job,'Confirm Drop')"></button>
                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>
                                        <!-- <button pButton pRipple icon="pi pi-exclamation-circle"
                                            pTooltip="Exception for moves"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="exceptions(job.jobId)"></button> -->

                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>
                                        {{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>
                                        {{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>
                                        {{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>

                                    <td style="max-width: 100px; overflow: hidden;word-wrap: break-word;">
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                        <div *ngIf="job.pickupNotes">
                                            <b>{{job.pickupRole}}</b> {{job.pickupNotes}}
                                        </div>
                                        <div *ngIf="job.dropNotes">
                                            <b>{{job.dropRole}}</b> {{job.dropNotes}}
                                        </div>
                                    </td>
                                    <td>
                                        <!-- <span class="p-column-title">Trailer/Container</span> -->
                                        <span (click)="onTrailerSelect(job)" style="cursor: pointer;color:blue;text-decoration: underline;">{{job.fleet?.unitNumber}}</span>
                                    </td>
                                    <td>
                                        <p-tag *ngIf="job.status == 'OPEN'" rounded="true" severity="warning"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'IN_TRANSIT'" rounded="true" severity="primary"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'COMPLETED'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'EXCEPTION'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                    </td>
                                    <!-- <td>
                                        {{ job.trailerException }}
                                    </td> -->
                                    <td *ngIf="userType != 'ROLE_CLIENT'">
                                        {{job.assignedTo?.firstName}} {{job.assignedTo?.lastName}}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                    <td>
                                        <div *ngIf="job.climate">
                                            {{job.climate}}
                                            <div>
                                                Temp:{{job.temperature}}&nbsp;F
                                            </div>

                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="15">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsIntransit"
                            (onPageChange)="paginateIntransit($event,'IN_TRANSIT')" #paginator2></p-paginator>
                    </p-accordionTab>
                    <p-accordionTab header="Completed">
                        <p-table showGridlines #dt3 [value]="jobsCompleted" [loading]="loadingComplete"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template #header>
                                <tr>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-popover #op>

                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-select placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDateCompleted"
                                                            [options]="createdDateSort" optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'COMPLETED')"
                                                            appendTo="body">
                                                        </p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="jobPickupTime">Spot Pickup Time</th>
                                    <th pSortableColumn="jobDropoffTime">Spot Dropoff Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                        <!-- <p-columnFilter type="text" field="jobNumber" display="menu"></p-columnFilter> -->
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                        <!-- <i class="pi pi-search" style="font-size: 1.5rem" (click)="demo($event)"></i> -->
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="status">Status</th>
                                    <!-- <th pSortableColumn="exception">Exceptions</th> -->
                                    <th pSortableColumn="assignedTo" *ngIf="userType != 'ROLE_CLIENT'">
                                        <div style="display: flex;flex-direction: row;">
                                            Assigned To
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op1>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="drivers" [showClear]="true"
                                                            [(ngModel)]="assignedDriverCompleted" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByAssignedUser($event,op1,'COMPLETED')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op1.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="createdBy">
                                        <div style="display: flex;flex-direction: row;">
                                            Created By
                                            <div *ngIf="hideButtonsIfGuard == false"
                                                class="flex justify-content-center">
                                                <p-popover #op2>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-select [options]="allUsers" [showClear]="true"
                                                            [(ngModel)]="createdUserCompleted" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByCreatedUser($event,op2,'COMPLETED')"></p-select>
                                                    </ng-template>


                                                </p-popover>
                                                <i class="pi pi-filter" (click)="op2.toggle($event)"></i>

                                            </div>
                                        </div>
                                    </th>
                                    <th pSortableColumn="weather">Weather</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job>
                                <tr>
                                    <td>
                                        <button
                                            *ngIf="userType !== 'ROLE_CLIENT' && (job.status !== 'COMPLETED' && job.status !== 'IN_TRANSIT')"
                                            pButton pRipple icon="pi pi-pencil" pTooltip="Edit"
                                            class="p-button-rounded p-button-help mr-2"
                                            (click)="routeToEdit(job.jobId)"></button>
                                        <button
                                            *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT' && hideButtonsIfGuard === false && hideButtonsIfSpotter === false && hideButtonsIfDriver === false"
                                            pButton pRipple icon="pi pi-trash" pTooltip="Delete Job"
                                            class="p-button-rounded p-button-danger mr-2"
                                            (click)="deleteJob(job.jobId)"></button>
                                        <button *ngIf="job.status == 'OPEN' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Pickup"
                                            class="p-button-rounded p-button-success mr-2"
                                            (click)="openStatusDialog(job,'Confirm Pickup')"></button>
                                        <button *ngIf="job.status == 'IN_TRANSIT' && userType !== 'ROLE_CLIENT'" pButton
                                            pRipple icon="pi pi-car" pTooltip="Confirm Drop"
                                            class="p-button-rounded p-button-warning mr-2"
                                            (click)="openStatusDialog(job,'Confirm Drop')"></button>
                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>

                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>
                                        {{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>{{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>
                                        {{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>
                                        {{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>

                                    <td style="max-width: 100px; overflow: hidden;word-wrap: break-word;">
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                        <div *ngIf="job.pickupNotes">
                                            <b>{{job.pickupRole}}</b> {{job.pickupNotes}}
                                        </div>
                                        <div *ngIf="job.dropNotes">
                                            <b>{{job.dropRole}}</b> {{job.dropNotes}}
                                        </div>
                                    </td>
                                    <td>
                                        <!-- <span class="p-column-title">Trailer/Container</span> -->
                                        <span (click)="onTrailerSelect(job)" style="cursor: pointer;color:blue;text-decoration: underline;">{{job.fleet?.unitNumber}}</span>
                                    </td>
                                    <td>
                                        <p-tag *ngIf="job.status == 'OPEN'" rounded="true" severity="warning"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'IN_TRANSIT'" rounded="true" severity="primary"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'COMPLETED'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'EXCEPTION'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                    </td>
                                    <!-- <td>
                                        {{ job.trailerException }}
                                    </td> -->
                                    <td *ngIf="userType != 'ROLE_CLIENT'">
                                        {{job.assignedTo?.firstName}} {{job.assignedTo?.lastName}}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                    <td>
                                        <div *ngIf="job.climate">
                                            {{job.climate}}
                                            <div>
                                                Temp:{{job.temperature}}&nbsp;F
                                            </div>

                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="15">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsCompleted"
                            (onPageChange)="paginateCompleted($event,'COMPLETED')" #paginator3></p-paginator>
                    </p-accordionTab>
                    <p-accordionTab header="Exceptions - {{ totalRecordsException }}" *ngIf="exceptionActive">
                        <p-table #dt4 [value]="jobsException" [loading]="loadingException"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10"
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template pTemplate="header">
                                <tr>
                                    <th style="width: 70px;"></th>
                                    <th pSortableColumn="createdDate">
                                        <div style="display: flex;flex-direction: row;">
                                            Spot Creation Date
                                            <div class="flex justify-content-center">
                                                <p-overlayPanel #op>

                                                    <ng-template pTemplate="content" style="height: 2000px;">
                                                        <h4>Sort Created Date</h4>
                                                        <p-dropdown placeholder="Order By" [showClear]="true"
                                                            [(ngModel)]="selectedSortDateException"
                                                            [options]="createdDateSort" optionLabel="name"
                                                            (onChange)="sortCreationDate($event,op,'EXCEPTION')"
                                                            appendTo="body">
                                                        </p-dropdown>
                                                    </ng-template>


                                                </p-overlayPanel>
                                                <i class="pi pi-filter" (click)="op.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="jobPickupTime">Spot Pickup Time</th>
                                    <th pSortableColumn="jobDropoffTime">Spot Dropoff Time</th>
                                    <th pSortableColumn="jobNumber">
                                        Spot Number
                                        <!-- <p-columnFilter type="text" field="jobNumber" display="menu"></p-columnFilter> -->
                                    </th>
                                    <th pSortableColumn="jobPriority">
                                        Spot Priority
                                        <!-- <i class="pi pi-search" style="font-size: 1.5rem" (click)="demo($event)"></i> -->
                                    </th>
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <th pSortableColumn="dropLocation">Drop Location</th>
                                    <th pSortableColumn="description" style="width: 160px;">Notes</th>
                                    <th pSortableColumn="trailerTruck">Trailer/Container</th>
                                    <th pSortableColumn="status">Status</th>
                                    <th pSortableColumn="exception">Exceptions</th>
                                    <th pSortableColumn="assignedTo" *ngIf="userType != 'ROLE_CLIENT'">
                                        <div style="display: flex;flex-direction: row;">
                                            Assigned To
                                            <div class="flex justify-content-center">
                                                <p-overlayPanel #op1>

                                                    <ng-template pTemplate="content">
                                                        <h4>Select User</h4>
                                                        <p-dropdown [options]="drivers" [showClear]="true"
                                                            [(ngModel)]="assignedDriverException" optionLabel="fullName"
                                                            placeholder="Select User"
                                                            (onChange)="sortByAssignedUser($event,op1,'EXCEPTION')"></p-dropdown>
                                                    </ng-template>


                                                </p-overlayPanel>
                                                <i class="pi pi-filter" (click)="op1.toggle($event)"></i>
                                            </div>
                                        </div>

                                    </th>
                                    <th pSortableColumn="createdBy">Created By</th>
                                    <th pSortableColumn="weather">Weather</th>
                                    <th pSortableColumn="sequenceAsn" *ngIf="isSupervisorOrClient">Sequence ASN</th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-job>
                                <tr>
                                    <td>

                                        <button *ngIf="job.bols && job.bols.length > 0" pButton pRipple
                                            icon="pi pi-images" pTooltip="View BOL images"
                                            class="p-button-rounded p-button-secondary mr-2"
                                            (click)="viewBol(job.bols)"></button>

                                    </td>
                                    <td>
                                        {{job.audit?.createdDate}}
                                    </td>
                                    <td>

                                        {{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>

                                        {{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                                    <td>
                                        {{job.jobNumber}}
                                    </td>
                                    <td class="text-center">
                                        <p-tag *ngIf="job.priority == 'HIGH'" rounded="true" severity="danger"
                                            value="High"></p-tag>
                                        <p-tag *ngIf="job.priority == 'MEDIUM'" rounded="true" severity="primary"
                                            value="Medium"></p-tag>
                                        <p-tag *ngIf="job.priority == 'LOW'" rounded="true" severity="success"
                                            value="Low"></p-tag>
                                    </td>
                                    <td>

                                        {{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} -
                                        <br />
                                        {{job.pickupSpot?.spotName}}
                                    </td>

                                    <td>
                                        {{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} -
                                        <br />
                                        {{job.dropSpot?.spotName}}
                                    </td>

                                    <td style="max-width: 100px; overflow: hidden;word-wrap: break-word;">
                                        <div *ngIf="job.description">
                                            <b>{{job.descriptionRole}}</b> {{job.description}}
                                        </div>
                                        <div *ngIf="job.pickupNotes">
                                            <b>{{job.pickupRole}}</b> {{job.pickupNotes}}
                                        </div>
                                        <div *ngIf="job.dropNotes">
                                            <b>{{job.dropRole}}</b> {{job.dropNotes}}
                                        </div>
                                    </td>
                                    <td>
                                        {{job.fleet?.unitNumber}}
                                    </td>
                                    <td>
                                        <p-tag *ngIf="job.status == 'OPEN'" rounded="true" severity="warning"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'IN_TRANSIT'" rounded="true" severity="primary"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'COMPLETED'" rounded="true" severity="success"
                                            [value]="job.status"></p-tag>
                                        <p-tag *ngIf="job.status == 'EXCEPTION'" rounded="true" severity="danger"
                                            [value]="job.status"></p-tag>
                                    </td>
                                    <td>
                                        {{ job.trailerException }}
                                    </td>
                                    <td *ngIf="userType != 'ROLE_CLIENT'">
                                        {{job.assignedTo?.firstName}} {{job.assignedTo?.lastName}}
                                    </td>
                                    <td>
                                        {{job.audit.createdBy?.firstName}} {{job.audit.createdBy?.lastName}}
                                    </td>
                                    <td>
                                        <div *ngIf="job.climate">
                                            {{job.climate}}
                                            <div>
                                                Temp:{{job.temperature}}&nbsp;F
                                            </div>

                                        </div>
                                    </td>
                                    <td *ngIf="isSupervisorOrClient">
                                        {{ job.sequenceAsn }}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="15">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecordsException"
                            (onPageChange)="paginateExceptions($event,'EXCEPTION')" #paginator4></p-paginator>
                    </p-accordionTab>
                </p-accordion>
            </div>

        </div>

        <p-dialog [(visible)]="jobStatusDialog" [style]="{width: '450px'}" header="{{modalTitle}}" [modal]="true">
            <p-fluid>
                <div class="flex flex-column mt-4">
                    <div class="flex justify-content-between">
                        <div class="flex">
                            <i class="pi pi-map-marker mr-2"></i>
                            <span class="font-semibold">{{modalTitle == 'Confirm Pickup' ? 'Pickup' : 'Drop'}}</span>
                        </div>
                        <div class="flex">
                            <span class="font-semibold spots">{{modalTitle == 'Confirm Pickup' ?
                                modalJob?.pickupSpot?.spotName : modalJob?.dropSpot?.spotName}}</span>
                        </div>
                    </div>
                    <div *ngIf="modalTitle == 'Confirm Pickup'" class="flex flex-column mt-1">
                        <p class="mb-1"><b>{{modalJob.pickupLocation?.locationName}}</b></p>
                        <small>{{modalJob.pickupLocation?.street}}, {{modalJob.pickupLocation?.city}},
                            {{modalJob.pickupLocation?.state}}, {{modalJob.pickupLocation?.zip}}</small>
                    </div>
                </div>
                <hr>
                <form *ngIf="modalTitle !== 'Confirm Pickup'" [formGroup]="jobForm">
                    <div class="field">
                        <label for="locationList">Location</label>
                        <p-select class="full-width" [options]="locationList" formControlName="dropLocationId"
                            placeholder="Select Drop Location" optionLabel="locationName" optionValue="locationId"
                            (onChange)="onSelectLocation($event)"></p-select>
                    </div>

                    <div class="field">
                        <label for="dropSpot">Drop Parking Spot</label>
                        <p-select class="full-width" [options]="dropSpots" formControlName="dropSpotId"
                            placeholder="Select Drop Spot" optionLabel="spotAndStatus" optionValue="spotId"></p-select>
                    </div>
                    <div class="field">
                        <label for="trailerStatus">Trailer/Container Status</label>
                        <p-select class="full-width" [options]="trailerStatus" formControlName="fleetStatus"
                            placeholder="Select Trailer/Container Status" optionLabel="name"
                            optionValue="code"></p-select>
                    </div>
                </form>
                <form [formGroup]="jobStatusForm">
                    <div class="field">
                        <label for="remarks">Drop Notes(If any)</label>
                        <textarea id="remarks" style="min-height: 4rem;" pTextarea formControlName="notes"
                            rows="3" cols="30"></textarea>
                    </div>
                </form>
            </p-fluid>

            <ng-template pTemplate="footer">
                <button pButton pRipple label="Cancel" icon="pi pi-times" class="p-button-text"
                    (click)="hidejobStatusDialog()"></button>
                <button pButton pRipple label="Confirm" icon="pi pi-check" class="p-button-text"
                    (click)="changeJobStatus()"></button>
            </ng-template>
        </p-dialog>

        <p-dialog [(visible)]="deleteProductDialog" header="Confirm" [modal]="true" [style]="{width:'450px'}">
            <div class="flex align-items-center justify-content-center">
                <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem"></i>
                <span *ngIf="product">Are you sure you want to delete <b>{{product.name}}</b>?</span>
            </div>
            <ng-template pTemplate="footer">
                <button pButton pRipple icon="pi pi-times" class="p-button-text" label="No"
                    (click)="deleteProductDialog = false"></button>
                <button pButton pRipple icon="pi pi-check" class="p-button-text" label="Yes"
                    (click)="confirmDelete()"></button>
            </ng-template>
        </p-dialog>
    </div>
</div>
<p-dialog [(visible)]="deleteJobDialog" header="Confirm" [modal]="true" [style]="{width:'450px'}">
    <div class="flex align-items-center justify-content-start">
        <i class="pi pi-exclamation-triangle mr-3 my-4" style="font-size: 2rem"></i>
        <span>Are you sure you want to delete this spot ?</span>
    </div>
    <ng-template pTemplate="footer">
        <button pButton pRipple icon="pi pi-times" class="p-button-text" label="No"
            (click)="deleteJobDialog = false"></button>
        <button pButton pRipple icon="pi pi-check" class="p-button-text" label="Yes" (click)="confirmDelete()"></button>
    </ng-template>
</p-dialog>
<p-dialog [(visible)]="bolDialog" header="BoL Images" [modal]="true" [style]="{width:'700px',height: '700px'}">

    <ng-template pTemplate="content">
        <!-- <p-image *ngIf="selectedBol && selectedBol[0]" [src]="selectedBol[0].imagePath" alt="Image" width="250"></p-image> -->
        <p-carousel [value]="selectedBol" (onPage)="onPageChange($event)" [numVisible]="1" [numScroll]="1"
            [circular]="false">
            <ng-template let-bol pTemplate="item">
                <div class="border-1 surface-border border-round m-2 text-center py-6 px-3 custom-div">
                    <div>
                        <p-image [src]="bol.imagePath" alt="Image" width="100%" height="400px"></p-image>

                    </div>
                </div>
                <!-- <button pButton pRipple label="Download" class="p-button-success" (click)="downloadImage(bol.imagePath)"></button> -->
                <!-- <a [href]="bol.imagePath" download="downloaded-image.jpg" target="_blank">Download Image</a> -->
            </ng-template>
        </p-carousel>

    </ng-template>
    <ng-template pTemplate="footer">
        <!-- <button pButton pRipple label="Download" class="p-button-success"></button> -->
        <a [href]="selectedImagePath" download="downloaded-image.jpg" target="_blank">Download Image</a>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="exceptionDialog" header="Exception for moves" [modal]="true" (onHide)="cancelExceptionDialog()"
    [style]="{width:'600px',height: '500px'}">

    <ng-template pTemplate="content">
        <div class="">
            <div class="field">
                <label>Select the type of exception</label>
            </div>
            <div class="field">

            </div>

            <div class="field">
                <input pInputText type="text" class="w-full" placeholder="Enter new exception"
                    (blur)="selectException($event.target.value)" [(ngModel)]="exceptionInputText" />
            </div>

        </div>

        <ng-template ngFor let-item [ngForOf]="exceptionLists" class="exception-flex-style">
            <button pButton label="{{item.name}}" class="p-button exception-flex-style-child"
                [ngClass]="item.name == selectedException ? 'active':''"
                (click)="selectException(item.name,1)"></button>
            <!-- <p-radioButton name="group1"  value="{{item.name}}" [(ngModel)]="selectedButton" (click)="selectException(item.name)" class="custom-radio exception-flex-style-child">
                <label>Option 1</label>
            </p-radioButton> -->

        </ng-template>

    </ng-template>
    <ng-template pTemplate="footer">
        <button pButton pRipple class="p-button mrgr-10" label="Send" (click)="sendException()"></button>
        <button pButton label="Cancel" class="p-button-text" (click)="cancelExceptionDialog()"></button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="isExportClicked" header="Export Date Range" [modal]="true" [style]="{width:'600px'}"
    (onHide)="clearExportPopup()">

    <ng-template pTemplate="content">
        <span class="block mt-2 md:mt-0 p-input-icon-left">
            <span class="mr-2 font-medium">From Date : <input pInputText type="date" [(ngModel)]="fromExportDate"
                    name="fromExportDate" placeholder="Select Date" /></span>
            <span class="mx-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toExportDate"
                    name="toExportDate" placeholder="Select Date" /></span>
        </span>
        <span class="error" *ngIf="exportDateValid===true">*Please enter both from and to date.</span>
        <span class="error" *ngIf="exportRangeValid===true">Please enter a valid date range.</span>
        <span class="error" *ngIf="sixMonthValidDate === false">
            * Cannot export more than Six months. Please change the dates.
        </span>
    </ng-template>
    <ng-template pTemplate="footer">
        <button pButton pRipple icon="pi pi-times" class="p-button-text" label="Cancel"
            (click)="clearExportPopup()"></button>
        <button pButton pRipple icon="pi pi-check" class="p-button-text" label="Ok" (click)="exportExcel()"></button>
    </ng-template>
</p-dialog>
<p-dialog [(visible)]="editableJobsDialog" header="Warning" [modal]="true" [style]="{width:'450px'}">
    <div class="flex align-items-center justify-content-center">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem"></i>
        <span>This Spot is currently in a different status. Please refresh the page using the refresh button at the top
            of the Spots Page.</span>
    </div>
    <ng-template pTemplate="footer">
        <button pButton pRipple icon="pi pi-check" class="p-button-text" label="Ok"
            (click)="editableJobsDialog = false"></button>
    </ng-template>
</p-dialog>
<p-dialog [(visible)]="trailerHistoryDialog" [modal]="true" position="middle" [style]="{height:'600px'}" >
    <ng-template pTemplate = "header">
        <div style="font-size: 1.75rem;">
            <span>Trailer History Log</span>
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <div>
            <h4>Trailer #: {{ unitNumberSelected }}</h4>
            <p-table #dt3 [value]="selectedTrailerLogs"
            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
            [columns]="cols" [rows]="10"
            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot','jobNumber']"
            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'" [loading]="loadingSelectedTrailerJobs">

            <ng-template pTemplate="header">
            <tr>
                <th pSortableColumn="createdDate">Event Date</th>
                <th pSortableColumn="actions">Action</th>
                <th pSortableColumn="createdBy">Created By</th>
                <th pSortableColumn="assignedTo">Assigned To</th>
            </tr>
            </ng-template>
            <ng-template pTemplate="body" let-logs>
            <tr>
                <td>
                    {{logs.audit?.createdDate}}
                </td>
                <td>
                    <span *ngIf="logs.job != undefined">
                        Move: location ({{ logs.job?.pickupLocation?.locationName }} - {{logs.job?.pickupSpot?.spotName}}) to ({{logs.job?.dropLocation?.locationName}} - {{logs.job?.dropSpot?.spotName}}) 
                    </span>
                    <span *ngIf="logs.actions != undefined && logs.job == undefined">
                        {{logs.actions}}
                    </span>
                </td>
                <td>
	                <span *ngIf="logs.job != undefined">{{logs.job?.audit?.lastModifiedBy?.firstName}} {{logs.job?.audit?.lastModifiedBy?.lastName}}</span>
	                <span *ngIf="logs.job == undefined">{{logs.audit.createdBy?.firstName}} {{logs.audit.createdBy?.lastName}}</span>
                </td>
                <td>
	                <span>{{logs.job?.assignedTo?.firstName}} {{logs.job?.assignedTo?.lastName}}</span>
                </td>

            </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
            <tr>
                <td colspan="4">No logs found.</td>
            </tr>
            </ng-template>
            </p-table>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <div class="dialog-footer-fixed mt-4"  style="text-align: right;">
            <button pButton pRipple label="Ok" class="p-button-secondary" (click)="closeSelectedTrailerPopup()"></button>
            <button pButton pRipple label="Go To Report" class="p-button-primary" (click)="redirectToTrailerLogsReport()"></button>  
        </div>
    </ng-template>
</p-dialog>