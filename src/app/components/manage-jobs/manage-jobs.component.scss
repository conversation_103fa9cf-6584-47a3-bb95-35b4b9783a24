:host ::ng-deep .job-hover:hover{
     box-shadow: 2px 2px 2px #cdcdcd !important;
}

:host ::ng-deep .spots{
     padding: 5px;
     background-color: #cdcdcd;
     border-radius: 5px;
}

:host ::ng-deep {
     .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
          text-align: center !important;
      }
     
      .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
          text-align: center !important;
      }
}
.custom-div {
     height: 500px; /* Set the height to 200 pixels */
     /* You can use other units like %, vh, etc. based on your needs */
 }
 a:link, a:visited {
     background-color: rgb(53, 59, 143);
     color: white;
     padding: 14px 20px;
     text-align: center;
     text-decoration: none;
     display: inline-block;
     border-radius: 8px;
   }
   
   a:hover, a:active {
     background-color: rgb(91, 96, 155);
   }


// ::ng-deep .dialog-footer-fixed {
//      position: absolute;
//      bottom: 10px;
//      right: 20px;
//      z-index: 1;
//      // background-color: white;
//      padding-top: 10px;
//  }
 
 /* Optional: If the table content gets cut off */
//  ::ng-deep .p-dialog .p-dialog-content {
//      overflow: auto !important;
//      padding-bottom: 70px !important; /* to make space for the fixed button */
//  }

// ::ng-deep .dialog-footer-fixed {
//      position: sticky;
//      bottom: 0;
//      background-color: white; /* optional, to avoid content overlay */
//      padding: 10px 20px;
//      text-align: right;
//      border-top: 1px solid #ccc;
//      z-index: 10;
//  }
 
//  ::ng-deep .p-dialog .p-dialog-content {
//      overflow: auto !important;
//      padding-bottom: 70px !important; /* good! this makes space for footer */
//  }


 
 