<div class="grid">
	<div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>

    <div class="col-12">
        <div class="card">
            <div class="flex align-items-center justify-content-between mb-0">
                <h5>Client Locations  <p-tag *ngIf="userType !== 'ROLE_CLIENT' && hideButtonsIfDriver !== true && hideButtonsIfSupervisor === false && hideButtonsIfGuard === false && hideButtonsIfSpotter === false" class="tag-hover" severity="warning" (click)="routeToEditClient()" value="Edit" icon="pi pi-pencil"></p-tag></h5>
            </div>
            <p class="mb-2"><span class="font-medium text-500">Client Name:</span> {{client?.clientName}}</p>
            <p class="mb-2"><span class="font-medium text-500">Address:</span>  {{client?.street}}, {{client?.city}}, {{client?.state}}, {{client?.zip}}</p>
            <p class="mb-2"><span class="font-medium text-500">Contact Person:</span> {{client?.contactPerson}}</p>
            <p class="mb-2"><span class="font-medium text-500">Contact Email:</span> {{client?.contactEmail}}</p>
            <p class="mb-2"><span class="font-medium text-500">Contact Phone:</span> {{client?.contactPhone}}</p>
            <br>
            <div class="flex flex-column md:flex-row md:justify-content-between align-items-end">
                <div>
                    <h5>Default Location</h5>
                    <p class="mb-2"><span class="font-medium text-500">Location Name:</span> {{defaultLocations?.locationName}}</p>
                    <p class="mb-2"><span class="font-medium text-500">Address:</span> {{defaultLocations?.street}},
                        {{defaultLocations?.city}}, {{defaultLocations?.state}}, {{defaultLocations?.zip}}</p>
                </div>
                <div>
                    <button *ngIf="userType == 'ROLE_SUPERVISOR' || userType == 'ROLE_IT' || userType == 'ROLE_ADMIN'" pButton pRipple label="New Spot" icon="pi pi-plus"
                        class="p-button-success mr-2" (click)="routeToAddJob()"></button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12">
		<div class="card">
			<h5>Locations <p-tag *ngIf="userType !== 'ROLE_CLIENT' && hideButtonsIfDriver !== true && hideButtonsIfSupervisor === false && hideButtonsIfGuard === false && hideButtonsIfSpotter === false" class="tag-hover" severity="success" (click)="routeToAddLocation()" value="Add" icon="pi pi-plus"></p-tag></h5>
			<p-dataView #dv [value]="locations" [paginator]="false" [rows]="9" [layout]="'grid'">
                <ng-template #grid let-locationList>
                    <div class="grid grid-cols-12 grid-nogutter">
                        <div *ngFor="let location of locationList" class="col-12 md:col-4">
                            <div *ngIf="location && selectedLocation"
                                [ngClass]="location.locationId === selectedLocation.locationId ? 'highlight' : ''"
                                (click)="showSpotsForLocation(location,scrollToMap)"
                                class="card m-3 border-1 surface-border location-hover">
                                <div class="flex align-items-center justify-content-between">
                                    <div class="flex align-items-center">
                                        <i class="pi pi-map-marker mr-2"></i>
                                        <span class="font-semibold">{{location.locationName}}</span>
                                    </div>
                                    <i *ngIf="userType !== 'ROLE_CLIENT' && hideButtonsIfDriver !== true && hideButtonsIfSupervisor === false && hideButtonsIfGuard === false && hideButtonsIfSpotter === false"
                                        class="pi pi-pencil" (click)="routeToEditLocation(location.locationId)"></i>
                                </div>
                                <div class="mt-3">
                                    <div class="mb-2">{{location.street}}, {{location.city}}, {{location.state}}, {{location.country}}
                                    </div>
                                    <!-- <div class="mb-2">{{location.remarks}}</div> -->
                                    <!-- <div class="mb-2"  (click)="routeToSpots(location.locationId,location.locationName)"><i class="pi pi-directions pr-2 "></i>Route To Spots</div> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </ng-template>
			</p-dataView>
		</div>
	</div>

    <!-- <div class="col-12">
		<div class="card">
            <h5 class="mb-4">Docks/Parking Spots for {{selectedLocation?.locationName}}</h5>
            <div #scrollToMap>
            <agm-map [zoom]="zoom" [latitude]="selectedLocation?.latitude" [longitude]="selectedLocation?.longitude" [mapTypeId]="mapType" [mapTypeControl]='true'>
                <agm-marker [latitude]="selectedLocation?.latitude" [longitude]="selectedLocation?.longitude"></agm-marker>
                <agm-marker *ngFor="let spot of spotsArray; let i = index"
                (mouseOver)="onMouseOver(infoWindow,$event)" 
                (mouseOut)="onMouseOut(infoWindow,$event)" 
                [latitude]="spot.latitude" 
                [longitude]="spot.longitude"
                [iconUrl] ="{url: spot.status == 'OCCUPIED' ?  '../../../assets/demo/images/map-icons/green-marker.png' : '../../../assets/demo/images/map-icons/red-marker.png'}"
                >
                    <agm-info-window [disableAutoPan]="false" #infoWindow>
                        <p><b>Docks/Spots:</b> {{spot.spotName}}</p>
                        <p><b>Status:</b> {{spot.status}}</p>
                        <p><b>Trailer/Truck:</b> {{spot.trailer ? spot.trailer : '-' }}</p>
                        <p><b>Carrier:</b> {{spot.carrier ? spot.carrier : '-'}}</p>
                        <p><b>Notes:</b> {{spot.remarks}}</p>
                    </agm-info-window>
                </agm-marker>
            </agm-map>
            </div>
        </div>
    </div> -->


    <div *ngIf="selectedLocation?.mapImageUrl" class="col-12">
		<div class="card">
            <h5 class="mb-4">{{selectedLocation?.locationName}}</h5>
            <img [src]="selectedLocation?.mapImageUrl" alt="Location Image" width="100%">
        </div>
    </div>


    
    <div *ngIf="showChildren" class="col-12">
		<div class="card" style="position:relative;">
            <h5 class="mb-4">Docks/Parking Spots for {{selectedLocation?.locationName}}</h5>
            <div class="scrollMap">
            <app-preview-canvas-map  [locationMapJson]="selectedLocation?.locationMapJson" [locations]="selectedLocation"></app-preview-canvas-map>
            </div>
            <div class="map-legend-container">
                <div *ngFor="let arr of mapArr" class="map-legend">
                    <div class="map-legend-tile" [style.backgroundColor]="arr.color"></div>
                    <div class="map-legend-text">{{arr.statusVal}}</div>
                </div>
            </div>
        </div>
    </div>


    <div class="col-12">
        <div class="card">
            <h5 class="mb-4">Docks/Parking Spot-Trailer/Container Status</h5>
            <br>
            <div class="flex flex-column md:flex-row md:justify-content-between align-items-end">
                <div>
                    <p class="mb-2"><span class="font-medium text-500">Occupied Full Trailer:</span>
                        {{tallyObj?.totalOccupiedFullTrailer}}</p>
                    <p class="mb-2"><span class="font-medium text-500">Occupied Empty Trailer:</span>
                        {{tallyObj?.totalOccupiedEmptyTrailer}}</p>
                    <p class="mb-2"><span class="font-medium text-500">Location Reserved:</span> {{tallyObj?.totalToBeOccupied}}</p>
                    <p class="mb-2"><span class="font-medium text-500">Scheduled for Pick-Up:</span> {{tallyObj?.totalToBeEmpty}}
                    </p>
                    <p class="mb-2"><span class="font-medium text-500">Empty:</span> {{tallyObj?.totalEmpty}}</p>
                </div>
                <div>
                    <button *ngIf="userType == 'ROLE_SUPERVISOR' || userType == 'ROLE_IT' || userType == 'ROLE_ADMIN'" pButton pRipple label="New Spot"
                        icon="pi pi-plus" class="p-button-success mr-2" (click)="routeToAddJob()"></button>
                </div>
            </div>
            <br>
			<p-table showGridlines #dt [value]="spotsFilteredArray" styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true" [rows]="10" [globalFilterFields]="['fleet.unitNumber']" [rowHover]="true" dataKey="id">
                <ng-template #caption>
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <div>
                            <p-select [showClear]="true" class="ml-2" placeholder="Location Status" (onChange)="filterSpotsByStatus($event)" [options]="statuses" optionLabel="name" optionValue="code"></p-select>
                            <p-select [showClear]="true" class="ml-2" placeholder="Select Trailer Status" (onChange)="filterSpotsByTrailerStatus($event)" [options]="trailerStatus" optionLabel="name" optionValue="code"></p-select>
                        </div>
                        <div class="flex">
                            <p-iconfield iconPosition="left" class="ml-auto">
                                <p-inputicon>
                                    <i class="pi pi-search"></i>
                                </p-inputicon>
                                <input pInputText type="text" (input)="dt.filterGlobal($event.target.value, 'contains')"
                                    placeholder="Search..." />
                            </p-iconfield>
                        </div>
                    </div>
                </ng-template>
                <ng-template #header>
                    <tr>
                        <!-- <th></th> -->
                        <th pSortableColumn="spotName">Dock/Parking Spots</th>
                        <!-- <th pSortableColumn="type">Dock/Parking Spot</th> -->
                        <th pSortableColumn="status">Current Dock Status</th>
                        <th pSortableColumn="carrier">Carrier</th>
                        <th pSortableColumn="unitNumber">Trailer #</th>
                        <th pSortableColumn="trailerStatus">Trailer Status</th>
                        <th pSortableColumn="nextJobStatus">Scheduled Dock Status</th>
                        <th pSortableColumn="elapsedTime">Time Empty(dd:hh:mm:ss)</th>
                        <th pSortableColumn="elapsedTime">Time Occupied(dd:hh:mm:ss)</th>
                        <!-- <th pSortableColumn="latitude">Latitude</th>
                        <th pSortableColumn="longitude">Longitude</th> -->
                    </tr>
                </ng-template>
                <ng-template #body let-spot>
                    <tr>
                        <!-- <td>
                            <button pButton pRipple icon="pi pi-pencil" pTooltip="Edit" class="p-button-rounded p-button-warning mr-2" (click)="editSpot(spot)"></button>
                            <button pButton pRipple icon="pi pi-trash" pTooltip="Delete" class="p-button-rounded p-button-danger mt-2" (click)="deleteSpot(spot)"></button>
                        </td> -->
                        <td >
                            {{spot.spotName}}
                        </td>
                        <!-- <td >
                            {{spot.type}}
                        </td> -->
                        <td >
                            <p-tag *ngIf="spot.status == 'EMPTY' && spot?.fleet == null"  rounded="true" severity="danger" value="Empty"></p-tag>
                            <p-tag *ngIf="spot.status == 'OCCUPIED' && spot?.fleet?.fleetStatus == 'FULL' " rounded="true" severity="success" value="Occupied Full Trailer"></p-tag>
                            <p-tag *ngIf="spot.status == 'OCCUPIED' && spot?.fleet?.fleetStatus == 'EMPTY'" rounded="true" severity="primary" value="Occupied Empty Trailer"></p-tag>
                            <p-tag *ngIf="spot.status == 'TO_BE_EMPTY'" rounded="true" severity="info" value="Scheduled for Pick-Up"></p-tag>
                            <p-tag *ngIf="spot.status == 'TO_BE_OCCUPIED'" rounded="true" severity="warning" value="Location Reserved"></p-tag>
                        </td>
                        <td >
                            {{spot?.fleet?.carrier ? spot?.fleet?.carrier : '-'}}
                        </td>
                        <td >
                            {{spot?.fleet?.unitNumber ? spot?.fleet?.unitNumber : '-' }}
                        </td>
                        
                        <td >
                            {{spot?.fleet?.fleetStatus && spot?.fleet?.fleetStatus=='FULL' ? 'LOADED' : ''}}
                            {{spot?.fleet?.fleetStatus && spot?.fleet?.fleetStatus=='EMPTY' ? spot?.fleet?.fleetStatus : ''}}
                        </td>
                        <td >
                            <p-tag *ngIf="spot.nextJobStatus == 'TO_BE_EMPTY'" rounded="true" severity="info" value="Scheduled for Pick-Up"></p-tag>
                            <p-tag *ngIf="spot.nextJobStatus == 'TO_BE_OCCUPIED'" rounded="true" severity="warning" value="Location Reserved"></p-tag>
                       </td>
                        <td>
                            {{spot.emptiedSinceSeconds ? spot.emptiedSinceSeconds : '-'}} 
                        </td>
                        <td>
                            {{spot.occupiedSinceSeconds ? spot.occupiedSinceSeconds : '-'}}
                        </td>
                        <!-- <td >
                            {{spot.latitude}}
                        </td>
                        <td >
                            {{spot.longitude}}
                        </td> -->
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="6">No parking spot found.</td>
                    </tr>
                </ng-template>
            </p-table>
		</div>
	</div>

    
</div>
