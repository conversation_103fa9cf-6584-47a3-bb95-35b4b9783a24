:host ::ng-deep{
    .location-hover{
        cursor: pointer; 
     }
     
     .location-hover:hover{
         box-shadow: 2px 2px 2px #cdcdcd !important;
     }
     
     .tag-hover:hover{
         cursor: pointer;
     }
     
     .highlight{
         background-color: rgb(45 64 130 / 60%);
         color: white;
     }
     
     .p-tag.p-tag-danger {
         background-color: #ffffff !important;
         color: #000000 !important;
     }

     .p-tag.p-tag-success {
        background-color: #ff0000 !important;
        color: #fff !important;
    }

    .p-tag {
        background-color: #057203 !important;
        color: #fff !important;
    }

    .p-tag.p-tag-info {
        background-color: #ffff00 !important;
       color: #000000 !important;
   }

    .p-tag.p-tag-warning {
       background-color: #e68a00 !important;
       color: #fff !important;
   }

   .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
    text-align: center !important;
}

.p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
    text-align: center !important;
}
}

.scrollMap{
    position: relative;
    display: flex;
    overflow-x: scroll;
}

.map-legend-container {
  background-color: #f2f2f2;
  height: 80px;
  border:15px;
  width: 100%;
  margin-top:15px;
  display: flex;
  align-items: center; 
}

.map-legend {
  display: flex;
  align-items: center; 
  margin-left: 20px; 
  font-weight: 700;
  font-family: 'Nunito Sans';
}

.map-legend-tile {
  width:20px;
  height:20px;
  border:1px solid #000;
}

.map-legend-text {
  margin-left:7px;
  margin-top:2px;
}