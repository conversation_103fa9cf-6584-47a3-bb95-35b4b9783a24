<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>

    <div class="col-12" >
        <div class="card">
            
            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                
                <div class="w-full">
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <div class="my-2">
                            <h5 class="m-0">Moves By Driver</h5>
                        </div>

                        <div>

                            <p-splitButton  label="Export" [model]="items" (onClick)="exportExcel()" raised severity="help"></p-splitButton>
                   
                        </div>
                    </div>    
            
                
              </div> 
              <ng-template pTemplate="left">
                    <div class="my-2">
                        
                    
                        <span>
                            <!-- <p-select  [showClear]="true"
                                placeholder="Select Client" [options]="clientList"
                                (onChange)="filterJobsByClient($event)" optionLabel="clientName" optionValue="clientId"
                                class="mrgr-10"></p-select> -->

                                <p-autocomplete placeholder="Filter By Client" 
                                [suggestions]="clientList" (onSelect)="filterJobsByClient($event)" dataKey="clientId"
                                (completeMethod)="getClientList($event)" [dropdown]="true" [(ngModel)]="selectedClient"
                                (onClear)="clearClientFilter($event)" class="mrgr-10" field="clientName">
                                <ng-template let-client pTemplate="item">
                                    <div>{{client.clientName}}</div>
                                </ng-template>
                                </p-autocomplete>


                                <!-- <p-autoComplete placeholder="Filter By User" 
                                [suggestions]="drivers" (onSelect)="sortByAssignedUser($event)" dataKey="userId" [(ngModel)]="selectedUser"
                                (completeMethod)="viewUsers($event)" [dropdown]="true"
                                (onClear)="clearUserFilter($event)" class="mrgr-10" field="fullName" appendTo="body">
                                <ng-template let-user pTemplate="item">
                                    <div>{{user.fullName}}</div>
                                </ng-template>
                                </p-autoComplete> -->

                                <p-multiSelect [options]="drivers" [(ngModel)]="selectedUser" placeholder="Filter By User" 
                                    optionLabel="fullName" optionValue="userId" (onChange)="sortByAssignedUser($event)" class="mrgr-10" [filter]="true" [disabled]="isClient">
                                </p-multiSelect>

                                <!-- <p-autocomplete placeholder="Filter By Status"
                                [(ngModel)]="selectedStatus" [suggestions]="filteredStatuses" (completeMethod)="getJobStatusList($event)"
                                [dropdown]="true" (onSelect)="sortByJobstatus($event)" (onClear)="clearStatusFilter($event)"
                                class="mrgr-10" appendTo="body">
                                    <ng-template let-item pTemplate="item">
                                        <div>{{ item }}</div>
                                    </ng-template>
                                </p-autocomplete> -->

                                <p-multiSelect [options]="filteredStatuses" [(ngModel)]="selectedStatus" placeholder="Filter By Status" 
                                    optionLabel="name" optionValue="code" (onChange)="sortByJobstatus($event)" class="mrgr-10" [filter]="true">
                                </p-multiSelect>

                                <!-- <p-select [options]="drivers" [showClear]="true"
                                   optionLabel="fullName" optionValue="userId"
                                   placeholder="Select Driver"
                                   (onChange)="sortByAssignedUser($event)" class="mrgr-10"></p-select>    -->
                        </span>
                    
                    </div>
                </ng-template>
                
                <ng-template pTemplate="right">
                   <div class="block w-50 mt-2 md:mt-0 p-input-icon-left">
                            <span class="mr-2 font-medium">From Date : <input pInputText type="date"
                                    [(ngModel)]="fromDate" (change)="filterJobsByDate()" name="fromDate"
                                    placeholder="Select Date" /></span>
                            <span class="mx-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toDate"
                                    (change)="filterJobsByDate()" name="toDate" placeholder="Select Date" /></span>
                            </div>
                        
                </ng-template> 
            </p-toolbar>

            <p-table showGridlines #dt5 [value]="jobs" [loading]="loading"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"                         
                            [globalFilterFields]="['pickupLocation','pickupSpot','dropLocation','dropSpot']"
                            [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                            <ng-template #header>
                                <tr>
                                                
                                    <!-- <th pSortableColumn="jobNumber">Spot Number</th> -->
                                    <th pSortableColumn="pickupLocation">Pickup Location</th>
                                    <th pSortableColumn="dropLocation">Dropoff Location</th>
                                    <th pSortableColumn="jobCreationTime">Spot Creation Date and Time</th>
                                    <th pSortableColumn="jobPickupTime">Pickup Date and Time</th>
                                    <th pSortableColumn="jobDropoffTime">Dropoff Date and Time</th>
                                    <th pSortableColumn="trailerTruck">Trailer Number</th>
                                    <th pSortableColumn="carrier">Carrier</th>
                                    <th pSortableColumn="duration">Duration</th>
                                    <th pSortableColumn="assignedTo" *ngIf="!isClient">
                                        <span *ngIf="jobstatus != 'Completed'">Assigned To</span>
                                        <span *ngIf="jobstatus == 'Completed'">Completed By</span> 
                                    </th>
                                </tr>
                            </ng-template>
                            <ng-template #body let-job let-columns="columns" let-index="rowIndex">
                                <tr [pReorderableRow]="index">
                                   
                                    <!-- <td>
                                        {{job.jobNumber}}
                                    </td> -->

                                    <td>
                                        {{job.pickupLocation?.locationName}}, {{job.pickupLocation?.state}} - 
                                        <br/>
                                        {{job.pickupSpot?.spotName}}
                                    </td>
                                    
                                    <td>
                                        {{job.dropLocation?.locationName}}, {{job.dropLocation?.state}} - 
                                        <br/>
                                        {{job.dropSpot?.spotName}}
                                    </td>

                                    <td>{{job.createdDate}}</td>

                                    <td>
                                        {{job?.pickupDateTime ? job?.pickupDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>

                                    <td>
                                        {{job?.dropDateTime ? job?.dropDateTime : '-'}}
                                        <!-- <span *ngIf="job?.jobCompletionSeconds">(hh:mm:ss)</span> -->
                                    </td>
                
                                    <td>
                                        {{job.fleet?.unitNumber}}
                                    </td>
                                   
                                    <td>
                                        <!-- <span class="p-column-title">Carrier</span> -->
                                        {{job.fleet?.carrier}}
                                    </td>

                                    <td>
                                        <!-- <span class="p-column-title">Duration</span> -->
                                        {{job?.duration ? job.duration : '-'}}
                                    </td>

                                     <td *ngIf="!isClient">
                                        {{job.assignedTo != null? job.assignedTo?.firstName : "" }} {{job.assignedTo != null? job.assignedTo?.lastName : "" }}
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="16">No spots found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}"
                            [totalRecords]="totalRecords"
                            (onPageChange)="paginate($event)" #paginator></p-paginator>

        </div>
    </div>
</div>
<p-dialog [(visible)]="invalidDateModal" [style]="{width: '450px'}" header="Invalid Date"
    [modal]="true">
    <p>Please select a valid from date and to date</p>
    <ng-template pTemplate="footer">
        <button pButton pRipple label="Ok" icon="pi pi-check" class="p-button-text" (click)="closeAlert()"
            type="button"></button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="sixMonthValidDateModal" [style]="{width: '450px'}" header="Invalid Date"
    [modal]="true">
    <p>Cannot export more than Six months. Please change the dates.</p>
    <ng-template pTemplate="footer">
        <button pButton pRipple label="Ok" icon="pi pi-check" class="p-button-text" (click)="closeAlert()"
            type="button"></button>
    </ng-template>
</p-dialog>


