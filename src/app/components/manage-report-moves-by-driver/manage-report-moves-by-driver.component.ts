import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ManageReportService } from '../manage-report/manage-report.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { TokenService } from 'src/app/security/token.service';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { ManageUsersService } from '../manage-users/manage-users.service';
import { Subscription, map } from 'rxjs';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { Paginator } from 'primeng/paginator';
import { ActivatedRoute } from '@angular/router';
import { AutoCompleteSelectEvent } from 'primeng/autocomplete';

@Component({
  selector: 'app-manage-report-moves-by-driver',
  templateUrl: './manage-report-moves-by-driver.component.html',
  styleUrls: ['./manage-report-moves-by-driver.component.scss'],
  standalone: false
})
export class ManageReportMovesByDriverComponent implements OnInit {
  breadcrumbItems: MenuItem[];
  jobs: [];
  loading = false;
  totalRecords: any;
  query: IpagedQuery;
  userQuery: IpagedQuery;
  userRoles: any;
  clientId: string;
  fromDate: any = '';
  toDate: any;
  assignedToUser: any;
  drivers: any[] = [];
  subscription = new Subscription();
  clientList = [];
  isSupervisor: boolean = false;
  items: MenuItem[];
  accessToken: string;
  firstName: string;
  clientName: string;
  selectedUser: any = [];
  selectedClient: any;
  invalidDateModal: boolean = false;
  sixMonthValidDateModal: boolean = false;
  jobstatus: any = [];
  jobStatusList: string[] = ["Queue", "Open", "In_Transit", "Completed"];
  selectedStatus: any = [];
  filteredStatuses = [
    {name:'All', code: 'ALL'},
    {name:'Queue', code:'QUEUE'},
    {name:'Open', code:'OPEN'},
    {name:'In-Transit', code:'IN_TRANSIT'},
    {name:'Completed', code:'COMPLETED'}
  ];
  lastValidFromDate: any = '';
  lastValidToDate: any;
  isClient: boolean = false;

  @ViewChild('paginator', { static: false }) paginator: Paginator;

  constructor(private manageReportService: ManageReportService,
    private errorService: ErrorService,
    private loader: AppLoaderService,
    private tokenService: TokenService,
    private manageUserService: ManageUsersService,
    private manageClientService: ManageClientsService,
    private activatedRoute: ActivatedRoute) {

    this.items = [{
      label: 'Excel',
      icon: 'pi pi-download',
      command: () => {
        this.exportExcel();
      }
    },
    {
      label: 'PDF',
      icon: 'pi pi-download',
      command: () => {
        this.exportPdf();
      }
    }];

    this.activatedRoute.queryParams.subscribe(qparams => {
      if (qparams["clientId"]) {
        this.clientId = qparams["clientId"];
      }
      if (qparams["userIds"]) {
        this.selectedUser = qparams["userIds"]?.split(',') || [];
      }
      if (qparams["fromDate"]) {
        this.fromDate = qparams["fromDate"]
        this.lastValidFromDate = this.fromDate;
      }
      if (qparams["toDate"]) {
        this.toDate = qparams["toDate"]
        this.lastValidToDate = this.toDate;
      }
      if (qparams["jobstatus"]) {
        this.jobstatus = qparams["jobstatus"]?.split(',') || [];
      }
    })
  }

  ngOnInit(): void {
    this.breadcrumbItems = [];
    this.breadcrumbItems.push({ label: 'Reports', routerLink: '../manage-report' });
    this.breadcrumbItems.push({ label: 'Moves By Driver' });
    this.query = { isActive: true, size: 50, page: 0 };
    this.userQuery = { isActive: true, size: 1000, page: 0 };
    this.getJobs();
    // this.viewUsers();
    //this.getClientList(this.query);
    this.accessToken = this.tokenService.getAccessToken();
    this.getClientList(null, this.clientId);
    this.viewUsers(null);
    this.selectedStatus = this.jobstatus;
    console.log("status",this.selectedStatus);
    
    this.userRoles = this.tokenService.getUserRoles();
    if (this.userRoles.some(role => role === APP_CONSTANTS.USER_ROLES.ROLE_CLIENT)) {
      this.isClient = true;
    }
  }

  private updateCurrentPage(currentPage: number): void {
    setTimeout(() => this.paginator.changePage(currentPage));
  }

  getJobs() {
    this.loading = true;
    let role = "", bucket = "";
    if (this.jobstatus.length == 0 || this.jobstatus == null) {
      this.jobstatus = ["COMPLETED"];
    }

    if (this.jobstatus.includes("QUEUE")) {
      bucket = "BUCKET_DRIVER";
    }
    else {
      if (this.assignedToUser == "" || this.assignedToUser == null) {
        role = "DRIVER,SUPERVISOR";
      }
      else {
        role = ""
      }
    }
    let userList = null;
    if (this.selectedUser.length > 0 && this.selectedUser[0] == "ALL") {
      userList = null;
    } else {
      userList = this.selectedUser;
    }
    this.manageReportService.viewMoves(this.query, this.jobstatus, role, this.clientId, this.fromDate, this.toDate, this.assignedToUser,null,null,bucket, userList)
        .pipe(
          map(jobs => {
            let jobList = [];
            for (let job of jobs.list) {

              let start = new Date(job.pickupDateTime);
              let end = new Date(job.dropDateTime);
              let diffMs = end.getTime() - start.getTime();

              let diffHours = Math.floor(diffMs / (1000 * 60 * 60));
              let diffMinutes = Math.floor((diffMs / (1000 * 60)) % 60);
              let diffSeconds = Math.floor((diffMs / 1000) % 60);

              let str = "";
              if (!isNaN(diffHours)) {
                str += diffHours + "hrs";
              }
              if (!isNaN(diffMinutes)) {
                str += " : " + diffMinutes + "mins";
              }
              if (!isNaN(diffSeconds)) {
                str += " : " + diffSeconds + "sec";
              }
            
              let obj = {
                ...job,
                duration: str
              }
              
              jobList.push(obj);
            }
            return {
              ...jobs,
              list: jobList
            }
          })
        )    
        .subscribe(response => {
        
            this.jobs = response.list;
            this.totalRecords = response.totalElements;
            this.loading = false;

        }, (error) => {
          this.loader.hide();
          this.loading = false;
          this.errorService.handleError(error, true);
        })
  }

  paginate(event) {
    this.query.page = event.page;
    this.getJobs();
  }

  viewUsers(event?, userId?) {

    if (event) {
      this.firstName = event.query;
    }
    this.loader.show();
    this.subscription.add(
      this.manageUserService.viewUsers(this.userQuery, this.clientId, this.firstName, null, null, null, userId).
        pipe(
          map(res => {
            let users = [];
            for (let user of res.list) {
              let obj = {
                ...user,
                fullName: user.firstName + " " + user.lastName
              };
              users.push(obj);
            }
            return users
          })
        ).subscribe(users => {
          let usersList = [];
          users.map(user => {
            for (let role of user["roles"]) {

              if (role.roleName === "DRIVER" || role.roleName === "SUPERVISOR") {
                usersList.push(user);
              }
            }
          })
          this.drivers = usersList;
          this.drivers.unshift({
            userId: 'ALL',
            fullName: 'All'
          });
          // if(userId) {
          //   this.selectedUser = this.drivers[0];
          // }
          this.loader.hide();
        }, (error) => {
          this.loader.hide();
          this.errorService.handleError(error, true);
        })
    )
  }

  getClientList(event?, clientId?) {

    if (event) {
      this.clientName = event.query;
    }
    this.loader.show();
    this.manageClientService.viewClients(this.query, this.clientName, clientId).subscribe(response => {
      this.clientList = response.list;
      if (clientId) {
        this.selectedClient = this.clientList[0];
      }
      this.loader.hide();
      if (this.isSupervisor) {
        this.clientId = this.clientList[0].clientId;
      }
    }, (error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }


  filterJobsByClient(event: AutoCompleteSelectEvent) {
    this.clientId = event.value.clientId;
    this.assignedToUser = '';
    this.selectedUser = [];
    this.viewUsers(null);
    if (this.jobs.length == 0) {
      this.getJobs();
    }
    else {
      this.updateCurrentPage(0);
    }
  }

  filterJobsByDate() {
    if (this.toDate !== undefined) {
      const from = new Date(this.fromDate);
      const to = new Date(this.toDate);

      const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());
      if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
        this.sixMonthValidDateModal = true;
        if (this.lastValidFromDate && this.lastValidToDate) {
          this.fromDate = this.lastValidFromDate;
          this.toDate = this.lastValidToDate;
        }
      }
      else {
        this.lastValidFromDate = this.fromDate;
        this.lastValidToDate = this.toDate;
        if (this.jobs.length == 0) {
          this.getJobs();
        }
        else {
          this.updateCurrentPage(0);
        }
      }
    }
  }

  sortByAssignedUser(event: any) {

    const selected = event.value;

    if (selected.length > 1) {
      if (selected[selected.length - 1] === "ALL") {
        this.selectedUser = [selected[selected.length - 1]];
      } else {
        if (selected.includes('ALL')) {
          this.selectedUser = selected.filter(loc => loc !== 'ALL');
        } else {
          this.selectedUser = selected;
        }
      }
    } else {
      this.selectedUser = selected;
    }

    if (this.jobs.length == 0) {
      this.getJobs();
    }
    else {
      this.updateCurrentPage(0);
    }

  }

  sortByJobstatus(event: AutoCompleteSelectEvent) {
    
    if (event == null) {
      this.jobstatus = [];
    }
    else {
      this.jobstatus = event.value;
    }

    if (this.jobs.length == 0) {
      this.getJobs();
    }
    else {
      this.updateCurrentPage(0);
    }

  }

  exportExcel() {
    if (this.fromDate == '' || this.toDate == '') {
      this.invalidDateModal = true;
    }
    else {
      const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/jobs/moves/export/excel`);
      url.searchParams.append("isActive", "true");
      url.searchParams.append("access_token", this.accessToken);
      //url.searchParams.append("assignedTo.roles.roleName","DRIVER")

      if (this.clientId) {
        url.searchParams.append("pickupLocation.client.uuid", this.clientId);
      }

      if (this.assignedToUser) {
        url.searchParams.append("assignedTo.uuid", this.assignedToUser);
      }

      if (this.fromDate) {
        url.searchParams.append("fromDate", this.fromDate);
      }

      if (this.toDate) {
        url.searchParams.append("toDate", this.toDate);
      }

      let userList = null;
      if (this.selectedUser.length > 0 && this.selectedUser[0] == "ALL") {
        userList = null;
      } else {
        userList = this.selectedUser;
      }

      if (userList) {
        url.searchParams.append("userIds", userList.join(","));
      }



      if (this.jobstatus) {
        url.searchParams.append("status", this.jobstatus.join(","));
        if (this.jobstatus.includes("QUEUE")) {
          url.searchParams.append("bucket", "BUCKET_DRIVER");
        } else {
          url.searchParams.append("roleNames", "DRIVER, SUPERVISOR");
        }
      }
      console.log("url", url.toString());

      window.open(url.toString(), '_blank');
    }

  }

  exportPdf() {

    if (this.fromDate == '' || this.toDate == '') {
      this.invalidDateModal = true;

    } else {

      const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/jobs/moves/export/pdf`);
      url.searchParams.append("isActive", "true");
      url.searchParams.append("access_token", this.accessToken);
      //url.searchParams.append("assignedTo.roles.roleName","DRIVER");
      url.searchParams.append("role", "Driver");


      if (this.clientId) {
        url.searchParams.append("pickupLocation.client.uuid", this.clientId);
      }

      if (this.assignedToUser) {
        url.searchParams.append("assignedTo.uuid", this.assignedToUser);
      }

      if (this.fromDate) {
        url.searchParams.append("fromDate", this.fromDate);
      }

      if (this.toDate) {
        url.searchParams.append("toDate", this.toDate);
      }

      let userList = null;
      if (this.selectedUser.length > 0 && this.selectedUser[0] == "ALL") {
        userList = null;
      } else {
        userList = this.selectedUser;
      }

      if (userList) {
        url.searchParams.append("userIds", userList.join(","));
      }



      if (this.jobstatus) {
        url.searchParams.append("status", this.jobstatus.toString());
        if (this.jobstatus == "QUEUE") {
          url.searchParams.append("bucket", "BUCKET_DRIVER");
        } else {
          url.searchParams.append("roleNames", "DRIVER, SUPERVISOR");
        }
      }

      window.open(url.toString(), '_blank');
    }

  }

  clearUserFilter(event) {

    this.assignedToUser = '';
    this.selectedUser = [];
    this.getJobs();

  }

  clearClientFilter(event) {

    this.clientId = '';
    this.selectedClient = '';
    this.assignedToUser = '';
    this.selectedUser = [];
    this.getJobs();

  }

  closeAlert() {

    this.invalidDateModal = false;
    this.sixMonthValidDateModal = false;
  }

  clearStatusFilter(event) {
    this.selectedStatus = [];
    this.jobstatus = [];
  }

  getJobStatusList(event?, stat?) {
    const query = event.query.toLowerCase();
    // this.filteredStatuses = this.jobStatusList.filter(status =>
    //   status.toLowerCase().includes(query)
    // );
    if (stat) {
      this.selectedStatus = stat;
      this.jobstatus = stat;
    }
  }
}
