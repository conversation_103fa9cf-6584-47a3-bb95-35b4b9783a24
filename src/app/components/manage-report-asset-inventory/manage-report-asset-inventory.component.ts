import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ManageReportService } from '../manage-report/manage-report.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { TokenService } from 'src/app/security/token.service';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { ManageUsersService } from '../manage-users/manage-users.service';
import { Subscription, map} from 'rxjs';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { Paginator } from 'primeng/paginator';
import { ActivatedRoute } from '@angular/router';
import { ManageFleetsService } from '../manage-fleets/manage-fleets.service';
import { ManageLocationsService } from '../manage-locations/manage-locations.service';
import { AutoCompleteSelectEvent } from 'primeng/autocomplete';

@Component({
    selector: 'app-manage-report-asset-inventory',
    templateUrl: './manage-report-asset-inventory.component.html',
    styleUrls: ['./manage-report-asset-inventory.component.scss'],
    standalone: false
})
export class ManageReportAssetInventoryComponent implements OnInit {


  breadcrumbItems: MenuItem[];
  jobs: [];
  loading = false;
  totalRecords: any;
  query: IpagedQuery;
  userQuery: IpagedQuery;
  userRoles: any;
  clientId: string;

  assignedToUser: any;
  drivers: any[];
  subscription = new Subscription();
  clientList = [];
  isSupervisorOrClient: boolean = false;
  items: MenuItem[];
  accessToken: string;
  firstName: string;
  clientName: string;
  selectedUser: any;
  selectedClient: any;

  locationIds:any[];
  locationList = [];
  selectedLocations= [];
  locationId: string;
  selectedLocation: any;
  cols: any[];
  fleetList = [];
  tallyObj: any;
  checkboxDisplay = 'none';
  filterByClient: any;
  searchboxValue:string;
  unitNumberList = [];
  buttonDisable = false;
  cancelButtonDisable = true;
  trailerStatus = [
    {name:'Empty', code: 'EMPTY'},
    {name:'Loaded', code:'FULL'},
    {name:'All', code:'ALL'}
  ];
  selectedTrailerStatus:any=null;
  dropdownItems = [
    { name: 'Truck', code: 'TRUCK' },
    { name: 'Trailer', code: 'TRAILER' },
    { name: 'Container', code: 'CONTAINER' },

  ];
  unitNumber:string;
  //dropdownStatus: { name: string; code: boolean; }[];
  selectedFleetStatus:any=null;
  selectedTrailerAssetStatus:any=null;
  dropdownStatus = [
    {name:"Active",code:true},
    {name:"Inactive",code:false},
  ];
  isITOrAdmin: boolean = false;

  @ViewChild('paginator', { static: false }) paginator: Paginator;

  constructor(private manageReportService : ManageReportService,
              private errorService: ErrorService,
              private loader: AppLoaderService,
              private tokenService: TokenService,
              private manageUserService: ManageUsersService,
              private manageClientService: ManageClientsService,
              private manageFleetsService:ManageFleetsService,
              private manageLocationsService: ManageLocationsService,
              private activatedRoute: ActivatedRoute) {

                this.items = [
                  {
                      label: 'Excel',
                      icon: 'pi pi-download',
                      command: () => {
                          this.exportExcel();
                      }
                  }
                  ,
                  {
                      label: 'PDF',
                      icon: 'pi pi-download',
                      command: () => {
                          this.exportPdf();
                      }
                  }
              ];

              this.activatedRoute.queryParams.subscribe(qparams => {

                if (qparams["clientId"]) {
                  this.clientId = qparams["clientId"];
                }
                if(qparams["locationIds"]) {
                  this.locationIds = qparams["locationIds"]?.split(',') || []
                }
                if (qparams["fleetStatus"] !== undefined) {
                  this.selectedFleetStatus = qparams["fleetStatus"] === 'true';
                }
                if(qparams["trailerStatus"]) {
                  this.selectedTrailerAssetStatus = qparams["trailerStatus"]
                }
              })
    }

  ngOnInit(): void {
    
    this.breadcrumbItems = [];
    this.breadcrumbItems.push({ label: 'Reports',routerLink:'../manage-report'});
    this.breadcrumbItems.push({ label: 'Asset Inventory' });
    this.query = { isActive: true, size: 50, page: 0 };
    this.userQuery = { isActive: true, size: 1000, page: 0 };
    //this.getClientList(this.query);
    this.accessToken = this.tokenService.getAccessToken();
    this.userRoles = this.tokenService.getUserRoles();
    this.isITOrAdmin = this.userRoles.some(
      role => role === APP_CONSTANTS.USER_ROLES.ROLE_IT || role === APP_CONSTANTS.USER_ROLES.ROLE_ADMIN
    );
   
     this.cols = [
      { field: 'unitId', header: 'Unit Id' },
      { field: 'type', header: 'Type' },
      { field: 'make', header: 'Make' },
      { field: 'year', header: 'Year' },
      { field: 'model', header: 'Model' }
    ];
    if(this.clientId!=null && this.clientId!=="ALL"){
      this.getClientList(null, this.clientId);
      this.loadLocations(null);
      let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: 0 };
      if(this.selectedTrailerAssetStatus==="ALL")  
        this.getFleetList(fleetquery,this.clientId,null,null,this.locationIds);
      else
        this.getFleetList(fleetquery,this.clientId,null,this.selectedTrailerAssetStatus,this.locationIds);
    }
    else{
      this.getClientList(null);
      let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: 0 };
      if(this.selectedTrailerAssetStatus==="ALL") 
        this.getFleetList(fleetquery,null,null,null);
      else  
        this.getFleetList(fleetquery,null,null,this.selectedTrailerAssetStatus);
    }
    
  }

  private updateCurrentPage(currentPage: number): void {
    setTimeout(() => this.paginator.changePage(currentPage));
  }

  getFleetList(query: IpagedQuery,clientId?: any,unitNumber?:any,selectedTrailerStatus?:any,locationIds?:string[]) {
   
    this.loading = true;
    this.subscription.add(
      this.manageFleetsService.viewFleets(query,clientId,unitNumber,false,selectedTrailerStatus,locationIds)
      
      .subscribe(response=>{
        this.fleetList = response.list;
        this.totalRecords = response.totalElements;
        this.tallyObj = response.tally;
        this.loading = false;
      },(error) => {
        this.loader.hide();
        this.errorService.handleError(error, true);
      })
    ) 
  }


// filterByLocations(event) {
//   console.log("filterByLocations")
//   this.selectedLocations = [];
// }

// clearLocationFilter(event) {
//   this.locationId = '';
//   this.selectedLocations = [];
// }

filterFleetsByStatus(event) {
  this.selectedFleetStatus = event.value;
  //console.log("filterFleetsByStatus: "+event.value)
  if(event.page==undefined){
    event.page=0;
  }
    if (this.fleetList.length == 0) {
      if(this.clientId!=null && this.clientId!=="ALL"){
        let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
        if(this.selectedTrailerAssetStatus==="ALL")  
          this.getFleetList(fleetquery,this.clientId,null,null,this.selectedLocations);
        else
          this.getFleetList(fleetquery,this.clientId,null,this.selectedTrailerAssetStatus,this.selectedLocations);
      }else{
        let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
        if(this.selectedTrailerAssetStatus==="ALL") 
          this.getFleetList(fleetquery,null,null,null);
        else  
          this.getFleetList(fleetquery,null,null,this.selectedTrailerAssetStatus);
      }
    }
    else {
      this.updateCurrentPage(0);
    }
  
  // let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: 0 };
  // if(this.selectedTrailerAssetStatus==="ALL")  
  //   this.getFleetList(fleetquery,this.clientId,null,null,this.selectedLocations);
  // else
  //   this.getFleetList(fleetquery,this.clientId,null,this.selectedTrailerAssetStatus,this.selectedLocations);
}

filterTrailerStatus(event){
  
  //console.log("filterTrailerStatus: "+event.value)
  if(event.value==null){
    this.selectedTrailerAssetStatus="ALL";
  }
  else{
    this.selectedTrailerAssetStatus = event.value;
  }
  if(event.page==undefined){
    event.page=0;
  }
  if (this.fleetList.length == 0) {
    if(this.clientId!=null && this.clientId!=="ALL"){
      let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
      if(this.selectedTrailerAssetStatus==="ALL")  
        this.getFleetList(fleetquery,this.clientId,null,null,this.selectedLocations);
      else
        this.getFleetList(fleetquery,this.clientId,null,this.selectedTrailerAssetStatus,this.selectedLocations);
    }else{
      let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
      if(this.selectedTrailerAssetStatus==="ALL") 
        this.getFleetList(fleetquery,null,null,null);
      else  
        this.getFleetList(fleetquery,null,null,this.selectedTrailerAssetStatus);
    }
  }
  else {
    this.updateCurrentPage(0);
  }
}

loadLocations(event?: AutoCompleteSelectEvent) {
  if (event?.value.clientId) {
    this.clientId = event.value.clientId;
    this.selectedLocations = [];
    this.locationList =[];
  }
  let selClientID=this.clientId;
  if(this.clientId=="ALL"){
    selClientID=null;
  }
  else{
    this.loading = true;
    this.subscription.add(
      this.manageLocationsService.viewLocations(this.query, selClientID, null, '').subscribe(
        response => {
          this.locationList = response.list || [];
          this.totalRecords = response.totalElements;
          this.loading = false;

          if (this.locationIds?.length && event==null) {
            this.selectedLocations = this.locationIds.filter(id =>
              this.locationList.some(loc => loc.locationId === id)
            );
          }
          else{
            this.selectedLocations = [];
          }
        },
        error => {
          this.loading = false;
          this.errorService.handleError(error, true);
        }
      )
    );
  }
  if(event!=null){
    this.selectedLocations = [];
    
    let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: 0 };
    if(this.selectedTrailerAssetStatus==="ALL")  
      this.getFleetList(fleetquery,selClientID,null,null,null);
    else
      this.getFleetList(fleetquery,selClientID,null,this.selectedTrailerAssetStatus,null);
  }
}

filterFleetsByLocationIds(event) {
  this.selectedLocations = event.value;
  this.locationIds=event.value;
  
  //console.log("filterFleetsByLocationIds: "+event.value)
  if(event.page==undefined){
    event.page=0;
  }
  if(event.value!=null){
    if (this.fleetList.length == 0) {
      if(this.clientId!=null && this.clientId!=="ALL"){
        let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
        if(this.selectedTrailerAssetStatus==="ALL")  
          this.getFleetList(fleetquery,this.clientId,null,null,this.selectedLocations);
        else
          this.getFleetList(fleetquery,this.clientId,null,this.selectedTrailerAssetStatus,this.selectedLocations);
      }else{
        let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
        if(this.selectedTrailerAssetStatus==="ALL") 
          this.getFleetList(fleetquery,null,null,null);
        else  
          this.getFleetList(fleetquery,null,null,this.selectedTrailerAssetStatus);
      }
    }
    else {
      this.updateCurrentPage(0);
    }
  }
}

  cancel()
  {
    this.getFleetList(this.query,this.filterByClient,this.searchboxValue);
    this.checkboxDisplay = 'none';
    this.unitNumberList=[];
    this.buttonDisable=false;
    this.cancelButtonDisable=true;
    this.selectedLocations = [];
  }
    

    paginate(event) {
      //this.query.page = event.page;
      //console.log("paginate asset: "+event.page)
      if(event.page==undefined){
        event.page=0;
      }
          if(this.clientId!=null && this.clientId!=="ALL"){
            let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
            if(this.selectedTrailerAssetStatus==="ALL")  
              this.getFleetList(fleetquery,this.clientId,null,null,this.selectedLocations);
            else
              this.getFleetList(fleetquery,this.clientId,null,this.selectedTrailerAssetStatus,this.selectedLocations);
          }else{
            let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
            if(this.selectedTrailerAssetStatus==="ALL") 
              this.getFleetList(fleetquery,null,null,null);
            else  
              this.getFleetList(fleetquery,null,null,this.selectedTrailerAssetStatus);
          }
       
    }

      getClientList(event?,clientId?) {
        if(event) {
          this.clientName = event.query;
        }
        this.loader.show();           
        this.manageClientService.viewClients(this.query, this.clientName, clientId).subscribe(response => {
          this.clientList = response.list;
          if(clientId) {
            this.selectedClient = this.clientList[0];
          }
          
          this.loader.hide();
          if(this.isSupervisorOrClient)
          {
            this.clientId = this.clientList[0].clientId;
            this.selectedClient = this.clientList[0];
          }
          if (this.isITOrAdmin) {
            this.clientList.unshift({
              clientId: 'ALL',
              clientName: 'All Clients'
            });
            if(this.clientId=="ALL"){
              this.selectedClient = this.clientList[0];
            }
          }
        }, (error) => {
          this.loader.hide();
          this.errorService.handleError(error, true);
        })
      }


      filterJobsByClient(event) {
        //console.log("filterFleetsByLocationIds: "+event.value+" event.clientId: "+event.clientId)
        this.clientId = event.clientId;
        if(event.page==undefined){
          event.page=0;
        }
        if(event.value!=null){
          if (this.fleetList.length == 0) {
            if(this.clientId!=null && this.clientId!=="ALL"){
              let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
              if(this.selectedTrailerAssetStatus==="ALL")  
                this.getFleetList(fleetquery,this.clientId,null,null,this.selectedLocations);
              else
                this.getFleetList(fleetquery,this.clientId,null,this.selectedTrailerAssetStatus,this.selectedLocations);
            }else{
              let fleetquery = { isActive: this.selectedFleetStatus, size: 50, page: event.page };
              if(this.selectedTrailerAssetStatus==="ALL") 
                this.getFleetList(fleetquery,null,null,null);
              else  
                this.getFleetList(fleetquery,null,null,this.selectedTrailerAssetStatus);
            }
          }
          else {
            this.updateCurrentPage(0);
          }
        }
      } 
    

      exportExcel(){
        //console.log("this.selectedLocations: "+this.selectedLocations)
      
          const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/fleets/export/excel`);
          url.searchParams.append("isActive", this.selectedFleetStatus);
          url.searchParams.append("access_token", this.accessToken);
          
          if (this.clientId!=null && this.clientId!=="ALL") {
              url.searchParams.append("clients.uuid", this.clientId);
          }

          if(this.selectedLocations) {
            url.searchParams.append("locationIds", this.selectedLocations.join(","));
          }

          if(this.selectedTrailerAssetStatus!=null && this.selectedTrailerAssetStatus!="ALL") {
            url.searchParams.append("fleetStatus", this.selectedTrailerAssetStatus);
          }
          
          window.open(url.toString(), '_blank');
       } 

      exportPdf() {

        const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/fleets/export/pdf`);
          url.searchParams.append("isActive", this.selectedFleetStatus);
          url.searchParams.append("access_token", this.accessToken);
          
          if (this.clientId!=null && this.clientId!=="ALL") {
            url.searchParams.append("clients.uuid", this.clientId);
          }

          if(this.selectedLocations) {
            url.searchParams.append("locationIds", this.selectedLocations.join(","));
          }

          if(this.selectedTrailerAssetStatus!=null && this.selectedTrailerAssetStatus!="ALL") {
            url.searchParams.append("fleetStatus", this.selectedTrailerAssetStatus);
          }
          
          window.open(url.toString(), '_blank');
      }

      clearUserFilter(event) {
      
          this.assignedToUser = '';
          this.selectedUser = '';
          
      }

      clearClientFilter(event) {
      
        this.clientId = '';
        this.selectedClient = '';
        this.selectedLocations = [];
    }

}
