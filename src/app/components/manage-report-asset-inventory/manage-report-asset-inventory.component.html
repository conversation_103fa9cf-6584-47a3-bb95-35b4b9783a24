<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>

    <div class="col-12" >
        <div class="card">
            
            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <div class="w-full">
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <div class="my-2">
                            <h5 class="m-0">Asset Inventory Report</h5>
                        </div>

                        <div>

                            <p-splitButton  label="Export" [model]="items" (onClick)="exportExcel()" raised severity="help"></p-splitButton>
                   
                        </div>
                    </div>    
            
                <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center mrgt-30">
                    
                        <span>
                            <!-- <p-select  [showClear]="true"
                                placeholder="Select Client" [options]="clientList"
                                (onChange)="filterJobsByClient($event)" optionLabel="clientName" optionValue="clientId"
                                class="mrgr-10"></p-select> -->

                                <p-autocomplete placeholder="Filter By Client" 
                                [suggestions]="clientList" (onSelect)="loadLocations($event)" dataKey="clientId"
                                (completeMethod)="getClientList($event)" [dropdown]="true" [(ngModel)]="selectedClient"
                                (onClear)="clearClientFilter($event)" class="mrgr-10" field="clientName">
                                <ng-template let-client pTemplate="item">
                                    <div>{{client.clientName}}</div>
                                </ng-template>
                                </p-autocomplete>

                                <p-multiSelect [options]="locationList" [(ngModel)]="selectedLocations" 
                                placeholder="Filter By Locations"  optionLabel="locationName" optionValue="locationId"
                                    class="mrgr-10" [filter]="true" (onChange)="filterFleetsByLocationIds($event)"
                                    *ngIf="clientId !== 'ALL'">
                                </p-multiSelect> 

                                <p-select [options]="dropdownStatus" placeholder="Select Asset Status" optionLabel="name" optionValue="code"
                                    (onChange)="filterFleetsByStatus($event)" [(ngModel)]="selectedFleetStatus"></p-select>
                                                    
                                <p-select class="ml-2" [options]="trailerStatus" placeholder="Select Trailer Status" optionLabel="name" 
                                optionValue="code" (onChange)="filterTrailerStatus($event)" [(ngModel)]="selectedTrailerAssetStatus"></p-select>

                        </span>
                    
                  
                        <!-- <span class="block mt-2 md:mt-0 p-input-icon-left">
                            <span class="mr-2 font-medium">From Date : <input pInputText type="date"
                                    [(ngModel)]="fromDate"  name="fromDate" (change)="filterJobsByDate()"
                                    placeholder="Select Date" /></span>
                            <span class="mx-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toDate"
                                     name="toDate" placeholder="Select Date"  (change)="filterJobsByDate()"/></span>
                        </span> -->
                    
                    
                </div>
              </div>  
            </p-toolbar>

                            <p-table showGridlines #dt [value]="fleetList"
                            [globalFilterFields]="['carrier','unitNumber','owner','type','fleetStatus']" [loading]="loading"
                            styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                            [columns]="cols" [rows]="10" [rowHover]="true" dataKey="id">

                            <ng-template #header>
                                <tr>
                                                
                                    <!-- <th pSortableColumn="jobNumber">Spot Number</th> -->
                                    <th pSortableColumn="type">Type</th>
                                    <th pSortableColumn="unitNumber">Unit Number</th>
                                    <th pSortableColumn="carrier">Carrier</th>
                                    <th pSortableColumn="trailerStatus">Trailer status</th>
                                    <th pSortableColumn="spotName">Location</th>
                                    <th pSortableColumn="notes">Notes</th>
                                    <th pSortableColumn="createdBy">Created By</th>
                            
                                </tr>
                            </ng-template>
                            <ng-template #body let-fleet>
                                <tr>
                                    
                                    <td>
                                        {{fleet.type}}
                                    </td>
                                   
                                    <td>
                                        {{fleet.unitNumber}}
                                    </td>
                                    <td>
                                        {{fleet.carrier}}
                                    </td>
                                   
                                    <td>
                                        {{fleet.fleetStatus && fleet.fleetStatus=='FULL' ? 'LOADED' : '' }}
                                        {{fleet.fleetStatus && fleet.fleetStatus=='EMPTY' ? 'EMPTY' : '' }}
                                    </td>
                                    <td>
                                        {{fleet.spot? fleet.spot?.locationName: '' }}
                                        <br>
                                        {{fleet.spot? fleet.spot?.spotName : '-' }}
                                    </td>
                                    <td>
                                        {{fleet.remarks ? fleet.remarks : '-' }}
                                    </td>
                                    <td>
                                        {{fleet.audit ? fleet.audit.createdBy.firstName : '-' }}
                                        {{fleet.audit ? fleet.audit.createdBy.lastName : '-' }}
                                    </td>
                                    
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="7">Truck-Trailer-Container not found.</td>
                                </tr>
                            </ng-template>
                        </p-table>
                        <p-paginator [rows]="50" [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords}" [totalRecords]="totalRecords"
                            (onPageChange)="paginate($event)" #paginator></p-paginator>

        </div>
    </div>
</div>



