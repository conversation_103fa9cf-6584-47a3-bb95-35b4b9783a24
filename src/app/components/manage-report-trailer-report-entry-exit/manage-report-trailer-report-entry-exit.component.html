<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>

    <div class="col-12">
        <div class="card">

            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <div class="w-full">
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <div class="my-2">
                            <h5 class="m-0">Trailer Report by Entry and Exit</h5>
                        </div>

                        <div>

                            <p-splitButton label="Export" [model]="items" (onClick)="exportExcel()" raised
                                severity="help"></p-splitButton>

                        </div>
                    </div>

                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center mrgt-30">

                        <span>
                            <!-- <p-select  [showClear]="true"
                                placeholder="Select Client" [options]="clientList"
                                (onChange)="filterEntryExitByClient($event)" optionLabel="clientName" optionValue="clientId"
                                class="mrgr-10"></p-select> -->



                            <p-autocomplete placeholder="Filter By Client" [suggestions]="clientList"
                                (onSelect)="filterEntryExitByClient($event)" dataKey="clientId"
                                [(ngModel)]="selectedClient" (completeMethod)="getClientList($event)" [dropdown]="true"
                                (onClear)="clearClientFilter($event)" class="mrgr-10" field="clientName">
                                <ng-template let-client pTemplate="item">
                                    <div>{{client.clientName}}</div>
                                </ng-template>
                            </p-autocomplete>

                            <p-autocomplete placeholder="Filter By Location" [suggestions]="locationList"
                                (onSelect)="filterEntryExitByLocation($event)" dataKey="locationId"
                                [(ngModel)]="selectedLocation" (completeMethod)="getClientLocations($event)"
                                [dropdown]="true" (onClear)="clearLocationFilter($event)" class="mrgr-10"
                                field="locationName">
                                <ng-template let-location pTemplate="item">
                                    <div>{{location.locationName}}</div>
                                </ng-template>
                            </p-autocomplete>


                            <!-- <p-select  [showClear]="true"
                                placeholder="Select Location" [options]="locationList"
                                (onChange)="filterEntryExitByLocation($event)" optionLabel="locationName" optionValue="locationId"
                                class="mrgr-10"></p-select> -->

                            <!-- <p-select  [showClear]="true"
                                placeholder="Select Type" [options]="Types"
                                (onChange)="filterEntryExitByType($event)" optionLabel="code" optionValue="value"
                                class="mrgr-10"></p-select> -->



                        </span>


                        <span class="block mt-2 md:mt-0 p-input-icon-left">
                            <span class="mr-2 font-medium">From Date : <input pInputText type="date"
                                    [(ngModel)]="fromDate" (change)="filterEntryExitByDate()" name="fromDate"
                                    placeholder="Select Date" /></span>
                            <span class="mx-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toDate"
                                    (change)="filterEntryExitByDate()" name="toDate" placeholder="Select Date" /></span>
                        </span>


                    </div>
                    <div class="mt-2">
                        <p-select [showClear]="true" placeholder="Select Type" [options]="Types"
                            [(ngModel)]="selectedType" (onChange)="filterEntryExitByType($event)" optionLabel="code"
                            optionValue="value" class="mrgr-10"></p-select>
                    </div>
                </div>
            </p-toolbar>
            <p-table showGridlines #dt [value]="entryExitList" [loading]="loading"
                styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true"
                [globalFilterFields]="['location.locationName','dateOfEntry','fleet.carrier','fleet.unitNumber','fleet.owner','type','entryTime','audit.createdBy.firstName']"
                [rows]="10" [paginator]="false" [rowHover]="true" dataKey="id" [responsiveLayout]="'scroll'">

                <ng-template #header>
                    <tr>
                        <th pSortableColumn="locations">Trailer #</th>
                        <th pSortableColumn="dateOfEntry">Location</th>
                        <th pSortableColumn="carrier">Tractor#</th>
                        <th pSortableColumn="unitNumber">Carrier</th>
                        <th pSortableColumn="owner">Supplier</th>
                        <th pSortableColumn="typeOfEntry">Sequence #</th>
                        <th pSortableColumn="trailer">Load Status</th>
                        <th pSortableColumn="entryTime">Arrival Date</th>
                        <th pSortableColumn="exitTime">Pickup Date</th>
                        <th pSortableColumn="reportBy">Driver name</th>
                        <!-- <th pSortableColumn="remarks">Remarks <p-sortIcon field="remarks"></p-sortIcon></th> -->
                    </tr>
                </ng-template>
                <ng-template #body let-entry>
                    <tr>
                        <td>
                            {{entry?.fleet?.unitNumber}}
                        </td>
                        <td>
                            {{entry?.location?.locationName}}, {{entry?.spot?.spotName}}

                        </td>
                        <td>
                            {{ entry?.tractorNumber }}
                        </td>
                        <td>
                            {{entry?.carrier}}
                        </td>
                        <td>
                            {{entry?.supplier}}
                        </td>
                        <td>
                            {{entry?.sequenceNumber}}
                        </td>
                        <td>
                            {{entry?.loadStatus}}
                        </td>
                        <td>
                            {{ entry?.dateOfArrival }}
                        </td>
                        <td>
                            {{ entry?.dateOfPickup}}
                        </td>
                        <td>
                            {{ entry?.driver}}
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="9">No entry exit record found.</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-paginator [rows]="10" [showCurrentPageReport]="true"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords}" [totalRecords]="totalRecords"
                (onPageChange)="paginate($event)" #paginator></p-paginator>

        </div>
    </div>
</div>
<p-dialog [(visible)]="invalidDateModal" [style]="{width: '450px'}" header="Invalid Date"
    [modal]="true">
    <p>Please select a valid from date and to date</p>
    <ng-template #footer>
        <button pButton pRipple label="Ok" icon="pi pi-check" class="p-button-text" (click)="closeAlert()"
            type="button"></button>
    </ng-template>
</p-dialog>

<p-dialog [(visible)]="sixMonthValidDateModal" [style]="{width: '450px'}" header="Invalid Date"
    [modal]="true">
    <p>Cannot export more than Six months. Please change the dates.</p>
    <ng-template pTemplate="footer">
        <button pButton pRipple label="Ok" icon="pi pi-check" class="p-button-text" (click)="closeAlert()"
            type="button"></button>
    </ng-template>
</p-dialog>