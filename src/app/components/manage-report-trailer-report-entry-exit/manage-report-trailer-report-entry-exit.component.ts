import { Component, OnInit, ViewChild } from '@angular/core';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { ManageEntryExitService } from '../manage-entry-exit/manage-entry-exit.service';
import { map, Subscription } from 'rxjs';
import moment from 'moment';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { MenuItem } from 'primeng/api';
import { ManageUsersService } from '../manage-users/manage-users.service';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { Paginator } from 'primeng/paginator';
import { ManageLocationsService } from '../manage-locations/manage-locations.service';
import { EntryExit, EntryExitType } from 'src/app/model/Report';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { TokenService } from 'src/app/security/token.service';
import { ActivatedRoute } from '@angular/router';
import { AutoCompleteSelectEvent } from 'primeng/autocomplete';


@Component({
    selector: 'app-manage-report-trailer-report-entry-exit',
    templateUrl: './manage-report-trailer-report-entry-exit.component.html',
    styleUrls: ['./manage-report-trailer-report-entry-exit.component.scss'],
    standalone: false
})
export class ManageReportTrailerReportEntryExitComponent implements OnInit {

  loading: boolean;
  entryExitList = [];
  totalRecords: any;
  breadcrumbItems: MenuItem[];
  query : IpagedQuery;
  filterByClient: any;
  drivers: any[];
  subscription = new Subscription();
  clientList = [];
  clientId: string;
  isSupervisor: boolean = false;
  locationList = [];
  locationId: string;
  selectedType: string;
  Types: EntryExit[] =[{code:'Entry',value:EntryExitType.Entry},{code:'Exit',value:EntryExitType.Exit}];
  fromDate: any;
  toDate: any;
  items: MenuItem[];
  accessToken: string;
  clientName: string;
  locationName: string;
  selectedLocation: any;
  selectedClient: any;
  invalidDateModal: boolean = false;
  sixMonthValidDateModal: boolean = false;
  lastValidFromDate: any= '';
  lastValidToDate: any;

  @ViewChild('paginator', { static: false }) paginator: Paginator;

  constructor(private manageEntryExitService:ManageEntryExitService,
              private loader:AppLoaderService,
              private errorService:ErrorService,
              private tokenService: TokenService,
              private manageClientService: ManageClientsService,
              private manageLocationsService: ManageLocationsService,
              private activatedRoute: ActivatedRoute
        
  ) {

    this.items = [
      {
          label: 'Excel',
          icon: 'pi pi-download',
          command: () => {
              this.exportExcel();
          }
      }
      ,
      {
          label: 'PDF',
          icon: 'pi pi-download',
          command: () => {
              this.exportPdf();
          }
      }
  ];


  this.activatedRoute.queryParams.subscribe(qparams => {

    if (qparams["clientId"]) {
      this.clientId = qparams["clientId"];
    }
    if(qparams["locationId"]) {
      this.locationId = qparams["locationId"]
    }
    if(qparams["type"]) {
      this.selectedType = qparams["type"]
    }
    if(qparams["fromDate"]) {
      this.fromDate = qparams["fromDate"]
      this.lastValidFromDate=this.fromDate;
    }
    if(qparams["toDate"]) {
      this.toDate = qparams["toDate"]
      this.lastValidToDate=this.toDate;
    }  
  })
   }

  ngOnInit(): void {

    this.breadcrumbItems = [];
    this.breadcrumbItems.push({ label: 'Reports',routerLink:'../manage-report'});
    this.breadcrumbItems.push({ label: 'Trailer Report by Entry and Exit' });
    this.query = { isActive: true, size: 10, page: 0 };
   
    const today = new Date();
    const yyyy = today.getFullYear();
    const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const dd = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${yyyy}-${mm}-${dd}`;
    this.toDate = formattedDate;

    this.getEntryExit();
    // this.getClientList(this.query);
    this.accessToken = this.tokenService.getAccessToken();
    if(this.clientId != null) {
      this.getClientList(null, this.clientId);
    }
    if(this.locationId != null) {
      this.getClientLocations(null,this.locationId);
    }
  }

  private updateCurrentPage(currentPage: number): void {
    setTimeout(() => this.paginator.changePage(currentPage));
  }

  getEntryExit(){
    
    this.loading = true;
    this.entryExitList = [];
    this.manageEntryExitService.getEntryExitList(this.query,this.clientId, this.locationId, this.selectedType, this.fromDate, this.toDate)
    .pipe(
      map(res=>{
        let entryExitList = [];
        for(let entryExit of res.list){
            let obj = {
                ...entryExit,
                dateOfEntry : moment.utc(entryExit.audit.createdDate).format("MM/DD/YYYY"),
                entryTime : moment.utc(entryExit.audit.createdDate).format("hh:mm A")
              };
              entryExitList.push(obj);
        }
        return { 
          list : entryExitList,
          totalElements : res.totalElements
        }
      })
        
    )
    .subscribe(response=>{
      this.entryExitList = response.list;
      this.totalRecords = response.totalElements;
      this.loading = false;
    },(error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }

  paginate(event){
    this.query.page = event.page;
    this.getEntryExit();
  }

  getClientList(event?,clientId?) {

    if(event) {
      this.clientName = event.query;
    }
    
    this.loader.show();
    this.manageClientService.viewClients(this.query, this.clientName, clientId).subscribe(response => {
      this.clientList = response.list;
      if(clientId) {
        this.selectedClient = this.clientList[0];
      }
      this.loader.hide();
      if(this.isSupervisor)
      {
        this.clientId = this.clientList[0].clientId;
      }
    }, (error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }

  filterEntryExitByClient(event: AutoCompleteSelectEvent) {
    this.clientId = event.value.clientId;
    if(this.clientId != null) {
      // this.getClientLocations();
      this.locationId = '';
      this.selectedLocation = '';
    }  
    if (this.entryExitList.length == 0) {
      this.getEntryExit();
    }
    else {
      this.updateCurrentPage(0);
    }
  }

  filterEntryExitByLocation(event: AutoCompleteSelectEvent) {
      this.locationId = event.value.locationId;
      if (this.entryExitList.length == 0) {
        this.getEntryExit();
      }
      else {
        this.updateCurrentPage(0);
      }
  }

  filterEntryExitByType(event) {

    this.selectedType = event.value;
    if(this.entryExitList.length == 0) {
      this.getEntryExit();
    }
    else {
      this.updateCurrentPage(0);
    }
  }

  filterEntryExitByDate() {
    if (this.toDate !== undefined) {
      const from = new Date(this.fromDate);
      const to = new Date(this.toDate);

      const monthDiff = (to.getFullYear() - from.getFullYear()) * 12 + (to.getMonth() - from.getMonth());
      if (monthDiff > 6 || (monthDiff === 6 && to.getDate() > from.getDate())) {
        this.sixMonthValidDateModal = true;
        if (this.lastValidFromDate && this.lastValidToDate) {
          this.fromDate = this.lastValidFromDate;
          this.toDate = this.lastValidToDate;
        }
      }
      else{
        this.lastValidFromDate = this.fromDate;
        this.lastValidToDate = this.toDate;
        if(this.entryExitList.length == 0) {
          this.getEntryExit();
        }
        else {
          this.updateCurrentPage(0);
        }
      }
    }
  }
  
  
  getClientLocations(event?, locationId?) {

    if(event) {
      this.locationName = event.query;
    }
    this.loading = true;
    this.subscription.add(
        this.manageLocationsService.viewLocations(this.query,this.clientId,null,this.locationName,locationId).subscribe(response=>{
            this.locationList = response.list;
            if(locationId) {
              this.selectedLocation = this.locationList[0];
            }
            this.totalRecords = response.totalElements;
            this.loading = false;
        },(error) => {
            this.loader.hide();
            this.errorService.handleError(error, true);
        })
    )
}

exportExcel(){
        
  if(this.fromDate == '' || this.toDate == '') {
            
    this.invalidDateModal = true;
  }else {

    const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/fleets/entryExits/report/export/excel`);
    url.searchParams.append("isActive", "true");
    url.searchParams.append("access_token", this.accessToken);
    url.searchParams.append("assignedTo.roles.roleName","DRIVER")
    
   if (this.clientId) {
       url.searchParams.append("location.client.uuid", this.clientId);
  }
  
   if(this.selectedType) {
    url.searchParams.append("type",this.selectedType)
  }
  
  if(this.locationId) {
    url.searchParams.append("location.uuid", this.locationId)
  }
  
   if (this.fromDate) {
       url.searchParams.append("fromDate", this.fromDate);
   }
   
   if (this.toDate) {
       url.searchParams.append("toDate", this.toDate);
   }
  
    window.open(url.toString(), '_blank');
  }
  
} 

exportPdf() {


  if(this.fromDate == '' || this.toDate == '') {
            
    this.invalidDateModal = true;
  }else {

  const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/fleets/entryExits/report/export/pdf`);
  url.searchParams.append("isActive", "true");
  url.searchParams.append("access_token", this.accessToken);
  url.searchParams.append("assignedTo.roles.roleName","DRIVER")
  
 if (this.clientId) {
     url.searchParams.append("location.client.uuid", this.clientId);
}

 if(this.selectedType) {
  url.searchParams.append("type",this.selectedType)
}

if(this.locationId) {
  url.searchParams.append("location.uuid", this.locationId)
}

 if (this.fromDate) {
     url.searchParams.append("fromDate", this.fromDate);
 }
 
 if (this.toDate) {
     url.searchParams.append("toDate", this.toDate);
 }

  window.open(url.toString(), '_blank');
}
}

clearClientFilter(event) {
 
  this.selectedClient = '';
  this.clientId = '';
  this.locationId = '';
  this.selectedLocation = '';
  this.getEntryExit();
}

clearLocationFilter(event) {

  this.locationId = '';
  this.selectedLocation = '';
  this.getEntryExit();
}

closeAlert() {

  this.invalidDateModal = false;
  this.sixMonthValidDateModal = false;
}

}
