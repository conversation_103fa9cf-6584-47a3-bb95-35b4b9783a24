import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { Paginator } from 'primeng/paginator';
import { map, Subscription } from 'rxjs';
import { IpagedQuery } from 'src/app/model/IpagedQuery';
import { ManageReportService } from '../manage-report/manage-report.service';
import { ErrorService } from 'src/app/error-handling/error.service';
import { AppLoaderService } from 'src/app/app-loader/service/app-loader.service';
import { TokenService } from 'src/app/security/token.service';
import { ManageUsersService } from '../manage-users/manage-users.service';
import { ManageClientsService } from '../manage-clients/manage-clients.service';
import { ManageFleetsService } from '../manage-fleets/manage-fleets.service';
import { ManageLocationsService } from '../manage-locations/manage-locations.service';
import { ActivatedRoute } from '@angular/router';
import { APP_CONSTANTS } from 'src/app/constants/app.constants';
import { ManageSpotsService } from '../manage-spots/manage-spots.service';
import { AutoCompleteSelectEvent } from 'primeng/autocomplete';

@Component({
  selector: 'app-manage-report-trailer-audit',
  templateUrl: './manage-report-trailer-audit.component.html',
  styleUrls: ['./manage-report-trailer-audit.component.scss'],
  standalone: false
})
export class ManageReportTrailerAuditComponent implements OnInit {

  breadcrumbItems: MenuItem[];
    jobs: [];
    loading = false;
    totalRecords: any = 0;
    query: IpagedQuery;
    userQuery: IpagedQuery;
    userRoles: any;
    clientId: string;
  
    assignedToUser: any;
    drivers: any[];
    subscription = new Subscription();
    clientList = [];
    isSupervisorOrClient: boolean = false;
    items: MenuItem[];
    accessToken: string;
    firstName: string;
    clientName: string;
    selectedUser: any;
    selectedClient: any;
  
    locationIds:any[];
    locationList = [];
    selectedLocations= [];
    locationId: string;
    selectedLocation: any;
    cols: any[];
    fleetList = [];
    spotsArray = [];
    tallyObj: any;
    checkboxDisplay = 'none';
    filterByClient: any;
    searchboxValue:string;
    unitNumberList = [];
    buttonDisable = false;
    cancelButtonDisable = true;
    trailerStatus = [
      {name:'Empty', code: 'EMPTY'},
      {name:'Loaded', code:'FULL'},
      {name:'All', code:'ALL'}
    ];
    selectedTrailerStatus:any=null;
    dropdownItems = [
      { name: 'Truck', code: 'TRUCK' },
      { name: 'Trailer', code: 'TRAILER' },
      { name: 'Container', code: 'CONTAINER' },
  
    ];
    unitNumber:string;
    //dropdownStatus: { name: string; code: boolean; }[];
    selectedLastUpdated:any=null;
    selectedTrailerAssetStatus:any=null;
    dropdownStatus = [
      {name:"Active",code:true},
      {name:"Inactive",code:false},
    ];
    isITOrAdmin: boolean = false;
    clientErrorModal: boolean = false;

    @ViewChild('paginator', { static: false }) paginator: Paginator;
  
    constructor(private manageReportService : ManageReportService,
                private errorService: ErrorService,
                private loader: AppLoaderService,
                private tokenService: TokenService,
                private manageUserService: ManageUsersService,
                private manageClientService: ManageClientsService,
                private manageFleetsService:ManageFleetsService,
                private manageLocationsService: ManageLocationsService,
                private activatedRoute: ActivatedRoute,
                private manageSpotsServie: ManageSpotsService,
            ) {
  
                  this.items = [
                    {
                        label: 'Excel',
                        icon: 'pi pi-download',
                        command: () => {
                            this.exportExcel();
                        }
                    }
                    ,
                    {
                        label: 'PDF',
                        icon: 'pi pi-download',
                        command: () => {
                            this.exportPdf();
                        }
                    }
                ];
  
                this.activatedRoute.queryParams.subscribe(qparams => {
  
                  if (qparams["clientId"]) {
                    this.clientId = qparams["clientId"];
                  }
                  if(qparams["locationIds"]) {
                    this.locationIds = qparams["locationIds"]?.split(',') || []
                  }
                  // if (qparams["lastUpdated"]) {
                  //   this.selectedLastUpdated = qparams["lastUpdated"];
                  // }
                  if(qparams["trailerStatus"]) {
                    this.selectedTrailerAssetStatus = qparams["trailerStatus"]
                  }
                })
      }
  
    ngOnInit(): void {
      
      this.breadcrumbItems = [];
      this.breadcrumbItems.push({ label: 'Reports',routerLink:'../manage-report'});
      this.breadcrumbItems.push({ label: 'Trailer Audit Report' });
      this.query = { isActive: true, size: 50, page: 0 };
      this.userQuery = { isActive: true, size: 1000, page: 0 };
      //this.getClientList(this.query);
      this.accessToken = this.tokenService.getAccessToken();
      this.userRoles = this.tokenService.getUserRoles();
      this.isITOrAdmin = this.userRoles.some(
        role => role === APP_CONSTANTS.USER_ROLES.ROLE_IT || role === APP_CONSTANTS.USER_ROLES.ROLE_ADMIN
      );
     
      //  this.cols = [
      //   { field: 'unitId', header: 'Unit Id' },
      //   { field: 'type', header: 'Type' },
      //   { field: 'make', header: 'Make' },
      //   { field: 'year', header: 'Year' },
      //   { field: 'model', header: 'Model' }
      // ];
      if(this.clientId!=null){
        this.getClientList(null, this.clientId);
        this.selectedLocations = this.locationIds;
        this.loadLocations(null);
        if(this.locationIds.length == 1){
          if(this.locationIds[0] == "ALL"){
            this.getAllSpots();
          }else{
            this.getSpots();
          }
        }else{
          this.getSpots();
        }
      }
      
    }
  
    private updateCurrentPage(currentPage: number): void {
      setTimeout(() => this.paginator.changePage(currentPage));
    }
  
    getSpots(){
          // if (!this.clientId || !this.selectedLocation) {
          //   return;
          // }
    
          // console.log('calling:::::::: ');
          this.loader.show();
          let fleetStatus = null;
          if(this.selectedTrailerAssetStatus != "ALL"){
           fleetStatus = this.selectedTrailerAssetStatus;
          }

          this.manageSpotsServie.viewSpots(this.query, this.clientId, null, null, fleetStatus, this.selectedLocations, this.selectedLastUpdated)
            .pipe(
              map(res => {
                let spotsArray = [];
                for (let spot of res.list) {
                  let inTransit = [];
                 
                  let obj = {
                    ...spot,
                    fleetIdIfExist: spot.fleet != null ? { "unitNumber" : spot.fleet.unitNumber, "fleetId": spot.fleet.fleetId} : '',
                    fleetStatusIfExist: spot.fleet != null ? spot.fleet.fleetStatus : '',
                  }
                  spotsArray.push(obj);
                }
                this.totalRecords = res.totalElements;
                return spotsArray;
    
              })
            )
            .subscribe(spots => {
              this.loader.hide();
              this.spotsArray = spots;
    
            }, (error) => {
              this.loader.hide();
              this.errorService.handleError(error, true);
            });
        }

  getAllSpots(){
    console.log("entered");
    
          // if (!this.clientId || !this.selectedLocation) {
          //   return;
          // }
    
          // console.log('calling:::::::: ');
          this.loader.show();

          let fleetStatus = null;
          if(this.selectedTrailerAssetStatus != "ALL"){
           fleetStatus = this.selectedTrailerAssetStatus;
          }
          this.manageSpotsServie.viewSpots(this.query, this.clientId, null, null, fleetStatus, null, this.selectedLastUpdated)
            .pipe(
              map(res => {
                let spotsArray = [];
                for (let spot of res.list) {
                  let inTransit = [];
                 
                  let obj = {
                    ...spot,
                    fleetIdIfExist: spot.fleet != null ? { "unitNumber" : spot.fleet.unitNumber, "fleetId": spot.fleet.fleetId} : '',
                    fleetStatusIfExist: spot.fleet != null ? spot.fleet.fleetStatus : '',
                  }
                  spotsArray.push(obj);
                }
                this.totalRecords = res.totalElements;
                return spotsArray;
    
              })
            )
            .subscribe(spots => {
              this.loader.hide();
              this.spotsArray = spots;
    
            }, (error) => {
              this.loader.hide();
              this.errorService.handleError(error, true);
            });
        }
  
  
  filterTrailerStatus(event){
    
    this.query.page = 0;
    if(event.value==null){
      this.selectedTrailerAssetStatus="ALL";
    }
    else{
      this.selectedTrailerAssetStatus = event.value;
    }

     if (this.selectedLocations[0] == "ALL") {
        this.getAllSpots();
      } else {
        this.getSpots();
      }

  }
  
  loadLocations(event: AutoCompleteSelectEvent) {
    if (event?.value.clientId) {
      this.clientId = event.value.clientId;
      this.selectedLocations = [];
      this.locationList =[];
      this.spotsArray = [];
      this.query.page = 0;
      this.totalRecords = 0;
    }
      this.loading = true;
      this.subscription.add(
        this.manageLocationsService.viewLocations(this.query, this.clientId, null, '').subscribe(
          response => {
            this.locationList = response.list || [];
            // this.totalRecords = response.totalElements;
            this.loading = false;
            

            this.locationList.unshift({
              locationId: 'ALL',
              locationName: 'All Locations'
            });

            if (this.locationIds?.length && event == null) {
              this.selectedLocations = this.locationIds.filter(id =>
                this.locationList.some(loc => loc.locationId === id)
              );
            }
            else{
              this.selectedLocations = [];
            }

          },
          error => {
            this.loading = false;
            this.errorService.handleError(error, true);
          }
        )
      );
    
  
  }
  
  filterFleetsByLocationIds(event) {
    const selected = event.value;

    if (selected.length > 1) {
      if(selected[selected.length - 1] == "ALL"){
          this.selectedLocations = [selected[selected.length - 1]];
      }else{
        if (selected.includes('ALL')){
          this.selectedLocations = selected.filter(loc => loc !== 'ALL');
        }else{
          this.selectedLocations = selected;
        }
      }
    }else{
        this.selectedLocations = selected;
    }
    this.locationIds = this.selectedLocations;
    
    this.query.page = 0;
    
    if (this.selectedLocations.length > 0) {
      if (this.selectedLocations[0] == "ALL") {
        this.getAllSpots();
      } else {
        this.getSpots();
      }
    }else{
      this.spotsArray = [];
      this.totalRecords = 0;
    }
  }

  filterByLastDate(){
     if (this.selectedLocations[0] == "ALL") {
        this.getAllSpots();
      } else {
        this.getSpots();
      }
  } 
      
  
  paginate(event) {
    this.query.page = event.page;
    if (this.selectedLocations[0] == "ALL") {
      this.getAllSpots();
    } else {
      this.getSpots();
    }

  }

  getClientList(event?, clientId?) {
    if (event) {
      this.clientName = event.query;
    }
    this.loader.show();
    this.manageClientService.viewClients(this.query, this.clientName, clientId).subscribe(response => {
      this.clientList = response.list;
      if (clientId) {
        this.selectedClient = this.clientList[0];
      }

      this.loader.hide();
      if (this.isSupervisorOrClient) {
        this.clientId = this.clientList[0].clientId;
        this.selectedClient = this.clientList[0];
      }
      // if (this.isITOrAdmin) {
      //   this.clientList.unshift({
      //     clientId: 'ALL',
      //     clientName: 'All Clients'
      //   });
      //   if(this.clientId=="ALL"){
      //     this.selectedClient = this.clientList[0];
      //   }
      // }
    }, (error) => {
      this.loader.hide();
      this.errorService.handleError(error, true);
    })
  }


  exportExcel() {
    
    if(this.selectedClient.clientId == null || this.selectedClient.clientId == undefined) {
       this.clientErrorModal = true;
       return;
    }
      const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/trailerAudit/export/excel`);
      url.searchParams.append("isActive", "true");
      url.searchParams.append("access_token", this.accessToken); 
      if (this.selectedClient) {
        url.searchParams.append("clientId", this.selectedClient.clientId);
      }
      if(this.selectedLocations && this.selectedLocations.length > 0 && this.selectedLocations[0] != "ALL") {
        url.searchParams.append("locationIds", this.selectedLocations.join(','));
      }
      if(this.selectedTrailerAssetStatus && this.selectedTrailerAssetStatus != "ALL") {
        url.searchParams.append("fleetStatus", this.selectedTrailerAssetStatus);
      }
     window.open(url.toString(), '_blank');
  }

  exportPdf() {

     if(this.selectedClient.clientId == null || this.selectedClient.clientId == undefined) {
       this.clientErrorModal = true;
       return;
     }
     const url = new URL(`${APP_CONSTANTS.BASE_API_URL}/v1/trailerAudit/export/pdf`);
     url.searchParams.append("isActive", "true");
     url.searchParams.append("access_token", this.accessToken);
     if (this.selectedClient) {
       url.searchParams.append("clientId", this.selectedClient.clientId);
     }
     if(this.selectedLocations && this.selectedLocations.length > 0 && this.selectedLocations[0] != "ALL") {
       url.searchParams.append("locationIds", this.selectedLocations.join(','));
     }
     if(this.selectedTrailerAssetStatus && this.selectedTrailerAssetStatus != "ALL") {
       url.searchParams.append("fleetStatus", this.selectedTrailerAssetStatus);
     }
     window.open(url.toString(), '_blank');
  }

  clearUserFilter(event) {

    this.assignedToUser = '';
    this.selectedUser = '';

  }

  clearClientFilter(event) {

    this.clientId = '';
    this.selectedClient = '';
    this.selectedLocations = [];
  }

  closeAlert() {

    this.clientErrorModal = false;
  }
}
