<div class="grid">
    <div class="col-12">
        <div class="card card-w-title">
            <p-breadcrumb [model]="breadcrumbItems" [home]="{icon: 'pi pi-home',routerLink:'../'}"></p-breadcrumb>
        </div>
    </div>

    <div class="col-12">
        <div class="card">

            <p-toast></p-toast>
            <p-toolbar styleClass="mb-4">
                <div class="w-full">
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <div class="my-2">
                            <h5 class="m-0">Trailer Audit Report</h5>
                        </div>

                        <div>

                            <p-splitButton label="Export" [model]="items" (onClick)="exportExcel()"
                                styleClass="p-button-raised p-button-help mr-2 "></p-splitButton>

                        </div>
                    </div>

                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center mrgt-30">

                        <span>

                            <p-autocomplete placeholder="Filter By Client" [suggestions]="clientList"
                                (onSelect)="loadLocations($event)" dataKey="clientId"
                                (completeMethod)="getClientList($event)" [dropdown]="true" [(ngModel)]="selectedClient"
                                (onClear)="clearClientFilter($event)" class="mrgr-10" field="clientName">
                                <ng-template let-client pTemplate="item">
                                    <div>{{client.clientName}}</div>
                                </ng-template>
                            </p-autocomplete>

                            <p-multiSelect [options]="locationList" [(ngModel)]="selectedLocations"
                                placeholder="Filter By Locations" optionLabel="locationName" optionValue="locationId"
                                class="mrgr-10" [filter]="true" (onChange)="filterFleetsByLocationIds($event)">
                            </p-multiSelect>

                            <p-select class="ml-2" [options]="trailerStatus" placeholder="Select Trailer Status"
                                optionLabel="name" optionValue="code" (onChange)="filterTrailerStatus($event)"
                                [(ngModel)]="selectedTrailerAssetStatus"></p-select>

                            <!-- <span class="ml-2 font-medium">Last Updated : <input pInputText type="date" [(ngModel)]="selectedLastUpdated" name="lastUpdated"
                                        placeholder="Select Date" (change)="filterByLastDate()" /></span> -->

                        </span>


                        <!-- <span class="block mt-2 md:mt-0 p-input-icon-left">
                            <span class="mr-2 font-medium">From Date : <input pInputText type="date"
                                    [(ngModel)]="fromDate"  name="fromDate" (change)="filterJobsByDate()"
                                    placeholder="Select Date" /></span>
                            <span class="mx-2 font-medium">To Date : <input pInputText type="date" [(ngModel)]="toDate"
                                     name="toDate" placeholder="Select Date"  (change)="filterJobsByDate()"/></span>
                        </span> -->


                    </div>
                </div>
            </p-toolbar>

            <p-table showGridlines #dt [value]="spotsArray"
                [globalFilterFields]="['spotName','unitNumber','fleetStatus','notes']" [loading]="loading"
                styleClass="p-datatable-gridlines p-datatable-striped p-datatable-sm" [responsive]="true" [columns]=""
                [rows]="10" [rowHover]="true" dataKey="id">

                <ng-template #header>
                    <tr>

                        <th pSortableColumn="spotName">Dock</th>
                        <th pSortableColumn="unitNumber">Trailer</th>
                        <th pSortableColumn="status">Status</th>
                        <th pSortableColumn="notes">Notes</th>
                        <th pSortableColumn="lastModifiedDate">Last Updated</th>

                    </tr>
                </ng-template>
                <ng-template #body let-spot>
                    <tr>


                        <td>
                            {{spot.spotName}}
                        </td>

                        <td>

                            {{spot.fleet?.unitNumber}}

                        </td>
                        <td>
                            <!-- {{spot.fleet.fleetStatus}} -->
                            <!-- {{ spot.fleet?.fleetStatus == "FULL"? "Loaded" : "Empty" }} -->
                            {{spot?.fleet?.fleetStatus && spot?.fleet?.fleetStatus=='FULL' ? 'LOADED' : ''}}
                            {{spot?.fleet?.fleetStatus && spot?.fleet?.fleetStatus=='EMPTY' ? spot?.fleet?.fleetStatus : ''}}
                        </td>

                        <td>
                            {{ spot.notes }}
                        </td>

                        <td>
                            {{ spot.audit.lastModifiedDate }}
                        </td>

                    </tr>
                </ng-template>
                <ng-template #emptymessage>
                    <tr>
                        <td colspan="5">Data not found.</td>
                    </tr>
                </ng-template>
            </p-table>
            <p-paginator [rows]="50" [showCurrentPageReport]="true"
                currentPageReportTemplate="Showing {first} to {last} of {totalRecords}" [totalRecords]="totalRecords"
                (onPageChange)="paginate($event)" #paginator></p-paginator>

        </div>
    </div>
</div>



<p-dialog [(visible)]="clientErrorModal" [style]="{width: '450px', minHeight:'100px'}" header="Invalid Filter"
    [modal]="true" class="p-fluid">
    <ng-template pTemplate="content">
        <p>Select a client to export data</p>
    </ng-template>
    <ng-template pTemplate="footer">
        <button pButton pRipple label="Ok" icon="pi pi-check" class="p-button-text" (click)="closeAlert()"
            type="button"></button>
    </ng-template>
</p-dialog>