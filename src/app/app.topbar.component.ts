import { Component, OnInit, HostListener } from '@angular/core';
import { AppMainComponent } from './app.main.component';
import { MenuItem } from 'primeng/api';
import { AccountService } from './security/account.service';
import { AppLoaderService } from './app-loader/service/app-loader.service';
import { TokenService } from './security/token.service';
import { Router } from '@angular/router';

@Component({
    selector: 'app-topbar',
    templateUrl: './app.topbar.component.html',
    styleUrls: ['./app.topbar.component.scss'],
    standalone: false
})
export class AppTopBarComponent implements OnInit {

    items: MenuItem[];
    username: string;
    userRoles: any;
    showDropdown: boolean = false;

    constructor(
        public appMain: AppMainComponent,
        private accountService: AccountService,
        private loader: AppLoaderService,
        private tokenService: TokenService,
        private router: Router
    ) { }

    ngOnInit() {
        this.username = this.tokenService.getUserFullName();
        this.userRoles = this.tokenService.getUserRoles();
    }

    toggleDropdown() {
        this.showDropdown = !this.showDropdown;
    }

    logout() {
        this.loader.show();
        this.accountService.logout().subscribe(() => {
            this.tokenService.removeToken();
            this.router.navigateByUrl('');
            this.loader.hide();
            localStorage.clear();
        }, () => {
            this.tokenService.removeToken();
            this.router.navigateByUrl('');
            this.loader.hide();
        });
    }

    @HostListener('document:click', ['$event'])
    handleClickOutside(event: Event) {
        const dropdown = document.querySelector('.dropdown-card');
        const profileButton = document.querySelector('.user-profile');

        if (
            this.showDropdown &&
            dropdown &&
            !dropdown.contains(event.target as Node) &&
            profileButton &&
            !profileButton.contains(event.target as Node)
        ) {
            this.showDropdown = false;
        }
    }
}
