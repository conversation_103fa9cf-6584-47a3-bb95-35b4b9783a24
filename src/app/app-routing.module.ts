import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { DashboardComponent } from './components/dashboard-new/dashboard.component';
import { EmptyComponent } from './components/empty/empty.component';
import { AppMainComponent } from './app.main.component';
import { ManageClientsComponent } from './components/manage-clients/manage-clients.component';
import { ManageLocationsComponent } from './components/manage-locations/manage-locations.component';
import { ManageFleetsComponent } from './components/manage-fleets/manage-fleets.component';
import { ManageUsersComponent } from './components/manage-users/manage-users.component';
import { ManageRolesComponent } from './components/manage-roles/manage-roles.component';
import { AddLocationComponent } from './components/add-location/add-location.component';
import { AddClientComponent } from './components/add-client/add-client.component';
import { ManageSpotsComponent } from './components/manage-spots/manage-spots.component';
import { AddFleetComponent } from './components/add-fleet/add-fleet.component';
import { AddUserComponent } from './components/add-user/add-user.component';
import { LoginComponent } from './components/login/login.component';
import { ClientDetailsComponent } from './components/client-details/client-details.component';
import { AddJobComponent } from './components/add-job/add-job.component';
import { ManageJobsComponent } from './components/manage-jobs/manage-jobs.component';
import { ManageMessagesComponent } from './components/manage-messages/manage-messages.component';
import { ManageEntryExitComponent } from './components/manage-entry-exit/manage-entry-exit.component';
import { authGuard } from './security/auth.guard';
import { CanvasToolComponent } from './canvas-tool/canvas-tool.component';
import { PreviewCanvasMapComponent } from './preview-canvas-map/preview-canvas-map.component';
import { ForgotPasswordComponent } from './components/forgot-password/forgot-password.component';
import { NewUserComponent } from './components/new-user/new-user.component';
import { ManageTrailerAuditComponent } from './components/manage-trailer-audit/manage-trailer-audit.component';
import { AddTrailerAuditComponent } from './components/add-trailer-audit/add-trailer-audit.component';
import { TermsComponent } from './components/terms/terms.component';
import { UserTermsComponent } from './components/user-terms/user-terms.component';
import { PrivacyPolicyComponent } from './components/privacy-policy/privacy-policy.component';
import { UserprivacyPolicyComponent } from './components/userprivacy-policy/userprivacy-policy.component';
import { RedirectPageComponent } from './components/redirect-page/redirect-page.component';
import { OverviewComponent } from './components/overview/overview.component';
import { ManageMobileAppVersionComponent } from './components/manage-mobile-app-version/manage-mobile-app-version.component';
import { ManageUserAvailabilityComponent } from './components/manage-user-availability/manage-user-availability.component';
import { ManageOvertimeUsersComponent } from './components/manage-overtime-users/manage-overtime-users.component';
import { ManageTrailerTrackingComponent } from './components/manage-trailer-tracking/manage-trailer-tracking.component';
import { ManageReportComponent } from './components/manage-report/manage-report.component';
import { ManageReportMovesByDriverComponent } from './components/manage-report-moves-by-driver/manage-report-moves-by-driver.component';
import { ManageReportMovesBySpotterComponent } from './components/manage-report-moves-by-spotter/manage-report-moves-by-spotter.component';
import { ManageReportTrailerReportLoadstatusComponent } from './components/manage-report-trailer-report-loadstatus/manage-report-trailer-report-loadstatus.component';
import { ManageReportTrailerReportLocationComponent } from './components/manage-report-trailer-report-location/manage-report-trailer-report-location.component';
import { ManageReportTrailerReportEntryExitComponent } from './components/manage-report-trailer-report-entry-exit/manage-report-trailer-report-entry-exit.component';
import { ManageReportAssetInventoryComponent } from './components/manage-report-asset-inventory/manage-report-asset-inventory.component';
import { ManageTrailerAuditNewComponent } from './components/manage-trailer-audit-new/manage-trailer-audit-new.component';
import { ManageReportTrailerAuditComponent } from './components/manage-report-trailer-audit/manage-report-trailer-audit.component';
import { ManageReportTrailerHistoryComponent } from './components/manage-report-trailer-history/manage-report-trailer-history.component';
import { ManageContactUsComponent } from './components/manage-contact-us/manage-contact-us.component';
// import { ManageTrailerTrackingComponent } from './components/manage-trailer-tracking/manage-trailer-tracking.component';
@NgModule({
    imports: [
        RouterModule.forRoot([
            {
                path: '', redirectTo : 'login', pathMatch :'full',
            },
            { path:'login', component:LoginComponent},
            { path:'forgot-password', component:ForgotPasswordComponent},
            {path:'new-user', component:NewUserComponent},
            {path:'terms', component:TermsComponent},
            {path:'privacy-policy', component:PrivacyPolicyComponent},
            {path:'saml2-success', component:RedirectPageComponent},
            {
                path: 'main', component: AppMainComponent, canActivate:[authGuard],
                children: [
                    {path: '', component: DashboardComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    // {path: 'dashboard', component: DashboardComponent1, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    // {path: 'pages/empty', component: EmptyComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR"] }},
                    {path: 'manage-clients', component: ManageClientsComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path: 'manage-locations', component: ManageLocationsComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path: 'manage-fleets', component: ManageFleetsComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path: 'manage-users', component: ManageUsersComponent,canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER","ROLE_CLIENT"] }},
                    {path: 'manage-roles', component: ManageRolesComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN"] }},
                    {path: 'manage-spots', component: ManageSpotsComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER","ROLE_CLIENT"] }},
                    {path: 'add-client', component: AddClientComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN"] }},
                    {path: 'add-location', component: AddLocationComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN"] }},
                    {path: 'add-fleet', component: AddFleetComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_GUARD","ROLE_SPOTTER", "ROLE_CLIENT"] }},
                    {path: 'add-user', component: AddUserComponent,canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path: 'client-details', component: ClientDetailsComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path: 'add-job', component: AddJobComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER","ROLE_CLIENT"] }},
                    {path: 'manage-jobs', component: ManageJobsComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path: 'manage-messages', component: ManageMessagesComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path: 'manage-entry-exit', component: ManageEntryExitComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_GUARD"] }},
                    {path: 'manage-trailer-audit-old', component: ManageTrailerAuditComponent, canActivate:[authGuard],data: { roles: ["ROLE_CLIENT","ROLE_SUPERVISOR"] }},
                    {path: 'add-trailer-audit', component: AddTrailerAuditComponent, canActivate:[authGuard],data: { roles: ["ROLE_SUPERVISOR"] }},
                    {path:'user-terms',component: UserTermsComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path:'user-privacy-policy',component: UserprivacyPolicyComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                    {path:'overview',component: OverviewComponent, canActivate:[authGuard],data: { roles: ["ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR"] }},
                    {path:'manage-mobile-app',component: ManageMobileAppVersionComponent, canActivate:[authGuard],data: { roles: ["ROLE_ADMIN","ROLE_IT"] }},
                    {path:'manage-user-availability',component: ManageUserAvailabilityComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR"] }},
                    {path:'manage-overtime-users',component: ManageOvertimeUsersComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR"] }},
                    {path:'manage-trailer-tracking',component: ManageTrailerTrackingComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_GUARD","ROLE_CLIENT"] }},
                    {path:'manage-report',component: ManageReportComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-report-moves-by-driver',component: ManageReportMovesByDriverComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-report-moves-by-spotter',component: ManageReportMovesBySpotterComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-report-trailer-report-loadstatus',component: ManageReportTrailerReportLoadstatusComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-report-trailer-report-location',component: ManageReportTrailerReportLocationComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-report-trailer-report-entryexit',component: ManageReportTrailerReportEntryExitComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-trailer-audit', component: ManageTrailerAuditNewComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR"] }},
                    {path:'manage-report-asset-inventory',component: ManageReportAssetInventoryComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-report-trailer-audit',component: ManageReportTrailerAuditComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-report-trailer-history',component: ManageReportTrailerHistoryComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_SUPERVISOR","ROLE_CLIENT"] }},
                    {path:'manage-contact-us',component: ManageContactUsComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT","ROLE_ADMIN","ROLE_CLIENT","ROLE_SUPERVISOR","ROLE_DRIVER","ROLE_GUARD","ROLE_SPOTTER"] }},
                ]
            },
            {path: 'canvas-tool', component:CanvasToolComponent, canActivate:[authGuard],data: { roles: ["ROLE_IT"] }},
            {path: 'preview-map', component:PreviewCanvasMapComponent, canActivate:[authGuard]},
            {path: '**', redirectTo: 'main/pages/empty'},
        ],
        {scrollPositionRestoration: 'enabled'})
    ],
    exports: [RouterModule]
})
export class AppRoutingModule {
}
