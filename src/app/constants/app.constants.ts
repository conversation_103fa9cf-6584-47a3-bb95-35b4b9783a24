import { environment } from '../../environments/environment';

/**
 * Constants object which can hold app level constants
 */
export const APP_CONSTANTS = {
  BASE_API_URL: environment.API_URL,
  SOCKET_URL: environment.SOCKET_URL,
  VERSION: environment.VERSION,
  TOKEN_TYPES: {
    BEARER: 'bearer'
  },
  WEB_STORAGE_TYPE: {
    LOCAL: 'local',
    SESSION: 'session'
  },
  USER_ROLES: {
    SUPER_ADMIN: 'ROLE_SUPER_ADMIN',
    ROLE_ADMIN: 'ROLE_ADMIN',
    ROLE_IT: 'ROLE_IT',
    ROLE_DRIVER: 'ROLE_DRIVER',
    ROLE_CLIENT: 'ROLE_CLIENT',
    ROLE_SPOTTER: 'ROLE_SPOTTER',
    ROLE_SUPERVISOR: 'ROLE_SUPERVISOR',
    ROLE_GUARD: 'ROLE_GUARD'
  },
  PATTERNS: {
    EMAIL: /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  },
  FILE_TYPE: {
    IMAGE: 'IMAGE',
    VIDEO: 'VIDEO',
    DOCUMENT: 'DOCUMENT'
  }
};

export const USER_CONSTANTS = {
  USER_ROLE_FILTERS: {
    admin: 'ADMIN',
    client: 'CLIENT',
    supervisor: 'SUPERVISOR',
    driver: 'DRIVER',
    guard: 'GUARD',
    yardSpotter: 'SPOTTER',
    it: 'IT'
  },
  USER_ROLE_UUIDS: {
    admin: 'a2c227ae-a610-4ba8-88c7-2ae61f9fd9c4',
    client: 'f1023da7-98f3-43c0-abbf-c9f07c90f4fe',
    supervisor: 'ace1b5c7-a19f-4857-be94-aa68de1c2142',
    driver: 'b09614be-3973-4f89-b237-ff3506ccdd70',
    guard: '20a38eb5-16f9-4d50-8c98-1d4ecbb3f333',
    yardSpotter: '4c8d3ec7-2dc6-46c3-afb0-c266a3448f26',
    it: '095f6cc9-2a6f-4963-9de0-f52441b45330'
  },
  USER_ROLE_NAMES: {
    admin: 'Admin',
    client: 'Client',
    supervisor: 'Supervisor',
    driver: 'Driver',
    guard: 'Guard',
    yardSpotter: 'Yard Spotter',
    it: 'IT'
  },
  TIME_ZONES: [
    'America/Adak',
    'America/Anchorage',
    'America/Boise',
    'America/Chicago',
    'America/Denver',
    'America/Detroit',
    'America/Fort_Wayne',
    'America/Indiana/Indianapolis',
    'America/Indiana/Knox',
    'America/Indiana/Marengo',
    'America/Indiana/Petersburg',
    'America/Indiana/Tell_City',
    'America/Indiana/Vevay',
    'America/Indiana/Vincennes',
    'America/Indiana/Winamac',
    'America/Indianapolis',
    'America/Juneau',
    'America/Kentucky/Louisville',
    'America/Kentucky/Monticello',
    'America/Knox_IN',
    'America/Los_Angeles',
    'America/Louisville',
    'America/Menominee',
    'America/Metlakatla',
    'America/New_York',
    'America/Nome',
    'America/North_Dakota/Beulah',
    'America/North_Dakota/Center',
    'America/North_Dakota/New_Salem',
    'America/Phoenix',
    'America/Shiprock',
    'America/Sitka',
    'America/Yakutat',
    'Navajo',
    'Pacific/Honolulu',
    'Pacific/Johnston',
    'US/Alaska',
    'US/Aleutian',
    'US/Arizona',
    'US/Central',
    'US/East-Indiana',
    'US/Eastern',
    'US/Hawaii',
    'US/Indiana-Starke',
    'US/Michigan',
    'US/Mountain',
    'US/Pacific'
  ]
}
