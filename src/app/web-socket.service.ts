// import { Injectable } from '@angular/core';
// import SockJs from "sockjs-client";
// import { Client } from '@stomp/stompjs';
// import { APP_CONSTANTS } from './constants/app.constants';

// @Injectable({
//   providedIn: 'root'
// })
// export class WebSocketService {

//   constructor() { }

//   public connect() {
//     let url = APP_CONSTANTS.BASE_API_URL + "/socket";
//     let socket = new SockJs(url);

//     let stompClient = Stomp.over(socket);

//     return stompClient;
// }
// }

import { Injectable } from '@angular/core';
import SockJs from "sockjs-client";
import { Client, IMessage, IFrame } from '@stomp/stompjs';
import { APP_CONSTANTS } from './constants/app.constants';

@Injectable({
  providedIn: 'root'
})
export class WebSocketService {
  private stompClient: Client;

  constructor() {
    this.stompClient = new Client({
      webSocketFactory: () => new SockJs(APP_CONSTANTS.BASE_API_URL + '/socket'),
      reconnectDelay: 5000, // auto reconnect in 5 seconds
      debug: (str) => console.log('[STOMP]', str),
    });
  }

  public connect(onConnectCallback?: () => void) {
    this.stompClient.onConnect = (frame: IFrame) => {
      console.log('Connected: ', frame);
      if (onConnectCallback) onConnectCallback();
    };

    this.stompClient.onStompError = (frame) => {
      console.error('STOMP Error: ', frame);
    };

    this.stompClient.activate(); // Start the connection
  }

  public disconnect() {
    if (this.stompClient && this.stompClient.active) {
      this.stompClient.deactivate();
      console.log('Disconnected from WebSocket');
    }
  }

  public subscribe(topic: string, callback: (message: IMessage) => void) {
    this.stompClient.subscribe(topic, callback);
  }

  public send(destination: string, body: string) {
    this.stompClient.publish({ destination, body });
  }
}

