:host ::ng-deep {

    .layout-sidebar {
        top: 4rem !important;
        left: 0rem !important;
        border-radius: 0px !important;
        //height: calc(100vh - 5rem) !important;
        height: calc(120vh - 100px) !important;
        background-color: #363636 !important;
        //border-top: 1px solid #e0e0e0;
        padding: 0px;
        border-right: 1px solid #474747;
    }

    .side-layout-topbar-left {
        align-items: center;
        display: inline-flex;
        width: 100%;
        border-bottom: 1px solid #474747;
        padding: 15px 20px;
        height: 59px;

        img {
            height: 2rem;
            margin-right: 25px;
        }
    }

    .layout-topbar .layout-menu-button {
        margin-left: 0px !important;
    }

    .layout-menu li a {
        color: white;
    }

    .layout-menu li a.router-link-exact-active {
        font-weight: 600;
        // color: var(--primary-color) !important;
        // background: white;
        background-color: rgb(71, 69, 69);
    }

    .layout-menu li a:hover {
        //  color: var(--primary-color) !important;
        background-color: rgb(71, 69, 69);
    }

    .layout-menu li ul li {
        // margin-top: 0.75em;
        margin-top: 0px;
    }

    .layout-menu {
        margin-bottom: 20px;
        font-size: 14px;
        padding: 8px;
    }

    .layout-logout-button i {
        font-size: 1.5rem;
        color: white;
    }

    .logout-section:hover {
        background-color: rgb(71, 69, 69) !important;
        // padding: 5px;
        border-radius: 10px;
    }

    .logout-section {
        //  margin-top: 100px;
        padding: 15px;
    }

    .layout-menu-container {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        color: white;
        padding-top: 0px;
        // margin-top: 10px;
    }

    .logout-text {
        color: white;
        margin-left: 5px;
        ;
    }

    .user-details {
        display: flex;
        flex-direction: column;
    }
}