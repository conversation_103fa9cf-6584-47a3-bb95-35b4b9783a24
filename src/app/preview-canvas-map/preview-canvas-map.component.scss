.canvas{
    box-shadow: 0px 0px 3px 3px #ddd;
    background-color: #f5f5f5;
    z-index:3;
}

#overlay{
    width:200px;height:200px;background:white;
    z-index: 4;
    position:absolute;
    box-shadow: 0px 0px 5px 3px #ddd;
}
.uidnameShow{
    background-color: green; padding: 5px; padding-left: 8px; padding-right: 8px; float: right; color: white; text-align: center; font-weight: 600;box-shadow:0px 0px 5px 3px #ddd;
}
.overlayDetails{
    font-size: 13px;
    font-weight: 600;
    padding-left:12px;
    margin-top:5px;
}
#Test
{
    position: absolute;
    visibility: hidden;
    height: auto;
    width: auto;
    white-space: nowrap; /* Thanks to <PERSON> comment */
}
.incRedBtn{
    width: 25px;
    height: 25px;
    border:1px solid #eee;
    text-align: center;
    font-size: 18px;
    cursor: pointer;
    background-color: #eee;
}
.incRedVal{
    width: 35px;
    height: 25px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;


    border:1px solid #eee;
}

.canvasContainer{
    position:relative;overflow:hidden;
    -ms-overflow-style: none;  
    scrollbar-width: none;  
}
.canvasContainer::-webkit-scrollbar{
    display: none;
}