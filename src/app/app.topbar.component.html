<div class="layout-topbar">
    <!-- Left Section: Logo & Menu -->
    <div class="layout-topbar-left">
        <a class="layout-topbar-logo" routerLink="/main">
            <img src="../../../assets/logo/spoton_vector.svg" alt="logo">
        </a>

        <!-- <PERSON>u Toggle <PERSON> (Now positioned next to tabs) -->
        <a class="p-link layout-menu-button layout-topbar-button" href="#" (click)="appMain.toggleMenu($event)">
            <i class="pi pi-bars"></i>
        </a>
    </div>

    <!-- Right Section (User Profile) -->
    <div class="layout-topbar-right">
        <div class="user-profile" (click)="toggleDropdown()">
            <div class="avatar">{{ username.charAt(0) }}{{ username.charAt(1) }}</div>
        </div>

        <!-- Dropdown Menu -->
        <div class="dropdown-card" *ngIf="showDropdown">
            <div class="user-info">
                <div class="avatar-large">{{ username.charAt(0) }}{{ username.charAt(1) }}</div>
                <div class="user-details">
                    <p class="name">{{ username }}</p>
                    <p class="email">{{ userEmail }}</p>
                </div>
            </div>

            <p class="mb-0 mt-3 mx-2">
                <span *ngFor="let role of userRoles;let i=index">
                    <span *ngIf="role == 'ROLE_ADMIN'"> ADMIN</span>
                    <span *ngIf="role == 'ROLE_IT'"> IT</span>
                    <span *ngIf="role == 'ROLE_DRIVER'"> DRIVER</span>
                    <span *ngIf="role == 'ROLE_CLIENT'"> CLIENT</span>
                    <span *ngIf="role == 'ROLE_SPOTTER'"> YARD SPOTTER</span>
                    <span *ngIf="role == 'ROLE_SUPERVISOR'"> SUPERVISOR</span>
                    <span *ngIf="role == 'ROLE_GUARD'"> GUARD</span> {{i < userRoles.length - 1 ? ',' : ''}}
                </span>

            </p>

            <hr class="divider" />

            <button class="logout-button" (click)="logout()">
                <mat-icon>logout</mat-icon> Log Out
            </button>
        </div>
    </div>
</div>
