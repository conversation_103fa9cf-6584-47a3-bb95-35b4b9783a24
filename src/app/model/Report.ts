export interface Report{
   
    id:number
    name:string;
    description:string;
    type:ReportType;   
}

export enum ReportType{

    Moves = 'Moves',
    TrailerReport = 'Trailer Report'
}

export interface ReportTypes{
    
    type:ReportType,
    number:number
}

export enum EntryExitType{

    Entry = 'ENTRY',
    Exit = 'EXIT'
}

export interface EntryExit{

    code:string,
    value:string
}